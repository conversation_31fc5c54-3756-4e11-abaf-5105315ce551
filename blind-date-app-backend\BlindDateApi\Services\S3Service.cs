using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;

namespace BlindDateApi.Services
{
    public class S3Service : IS3Service
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<S3Service> _logger;
        private readonly IAmazonS3 _s3Client;
        private readonly string _bucketName;
        
        public S3Service(IConfiguration configuration, ILogger<S3Service> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            // Get S3 configuration from appsettings.json
            var awsAccessKey = _configuration["AWS:AccessKey"];
            var awsSecretKey = _configuration["AWS:SecretKey"];
            var awsRegion = _configuration["AWS:Region"];
            _bucketName = _configuration["AWS:BucketName"];
            
            // Create S3 client
            _s3Client = new AmazonS3Client(
                awsAccessKey,
                awsS<PERSON><PERSON><PERSON>ey,
                RegionEndpoint.GetBySystemName(awsRegion)
            );
        }
        
        public async Task<string> UploadFileAsync(IFormFile file, int userId)
        {
            try
            {
                // Generate a unique file name
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = $"profile-pictures/{userId}/{Guid.NewGuid()}{fileExtension}";
                
                // Upload file to S3
                using (var memoryStream = new MemoryStream())
                {
                    await file.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;
                    
                    var uploadRequest = new TransferUtilityUploadRequest
                    {
                        InputStream = memoryStream,
                        Key = fileName,
                        BucketName = _bucketName,
                        ContentType = file.ContentType,
                        CannedACL = S3CannedACL.PublicRead // Make the file publicly accessible
                    };
                    
                    var transferUtility = new TransferUtility(_s3Client);
                    await transferUtility.UploadAsync(uploadRequest);
                    
                    // Return the URL of the uploaded file
                    return $"https://{_bucketName}.s3.amazonaws.com/{fileName}";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to S3 for user {UserId}", userId);
                throw;
            }
        }
        
        public async Task<bool> DeleteFileAsync(string fileUrl)
        {
            try
            {
                // Extract the file key from the URL
                var fileKey = fileUrl.Replace($"https://{_bucketName}.s3.amazonaws.com/", "");
                
                // Delete the file from S3
                var deleteRequest = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = fileKey
                };
                
                await _s3Client.DeleteObjectAsync(deleteRequest);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from S3: {FileUrl}", fileUrl);
                return false;
            }
        }
        
        public async Task<string> GeneratePreSignedUrlAsync(string fileKey, int expiryMinutes = 60)
        {
            try
            {
                // Generate a pre-signed URL for the file
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = fileKey,
                    Expires = DateTime.UtcNow.AddMinutes(expiryMinutes)
                };
                
                return _s3Client.GetPreSignedURL(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pre-signed URL for file: {FileKey}", fileKey);
                throw;
            }
        }
    }
}
