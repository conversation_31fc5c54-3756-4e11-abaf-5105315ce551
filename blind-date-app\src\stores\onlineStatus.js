import { defineStore } from 'pinia'
import { onlineStatusService } from '../services/api'

export const useOnlineStatusStore = defineStore('onlineStatus', {
  state: () => ({
    pingInterval: null,
    pingIntervalTime: 60000, // 1 minute
    isOnline: true,
    error: null
  }),

  actions: {
    startPinging() {
      // Clear any existing interval
      this.stopPinging()
      
      // Send initial ping
      this.ping()
      
      // Set up interval for regular pings
      this.pingInterval = setInterval(() => {
        this.ping()
      }, this.pingIntervalTime)
    },
    
    stopPinging() {
      if (this.pingInterval) {
        clearInterval(this.pingInterval)
        this.pingInterval = null
      }
    },
    
    async ping() {
      try {
        await onlineStatusService.ping()
        this.isOnline = true
        this.error = null
      } catch (error) {
        this.error = error.message || 'Failed to ping server'
        console.error('Error pinging server:', error)
      }
    },
    
    async setOffline() {
      try {
        this.stopPinging()
        await onlineStatusService.setOffline()
        this.isOnline = false
      } catch (error) {
        console.error('Error setting offline status:', error)
      }
    },
    
    async getUserStatus(userId) {
      try {
        const response = await onlineStatusService.getUserStatus(userId)
        return response.data.isOnline
      } catch (error) {
        console.error('Error getting user status:', error)
        return false
      }
    }
  }
})
