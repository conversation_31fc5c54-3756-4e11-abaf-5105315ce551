import { defineStore } from 'pinia'
import { useAuthStore } from './auth'
import { subscriptionService } from '../services/api'
import { getStripe, createPaymentIntent, confirmPayment, cancelSubscription } from '../services/stripe'

export const useSubscriptionStore = defineStore('subscription', {
  state: () => ({
    plans: [],
    currentPlan: null,
    isLoading: false,
    error: null
  }),

  actions: {
    async fetchPlans() {
      this.isLoading = true
      this.error = null

      try {
        // First check if we already have plans loaded
        if (this.plans.length > 0) {
          return this.plans
        }

        // Fetch plans from API
        const response = await subscriptionService.getSubscriptions()

        if (response && response.data) {
          // Transform API data to match our frontend format
          this.plans = response.data.map(plan => ({
            id: plan.id.toString(),
            name: plan.name,
            price: plan.price,
            features: [
              plan.chatsPerDay === -1
                ? 'Unlimited chats per day'
                : `Limited chats per day (${plan.chatsPerDay})`,
              `${plan.maxInterests} interests selection`,
              `${plan.maxPictures} pictures`,
              this.formatPictureRevealCondition(plan.pictureRevealCondition)
            ],
            // Keep the original API data for reference
            apiData: plan,
            isCurrent: plan.isCurrent
          }))

          // Set current plan if one is marked as current
          const currentPlan = this.plans.find(p => p.apiData.isCurrent)
          if (currentPlan) {
            this.currentPlan = currentPlan
          }

          return this.plans
        }

        throw new Error('Invalid response format from API')
      } catch (error) {
        this.error = error.message || 'Failed to fetch subscription plans'

        // Fallback to default plans if API fails
        this.plans = this.getDefaultPlans()
        return this.plans
      } finally {
        this.isLoading = false
      }
    },

    formatPictureRevealCondition(condition) {
      switch (condition) {
        case 'Match': return 'Picture reveal if match'
        case '10 Messages': return 'Picture reveal after 10 messages'
        case 'Immediate': return 'Picture & Age on chat start'
        default: return condition
      }
    },

    getDefaultPlans() {
      return [
        {
          id: '1',
          name: 'Free',
          price: 0,
          features: [
            'Limited chats per day (10)',
            '2 interests selection',
            '2 pictures',
            'Picture reveal if match'
          ]
        },
        {
          id: '2',
          name: 'Premium',
          price: 4.99,
          features: [
            'Limited chats per day (30)',
            '4 interests selection',
            '4 pictures',
            'Picture reveal after 10 messages'
          ]
        },
        {
          id: '3',
          name: 'VIP',
          price: 9.99,
          features: [
            'Unlimited chats per day',
            '6 interests selection',
            '6 pictures',
            'Picture & Age on chat start'
          ]
        }
      ]
    },

    async fetchCurrentPlan() {
      this.isLoading = true
      this.error = null

      try {
        // First try to get the current plan from the API
        try {
          const response = await subscriptionService.getSubscriptions()

          if (response && response.data) {
            const currentPlanFromApi = response.data.find(plan => plan.isCurrent)

            if (currentPlanFromApi) {
              // Transform API data to match our frontend format
              this.currentPlan = {
                id: currentPlanFromApi.id.toString(),
                name: currentPlanFromApi.name,
                price: currentPlanFromApi.price,
                features: [
                  currentPlanFromApi.chatsPerDay === -1
                    ? 'Unlimited chats per day'
                    : `Limited chats per day (${currentPlanFromApi.chatsPerDay})`,
                  `${currentPlanFromApi.maxInterests} interests selection`,
                  `${currentPlanFromApi.maxPictures} pictures`,
                  this.formatPictureRevealCondition(currentPlanFromApi.pictureRevealCondition)
                ],
                apiData: currentPlanFromApi
              }

              return this.currentPlan
            }
          }
        } catch (apiError) {
          // If API call fails, fall back to local data
        }

        // If we couldn't get the current plan from the API, use the local data
        // Make sure we have plans loaded
        if (this.plans.length === 0) {
          await this.fetchPlans()
        }

        // Find the current plan from the user's subscription
        const authStore = useAuthStore()
        const planId = authStore.user?.subscription || '1' // Default to Free plan

        this.currentPlan = this.plans.find(plan => plan.id === planId) || this.plans[0]
        return this.currentPlan
      } catch (error) {
        this.error = error.message || 'Failed to fetch subscription plan'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async upgradePlan(planId, paymentDetails = {}) {
      this.isLoading = true
      this.error = null

      try {
        // Find the plan in our local state
        const plan = this.plans.find(p => p.id === planId)
        if (!plan) {
          throw new Error('Invalid plan selected')
        }

        // If it's a free plan, just update the subscription without payment
        if (plan.price === 0) {
          try {
            const response = await subscriptionService.subscribe(parseInt(planId), {})

            if (response && response.data) {
              // Update user subscription in local state
              const authStore = useAuthStore()
              authStore.user = { ...authStore.user, subscription: planId }
              localStorage.setItem('user', JSON.stringify(authStore.user))

              // Update current plan
              this.currentPlan = plan

              // Refresh plans from API to get updated current plan
              this.fetchPlans()

              return plan
            }
          } catch (apiError) {
            throw apiError
          }
        } else {
          // For paid plans, process payment first
          const paymentResult = await this.processPayment(planId, paymentDetails)

          if (paymentResult.success) {
            // Update user subscription in local state
            const authStore = useAuthStore()
            authStore.user = { ...authStore.user, subscription: planId }
            localStorage.setItem('user', JSON.stringify(authStore.user))

            // Update current plan
            this.currentPlan = plan

            // Refresh plans from API to get updated current plan
            this.fetchPlans()

            return plan
          }
        }
      } catch (error) {
        this.error = error.message || 'Failed to upgrade subscription'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async processPayment(planId, paymentDetails) {
      this.isLoading = true
      this.error = null

      try {
        // Create a payment intent on the server
        const clientSecret = await createPaymentIntent(parseInt(planId))

        // Load Stripe
        const stripe = await getStripe()

        // Confirm the payment with Stripe
        const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
          payment_method: {
            card: paymentDetails.cardElement,
            billing_details: {
              name: paymentDetails.cardName,
              email: paymentDetails.email
            }
          }
        })

        if (error) {
          throw new Error(error.message)
        }

        if (paymentIntent.status === 'succeeded') {
          // Confirm the payment on the server
          const subscription = await confirmPayment(paymentIntent.id)

          return {
            success: true,
            transactionId: paymentIntent.id,
            message: 'Payment processed successfully',
            subscription
          }
        } else {
          throw new Error(`Payment status: ${paymentIntent.status}`)
        }
      } catch (error) {
        this.error = error.message || 'Payment processing failed'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async cancelSubscription() {
      this.isLoading = true
      this.error = null

      try {
        const result = await cancelSubscription()

        if (result.success) {
          // Update user subscription to free plan
          const authStore = useAuthStore()
          authStore.user = { ...authStore.user, subscription: '1' }
          localStorage.setItem('user', JSON.stringify(authStore.user))

          // Update current plan
          this.currentPlan = this.plans.find(p => p.id === '1') || this.plans[0]

          // Refresh plans from API
          this.fetchPlans()

          return result
        } else {
          throw new Error(result.message || 'Failed to cancel subscription')
        }
      } catch (error) {
        this.error = error.message || 'Failed to cancel subscription'
        throw error
      } finally {
        this.isLoading = false
      }
    }
  }
})
