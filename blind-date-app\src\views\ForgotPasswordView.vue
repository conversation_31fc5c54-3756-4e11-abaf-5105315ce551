<template>
  <v-container class="fill-height">
    <v-row justify="center" align="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="pa-6" elevation="8">
          <v-card-title class="text-center text-h5 font-weight-bold mb-4">
            Forgot Password
          </v-card-title>
          
          <v-card-text>
            <div v-if="passwordResetStatus === 'email-sent'">
              <v-alert
                type="success"
                variant="tonal"
                class="mb-4"
              >
                If your email is registered, you will receive a password reset link.
              </v-alert>
              
              <v-btn
                color="primary"
                block
                to="/login"
                class="mt-4"
              >
                Return to Login
              </v-btn>
            </div>
            
            <v-form v-else @submit.prevent="submitForgotPassword" ref="form">
              <p class="mb-4">
                Enter your email address and we'll send you a link to reset your password.
              </p>
              
              <v-text-field
                v-model="email"
                :rules="emailRules"
                label="Email"
                type="email"
                required
                variant="outlined"
                :disabled="isLoading"
              ></v-text-field>
              
              <v-alert
                v-if="errorMessage"
                type="error"
                variant="tonal"
                class="mb-4"
              >
                {{ errorMessage }}
              </v-alert>
              
              <v-btn
                type="submit"
                color="primary"
                block
                :loading="isLoading"
                :disabled="isLoading"
                class="mt-4"
              >
                Send Reset Link
              </v-btn>
              
              <div class="text-center mt-4">
                <v-btn
                  variant="text"
                  to="/login"
                  :disabled="isLoading"
                >
                  Back to Login
                </v-btn>
              </div>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()
const form = ref(null)

const email = ref('')
const errorMessage = ref('')
const isLoading = computed(() => authStore.isLoading)
const passwordResetStatus = computed(() => authStore.passwordResetStatus)

// Email validation rules
const emailRules = [
  v => !!v || 'Email is required',
  v => /.+@.+\..+/.test(v) || 'Email must be valid'
]

// Submit forgot password request
const submitForgotPassword = async () => {
  // Validate the form
  const { valid } = await form.value.validate()
  if (!valid) return
  
  errorMessage.value = ''
  
  try {
    // Call the auth store method to send forgot password request
    await authStore.forgotPassword(email.value)
  } catch (error) {
    errorMessage.value = error.message
  }
}
</script>

<style scoped>
.v-card {
  border-radius: 12px;
}
</style>
