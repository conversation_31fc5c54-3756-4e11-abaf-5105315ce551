{"ConnectionStrings": {"DefaultConnection": "Server=blyndxdb.cyn06ea4ogj7.us-east-1.rds.amazonaws.com,1433;Initial Catalog=blyndxdb;MultipleActiveResultSets=true;TrustServerCertificate=True;User ID=admin;Password=*************", "Redis": "localhost:6379,abortConnect=false"}, "JwtSettings": {"Secret": "YourSuperSecretKeyHereMakeItLongAndComplex", "Issuer": "BlindDateApi", "Audience": "BlindDateApp", "ExpirationInMinutes": 10080}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Email": {"SmtpServer": "smtp.ionos.co.uk", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "27Septembrie", "FromEmail": "<EMAIL>", "FromName": "Blyndx"}, "AppUrl": "http://localhost:5173", "Stripe": {"SecretKey": "sk_test_your_stripe_secret_key", "PublishableKey": "pk_test_your_stripe_publishable_key", "WebhookSecret": "whsec_your_stripe_webhook_secret"}, "AWS": {"AccessKey": "********************", "SecretKey": "0lqmrRM8RzPJqjkwQGKx3TbbTZoHtQRRtX7D9/JQ", "Region": "us-east-1", "BucketName": "blyndx-profile-pictures"}}