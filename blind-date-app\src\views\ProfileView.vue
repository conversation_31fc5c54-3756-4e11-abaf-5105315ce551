<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useProfileStore } from '../stores/profile'
import { useSubscriptionStore } from '../stores/subscription'
import { profileService } from '../services/api'

const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const subscriptionStore = useSubscriptionStore()

const profile = ref(null)
const searchPreferences = ref(null)
const currentPlan = ref(null)
const isEditing = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const newPictureUrl = ref('')
const pictureDialog = ref(false)
const uploadTab = ref(0) // 0 for URL, 1 for file upload
const selectedFile = ref(null)
const filePreview = ref('')
const mobileMenu = ref(false)

// Editable profile copy
const editableProfile = ref({})

// Computed property for max pictures based on subscription
const maxPictures = computed(() => {
  if (!currentPlan.value) return 2 // Default
  return currentPlan.value.id === '1' || currentPlan.value.name === 'Free' ? 2 :
         currentPlan.value.id === '2' || currentPlan.value.name === 'Premium' ? 4 : 6
})

// Computed property for max interests based on subscription
const maxInterests = computed(() => {
  if (!currentPlan.value) return 2 // Default
  return currentPlan.value.id === '1' || currentPlan.value.name === 'Free' ? 2 :
         currentPlan.value.id === '2' || currentPlan.value.name === 'Premium' ? 4 : 6
})

// Computed property to check if user can add more pictures
const canAddMorePictures = computed(() => {
  if (!editableProfile.value.pictures) return true
  return editableProfile.value.pictures.length < maxPictures.value
})

onMounted(async () => {
  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  try {
    isLoading.value = true

    // Fetch interests first so they're available for the profile
    await profileStore.fetchInterests()

    // Fetch profile data
    profile.value = await profileStore.fetchProfile()

    // Initialize pictures array if it doesn't exist
    if (!profile.value.pictures) {
      profile.value.pictures = []
    }

    // Initialize interests array if it doesn't exist
    if (!profile.value.interests) {
      profile.value.interests = []
    }

    editableProfile.value = { ...profile.value }

    // Fetch search preferences
    searchPreferences.value = await profileStore.fetchSearchPreferences()

    // Fetch subscription plan
    currentPlan.value = await subscriptionStore.fetchCurrentPlan()
  } catch (error) {
    errorMessage.value = error.message || 'Failed to load profile data'
  } finally {
    isLoading.value = false
  }
})

const toggleEditMode = () => {
  if (isEditing.value) {
    // Cancel editing
    editableProfile.value = { ...profile.value }
    editableSearchPreferences.value = {}
  } else {
    // Start editing - initialize editable search preferences
    editableSearchPreferences.value = { ...searchPreferences.value }
  }

  isEditing.value = !isEditing.value
}

// Editable search preferences copy
const editableSearchPreferences = ref({})

// Date picker variables
const showDatePicker = ref(false)

// Formatted birth date for display
const formattedBirthDate = computed(() => {
  if (!editableProfile.value.birthDate) return ''
  return new Date(editableProfile.value.birthDate).toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
})

// Calculate age from birth date
const calculateAge = (birthDate) => {
  if (!birthDate) return ''
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

const saveProfile = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''

    // Create a combined profile data object with search preferences
    const combinedProfileData = { ...editableProfile.value }

    // Add search preferences to the profile data if they were edited
    if (Object.keys(editableSearchPreferences.value).length > 0) {
      // Set age preferences directly in the profile data
      combinedProfileData.minAgePreference = editableSearchPreferences.value.ageRange[0]
      combinedProfileData.maxAgePreference = editableSearchPreferences.value.ageRange[1]

      // Set gender preference (1 for men, 2 for women)
      combinedProfileData.preference = editableSearchPreferences.value.gender === 'men' ? 1 : 2
    }

    // Make a single API call to update everything
    await profileStore.updateProfile(combinedProfileData)

    // Update local state
    profile.value = { ...editableProfile.value }

    // Update search preferences in the store without making another API call
    if (Object.keys(editableSearchPreferences.value).length > 0) {
      profileStore.setSearchPreferences(editableSearchPreferences.value)
      searchPreferences.value = { ...editableSearchPreferences.value }
    }

    isEditing.value = false
  } catch (error) {
    errorMessage.value = error.message || 'Failed to save profile'
  } finally {
    isLoading.value = false
  }
}

const toggleInterest = (interest) => {
  const index = editableProfile.value.interests.indexOf(interest)

  if (index === -1) {
    // Add interest if not at max
    if (editableProfile.value.interests.length < maxInterests.value) {
      editableProfile.value.interests.push(interest)
    }
  } else {
    // Remove interest
    editableProfile.value.interests.splice(index, 1)
  }
}

// No longer needed as we replaced the interests chip group with gender chips

const addPicture = async () => {
  if (!canAddMorePictures.value) return

  // Initialize pictures array if it doesn't exist
  if (!editableProfile.value.pictures) {
    editableProfile.value.pictures = []
  }

  // Add picture based on selected tab
  if (uploadTab.value === 0) {
    // URL tab
    if (!newPictureUrl.value.trim()) return

    editableProfile.value.pictures.push(newPictureUrl.value)
    newPictureUrl.value = ''
    pictureDialog.value = false
  } else {
    // File upload tab
    if (!selectedFile.value) return

    isLoading.value = true
    errorMessage.value = ''

    try {
      // Create form data
      const formData = new FormData()
      formData.append('file', selectedFile.value)

      // Upload to S3 via API
      const response = await profileService.uploadProfilePicture(formData)

      if (response && response.data && response.data.url) {
        // Add the S3 URL to the profile pictures
        editableProfile.value.pictures.push(response.data.url)
        filePreview.value = ''
        selectedFile.value = null
        pictureDialog.value = false
      } else {
        throw new Error('Invalid response from server')
      }
    } catch (error) {
      errorMessage.value = error.response?.data || error.message || 'Failed to upload picture'
    } finally {
      isLoading.value = false
    }
  }
}

const handleFileUpload = (event) => {
  const file = event.target.files[0]
  if (!file) {
    clearFileInput()
    return
  }

  // Check if file is an image
  if (!file.type.match('image.*')) {
    errorMessage.value = 'Please select an image file'
    clearFileInput()
    return
  }

  // Check file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    errorMessage.value = 'File size should not exceed 5MB'
    clearFileInput()
    return
  }

  // Create a custom file object with a shorter name if needed
  const fileName = file.name.length > 20
    ? file.name.substring(0, 10) + '...' + file.name.substring(file.name.lastIndexOf('.'))
    : file.name

  // Store the original file but with a potentially modified name for display
  selectedFile.value = new File([file], fileName, { type: file.type })

  // Create a preview
  const reader = new FileReader()
  reader.onload = (e) => {
    filePreview.value = e.target.result
  }
  reader.readAsDataURL(file)
}

const removePicture = async (index) => {
  try {
    const pictureUrl = editableProfile.value.pictures[index]

    // Check if it's an S3 URL (contains the bucket name)
    if (pictureUrl.includes('s3.amazonaws.com')) {
      isLoading.value = true

      // Delete from S3 via API
      await profileService.deleteProfilePicture(pictureUrl)
    }

    // Remove from local state
    editableProfile.value.pictures.splice(index, 1)
  } catch (error) {
    console.error('Failed to delete picture:', error)
    // Still remove from local state even if S3 delete fails
    editableProfile.value.pictures.splice(index, 1)
  } finally {
    isLoading.value = false
  }
}

const clearFileInput = () => {
  // Clear file input and preview
  selectedFile.value = null
  filePreview.value = ''
}

const cancelPictureDialog = () => {
  // Reset all form fields and close the dialog
  pictureDialog.value = false
  newPictureUrl.value = ''
  clearFileInput()
  uploadTab.value = 0 // Reset to URL tab
  errorMessage.value = ''
}

const navigateToHome = () => {
  router.push('/home')
}

const navigateToMatches = () => {
  router.push('/matches')
}

const navigateToPayments = () => {
  router.push('/payments')
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}
</script>

<template>
  <v-layout>
    <!-- App Bar -->
    <v-app-bar color="white" elevation="1">
      <v-app-bar-title class="text-primary font-weight-bold">Blind Date</v-app-bar-title>

      <v-spacer></v-spacer>

      <!-- Desktop Navigation -->
      <div class="d-none d-sm-flex">
        <v-btn variant="text" @click="navigateToHome">
          <v-icon start>mdi-home</v-icon>
          Home
        </v-btn>

        <v-btn variant="text" @click="navigateToMatches">
          <v-icon start>mdi-account-multiple</v-icon>
          Matches
        </v-btn>

        <v-btn variant="text" @click="navigateToPayments">
          <v-icon start>mdi-crown</v-icon>
          Upgrade
        </v-btn>

        <v-btn variant="text" @click="logout">
          <v-icon start>mdi-logout</v-icon>
          Logout
        </v-btn>
      </div>

      <!-- Mobile Navigation -->
      <v-menu v-model="mobileMenu">
        <template v-slot:activator="{ props }">
          <v-btn
            icon
            v-bind="props"
            class="d-sm-none"
          >
            <v-icon>mdi-menu</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item @click="navigateToHome">
            <template v-slot:prepend>
              <v-icon>mdi-home</v-icon>
            </template>
            <v-list-item-title>Home</v-list-item-title>
          </v-list-item>

          <v-list-item @click="navigateToMatches">
            <template v-slot:prepend>
              <v-icon>mdi-account-multiple</v-icon>
            </template>
            <v-list-item-title>Matches</v-list-item-title>
          </v-list-item>

          <v-list-item @click="navigateToPayments">
            <template v-slot:prepend>
              <v-icon>mdi-crown</v-icon>
            </template>
            <v-list-item-title>Upgrade</v-list-item-title>
          </v-list-item>

          <v-list-item @click="logout">
            <template v-slot:prepend>
              <v-icon>mdi-logout</v-icon>
            </template>
            <v-list-item-title>Logout</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <v-container>
        <v-row justify="center">
          <v-col cols="12" sm="10" md="8">
            <v-progress-circular
              v-if="isLoading"
              indeterminate
              color="primary"
              size="64"
              class="mx-auto my-12 d-block"
            ></v-progress-circular>

            <template v-else-if="profile">
              <!-- Profile Card -->
              <v-card class="mb-6">
                <v-card-title class="d-flex justify-space-between align-center">
                  <span class="text-h5">Your Profile</span>

                  <div>
                    <v-btn
                      v-if="isEditing"
                      color="primary"
                      variant="elevated"
                      :loading="isLoading"
                      class="mr-2"
                      @click="saveProfile"
                    >
                      Save
                    </v-btn>

                    <v-btn
                      variant="outlined"
                      @click="toggleEditMode"
                    >
                      {{ isEditing ? 'Cancel' : 'Edit' }}
                    </v-btn>
                  </div>
                </v-card-title>

                <v-card-text>
                  <v-alert
                    v-if="errorMessage"
                    type="error"
                    variant="tonal"
                    class="mb-4"
                  >
                    {{ errorMessage }}
                  </v-alert>

                  <!-- Basic Information -->
                  <v-sheet class="mb-6 pa-4 rounded" color="grey-lighten-4">
                    <h3 class="text-h6 mb-4">Basic Information</h3>

                    <v-row>
                      <v-col cols="12" sm="6">
                        <v-text-field
                          v-if="isEditing"
                          v-model="editableProfile.firstName"
                          label="First Name"
                          variant="outlined"
                          density="comfortable"
                        ></v-text-field>
                        <div v-else>
                          <div class="text-caption text-grey">First Name</div>
                          <div>{{ profile.firstName }}</div>
                        </div>
                      </v-col>

                      <v-col cols="12" sm="6">
                        <v-text-field
                          v-if="isEditing"
                          v-model="editableProfile.lastName"
                          label="Last Name"
                          variant="outlined"
                          density="comfortable"
                        ></v-text-field>
                        <div v-else>
                          <div class="text-caption text-grey">Last Name</div>
                          <div>{{ profile.lastName }}</div>
                        </div>
                      </v-col>

                      <v-col cols="12">
                        <template v-if="isEditing">
                          <v-menu
                            v-model="showDatePicker"
                            :close-on-content-click="false"
                            transition="scale-transition"
                            min-width="auto"
                          >
                            <template v-slot:activator="{ props }">
                              <v-text-field
                                v-model="formattedBirthDate"
                                label="Birth Date"
                                variant="outlined"
                                readonly
                                v-bind="props"
                                placeholder="SELECT DATE"
                                :hint="editableProfile.birthDate ? `Age: ${calculateAge(editableProfile.birthDate)}` : ''"
                                persistent-hint
                              ></v-text-field>
                            </template>
                            <v-date-picker
                              v-model="editableProfile.birthDate"
                              :max="new Date(new Date().setFullYear(new Date().getFullYear() - 18)).toISOString().split('T')[0]"
                              @update:model-value="showDatePicker = false"
                            >
                              <template v-slot:header="{ date }">
                                <div class="text-h5 pa-2">
                                  {{ date ? new Date(date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }) : 'Select date' }}
                                </div>
                              </template>
                            </v-date-picker>
                          </v-menu>
                        </template>
                        <div v-else>
                          <div class="text-caption text-grey">Birth Date</div>
                          <div>{{ profile.birthDate ? new Date(profile.birthDate).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' }) : '' }}</div>
                          <div class="text-caption text-grey mt-1" v-if="profile.birthDate">Age: {{ calculateAge(profile.birthDate) }}</div>
                        </div>
                      </v-col>

                      <v-col cols="12">
                        <v-text-field
                          v-if="isEditing"
                          v-model="editableProfile.location"
                          label="Location"
                          variant="outlined"
                          density="comfortable"
                        ></v-text-field>
                        <div v-else>
                          <div class="text-caption text-grey">Location</div>
                          <div>{{ profile.location }}</div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-sheet>

                  <!-- Profile Pictures -->
                  <v-sheet class="mb-6 pa-4 rounded" color="grey-lighten-4">
                    <div class="d-flex justify-space-between align-center mb-4">
                      <h3 class="text-h6">Profile Pictures</h3>
                      <div class="text-caption text-grey">
                        {{ editableProfile.pictures ? editableProfile.pictures.length : 0 }} / {{ maxPictures }}
                      </div>
                    </div>

                    <div v-if="isEditing">
                      <v-row>
                        <v-col
                          v-for="(picture, index) in editableProfile.pictures"
                          :key="index"
                          cols="12" sm="6" md="4"
                        >
                          <v-card>
                            <v-img
                              :src="picture"
                              height="200"
                              cover
                            ></v-img>
                            <v-card-actions>
                              <v-spacer></v-spacer>
                              <v-btn
                                color="error"
                                icon
                                variant="text"
                                @click="removePicture(index)"
                              >
                                <v-icon>mdi-delete</v-icon>
                              </v-btn>
                            </v-card-actions>
                          </v-card>
                        </v-col>

                        <v-col cols="12" sm="6" md="4" v-if="canAddMorePictures">
                          <v-card
                            height="200"
                            class="d-flex align-center justify-center"
                            variant="outlined"
                            @click="pictureDialog = true"
                          >
                            <div class="text-center">
                              <v-icon size="large" color="primary">mdi-plus</v-icon>
                              <div class="mt-2">Add Picture</div>
                            </div>
                          </v-card>
                        </v-col>
                      </v-row>

                      <div class="text-caption text-grey mt-2">
                        You can add up to {{ maxPictures }} pictures with your {{ currentPlan.name }} plan.
                      </div>
                    </div>
                    <div v-else>
                      <v-row v-if="profile.pictures && profile.pictures.length > 0">
                        <v-col
                          v-for="(picture, index) in profile.pictures"
                          :key="index"
                          cols="12" sm="6" md="4"
                        >
                          <v-img
                            :src="picture"
                            height="200"
                            cover
                            class="rounded"
                          ></v-img>
                        </v-col>
                      </v-row>
                      <div v-else class="text-center py-4 text-grey">
                        No profile pictures added yet.
                      </div>
                    </div>
                  </v-sheet>

                  <!-- Bio -->
                  <v-sheet class="mb-6 pa-4 rounded" color="grey-lighten-4">
                    <h3 class="text-h6 mb-4">About Me</h3>

                    <v-textarea
                      v-if="isEditing"
                      v-model="editableProfile.bio"
                      label="Bio"
                      variant="outlined"
                      rows="4"
                    ></v-textarea>
                    <div v-else>
                      <p>{{ profile.bio }}</p>
                    </div>
                  </v-sheet>

                  <!-- Interests -->
                  <v-sheet class="mb-6 pa-4 rounded" color="grey-lighten-4">
                    <h3 class="text-h6 mb-4">Interests</h3>

                    <div v-if="isEditing">
                      <v-chip-group>
                        <v-chip
                          v-for="interest in profileStore.interestsData"
                          :key="interest.id"
                          :color="editableProfile.interests.includes(interest.name) ? 'primary' : undefined"
                          :variant="editableProfile.interests.includes(interest.name) ? 'elevated' : 'outlined'"
                          class="ma-1"
                          @click="toggleInterest(interest.name)"
                        >
                          {{ interest.name }}
                        </v-chip>
                      </v-chip-group>

                      <div class="text-caption text-grey mt-2">
                        You can select up to {{ maxInterests }} interests
                        with your {{ currentPlan.name }} plan.
                      </div>
                    </div>
                    <div v-else>
                      <v-chip
                        v-for="interest in profile.interests"
                        :key="interest"
                        color="primary"
                        variant="outlined"
                        class="ma-1"
                      >
                        {{ interest }}
                      </v-chip>
                    </div>
                  </v-sheet>

                  <!-- Search Preferences -->
                  <v-sheet class="mb-6 pa-4 rounded" color="grey-lighten-4">
                    <h3 class="text-h6 mb-4">Search Preferences</h3>

                    <div v-if="isEditing && searchPreferences">
                      <h4 class="text-subtitle-1 mb-2">Age Range</h4>
                      <v-row>
                        <v-col cols="12" sm="5">
                          <v-text-field
                            v-model.number="editableSearchPreferences.ageRange[0]"
                            label="Min Age"
                            type="number"
                            variant="outlined"
                            min="18"
                            max="100"
                            density="compact"
                          ></v-text-field>
                        </v-col>

                        <v-col cols="12" sm="2" style="display: flex; align-items: center; justify-content: center;" class="py-0 py-sm-3">
                          <span class="my-2 my-sm-0">to</span>
                        </v-col>

                        <v-col cols="12" sm="5">
                          <v-text-field
                            v-model.number="editableSearchPreferences.ageRange[1]"
                            label="Max Age"
                            type="number"
                            variant="outlined"
                            min="18"
                            max="100"
                            density="compact"
                          ></v-text-field>
                        </v-col>
                      </v-row>

                      <h4 class="text-subtitle-1 mb-2">Maximum Distance (km)</h4>
                      <v-slider
                        v-model="editableSearchPreferences.distance"
                        min="5"
                        max="100"
                        step="5"
                        show-ticks="always"
                        :ticks="[5, 25, 50, 75, 100]"
                        thumb-label
                      ></v-slider>

                      <h4 class="text-subtitle-1 mb-2">Searching for</h4>
                      <v-chip-group>
                        <v-chip
                          :color="editableSearchPreferences.gender === 'men' ? 'primary' : undefined"
                          :variant="editableSearchPreferences.gender === 'men' ? 'elevated' : 'outlined'"
                          class="ma-1"
                          @click="editableSearchPreferences.gender = 'men'"
                        >
                          Men
                        </v-chip>
                        <v-chip
                          :color="editableSearchPreferences.gender === 'women' ? 'primary' : undefined"
                          :variant="editableSearchPreferences.gender === 'women' ? 'elevated' : 'outlined'"
                          class="ma-1"
                          @click="editableSearchPreferences.gender = 'women'"
                        >
                          Women
                        </v-chip>
                      </v-chip-group>
                    </div>
                    <div v-else-if="searchPreferences">
                      <v-row>
                        <v-col cols="12" sm="6">
                          <div class="text-caption text-grey">Age Range</div>
                          <div>{{ searchPreferences.ageRange[0] }} - {{ searchPreferences.ageRange[1] }} years</div>
                        </v-col>

                        <v-col cols="12" sm="6">
                          <div class="text-caption text-grey">Maximum Distance</div>
                          <div>{{ searchPreferences.distance }} km</div>
                        </v-col>
                      </v-row>

                      <div class="mt-4">
                        <div class="text-caption text-grey mb-2">Searching for</div>
                        <v-chip
                          color="primary"
                          variant="outlined"
                          class="ma-1"
                        >
                          {{ searchPreferences.gender === 'men' ? 'Men' : 'Women' }}
                        </v-chip>
                      </div>
                    </div>
                  </v-sheet>
                </v-card-text>
              </v-card>

              <!-- Subscription Card -->
              <v-card>
                <v-card-title class="d-flex justify-space-between align-center">
                  <span class="text-h5">Your Subscription</span>

                  <v-btn
                    color="primary"
                    variant="elevated"
                    @click="navigateToPayments"
                  >
                    Upgrade
                  </v-btn>
                </v-card-title>

                <v-card-text v-if="currentPlan">
                  <v-card variant="outlined" class="mb-4">
                    <v-card-item>
                      <v-card-title>
                        {{ currentPlan.name }} Plan
                        <v-chip
                          color="success"
                          size="small"
                          class="ml-2"
                        >
                          Active
                        </v-chip>
                      </v-card-title>

                      <v-card-subtitle>
                        {{ currentPlan.price > 0 ? `$${currentPlan.price}/month` : 'Free' }}
                      </v-card-subtitle>
                    </v-card-item>
                  </v-card>

                  <h3 class="text-subtitle-1 font-weight-bold mb-2">Features</h3>
                  <v-list density="compact">
                    <v-list-item
                      v-for="feature in currentPlan.features"
                      :key="feature"
                      :title="feature"
                      prepend-icon="mdi-check"
                    ></v-list-item>
                  </v-list>
                </v-card-text>
              </v-card>
            </template>
          </v-col>
        </v-row>
      </v-container>
    </v-main>

    <!-- Add Picture Dialog -->
    <v-dialog v-model="pictureDialog" max-width="500" @update:model-value="val => !val && cancelPictureDialog()">
      <v-card>
        <v-card-title>Add Profile Picture</v-card-title>

        <v-card-text>
          <v-alert
            v-if="errorMessage"
            type="error"
            variant="tonal"
            class="mb-4"
            density="compact"
          >
            {{ errorMessage }}
          </v-alert>

          <v-tabs v-model="uploadTab" grow>
            <v-tab value="0">URL</v-tab>
            <v-tab value="1">Upload File</v-tab>
          </v-tabs>

          <v-window v-model="uploadTab" class="mt-4">
            <!-- URL Tab -->
            <v-window-item value="0">
              <v-form @submit.prevent="addPicture">
                <v-text-field
                  v-model="newPictureUrl"
                  label="Picture URL"
                  variant="outlined"
                  placeholder="https://example.com/image.jpg"
                  hint="Enter a URL to an image"
                  persistent-hint
                  required
                ></v-text-field>
              </v-form>
            </v-window-item>

            <!-- File Upload Tab -->
            <v-window-item value="1">
              <div class="pa-2">
                <v-file-input
                  label="Select Image"
                  variant="outlined"
                  accept="image/*"
                  prepend-icon="mdi-camera"
                  @change="handleFileUpload"
                  hint="Select an image file (max 5MB)"
                  persistent-hint
                  :model-value="selectedFile"
                  hide-details="auto"
                  placeholder="Select Image"
                  clearable
                  @click:clear="clearFileInput"
                  truncate-length="15"
                  class="file-input"
                ></v-file-input>

                <div v-if="filePreview" class="mt-4">
                  <v-card variant="outlined" class="pa-2">
                    <div class="d-flex align-center mb-2">
                      <v-icon color="success" class="mr-2">mdi-check-circle</v-icon>
                      <span class="text-body-2">Image selected</span>
                      <v-spacer></v-spacer>
                      <v-btn
                        density="compact"
                        icon="mdi-refresh"
                        size="small"
                        variant="text"
                        @click="clearFileInput"
                        title="Select different image"
                      ></v-btn>
                    </div>
                    <v-img
                      :src="filePreview"
                      max-height="200"
                      contain
                      class="bg-grey-lighten-4 rounded"
                    ></v-img>
                  </v-card>
                </div>
              </div>
            </v-window-item>
          </v-window>

          <div class="text-caption text-grey mt-4">
            You can add up to {{ maxPictures }} pictures with your {{ currentPlan.name }} plan.
            ({{ editableProfile.pictures ? editableProfile.pictures.length : 0 }} / {{ maxPictures }})
          </div>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>

          <v-btn
            variant="text"
            @click="cancelPictureDialog"
          >
            Cancel
          </v-btn>

          <v-btn
            color="primary"
            variant="elevated"
            @click="addPicture"
            :loading="isLoading"
            :disabled="(uploadTab === 0 && !newPictureUrl.trim()) ||
                       (uploadTab === 1 && !filePreview) ||
                       !canAddMorePictures ||
                       isLoading"
          >
            Add Picture
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<style scoped>
.file-input :deep(.v-field__input) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>