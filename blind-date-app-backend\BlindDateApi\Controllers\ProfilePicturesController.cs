using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/profile-pictures")]
    [Authorize]
    public class ProfilePicturesController : ControllerBase
    {
        private readonly IRepository<Profile> _profileRepository;
        private readonly IUserRepository _userRepository;
        private readonly IS3Service _s3Service;
        private readonly ILogger<ProfilePicturesController> _logger;

        public ProfilePicturesController(
            IRepository<Profile> profileRepository,
            IUserRepository userRepository,
            IS3Service s3Service,
            ILogger<ProfilePicturesController> logger)
        {
            _profileRepository = profileRepository;
            _userRepository = userRepository;
            _s3Service = s3Service;
            _logger = logger;
        }

        [HttpPost("upload")]
        public async Task<ActionResult<ProfilePictureDto>> UploadProfilePicture([FromForm] ProfilePictureUploadDto uploadDto)
        {
            try
            {
                // Get user ID from claims
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return Unauthorized();

                // Get user with subscription
                var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
                if (user == null)
                    return NotFound("User not found");

                // Get profile
                var profile = await _profileRepository.SingleOrDefaultAsync(p => p.UserId == userId);
                if (profile == null)
                    return NotFound("Profile not found");

                // Get user's subscription to check limits
                var subscription = user.CurrentSubscription?.Subscription;
                if (subscription == null)
                    return BadRequest("No active subscription found");

                // Check if pictures exceed the limit
                var currentPictures = profile.PicturesList;
                if (currentPictures.Count >= subscription.MaxPictures)
                {
                    return BadRequest($"You can only have up to {subscription.MaxPictures} pictures with your current subscription");
                }

                // Check if file is provided
                if (uploadDto.File == null || uploadDto.File.Length == 0)
                    return BadRequest("No file uploaded");

                // Check file size (max 5MB)
                if (uploadDto.File.Length > 5 * 1024 * 1024)
                    return BadRequest("File size should not exceed 5MB");

                // Check file type
                var allowedTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/webp" };
                if (!allowedTypes.Contains(uploadDto.File.ContentType.ToLower()))
                    return BadRequest("Only image files are allowed (JPEG, PNG, GIF, WEBP)");

                // Upload file to S3
                var fileUrl = await _s3Service.UploadFileAsync(uploadDto.File, userId);

                // Add the new picture URL to the profile
                currentPictures.Add(fileUrl);
                profile.PicturesList = currentPictures;

                // Save changes
                _profileRepository.Update(profile);
                await _profileRepository.SaveChangesAsync();

                return Ok(new ProfilePictureDto
                {
                    Url = fileUrl,
                    Success = true,
                    Message = "Profile picture uploaded successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading profile picture");
                return StatusCode(500, "An error occurred while uploading the profile picture");
            }
        }

        [HttpDelete("{pictureUrl}")]
        public async Task<ActionResult> DeleteProfilePicture(string pictureUrl)
        {
            try
            {
                // Get user ID from claims
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return Unauthorized();

                // Get profile
                var profile = await _profileRepository.SingleOrDefaultAsync(p => p.UserId == userId);
                if (profile == null)
                    return NotFound("Profile not found");

                // Check if the picture exists in the profile
                var currentPictures = profile.PicturesList;
                if (!currentPictures.Contains(pictureUrl))
                    return NotFound("Picture not found in profile");

                // Delete the file from S3
                var result = await _s3Service.DeleteFileAsync(pictureUrl);
                if (!result)
                    return StatusCode(500, "Failed to delete the file from storage");

                // Remove the picture URL from the profile
                currentPictures.Remove(pictureUrl);
                profile.PicturesList = currentPictures;

                // Save changes
                _profileRepository.Update(profile);
                await _profileRepository.SaveChangesAsync();

                return Ok(new { success = true, message = "Profile picture deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting profile picture");
                return StatusCode(500, "An error occurred while deleting the profile picture");
            }
        }
    }
}
