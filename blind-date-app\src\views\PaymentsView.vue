<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import StripePaymentForm from '../components/StripePaymentForm.vue'

const router = useRouter()
const authStore = useAuthStore()
const subscriptionStore = useSubscriptionStore()

const plans = computed(() => subscriptionStore.plans)
const currentPlan = ref(null)
const selectedPlan = ref(null)
const isLoading = ref(false)
const errorMessage = ref('')
const showPaymentForm = ref(false)
const paymentSuccess = ref(false)
const mobileMenu = ref(false)

// Payment form data
const paymentData = ref({
  cardNumber: '',
  cardName: '',
  expiryDate: '',
  cvv: ''
})

// Form validation rules
const rules = {
  required: value => !!value || 'Required.',
  cardNumber: value => /^\d{16}$/.test(value) || 'Card number must be 16 digits',
  expiryDate: value => /^\d{2}\/\d{2}$/.test(value) || 'Format must be MM/YY',
  cvv: value => /^\d{3,4}$/.test(value) || 'CVV must be 3 or 4 digits'
}

onMounted(async () => {
  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  try {
    isLoading.value = true

    // Fetch all subscription plans first
    await subscriptionStore.fetchPlans()

    // Then fetch current subscription plan
    currentPlan.value = await subscriptionStore.fetchCurrentPlan()
  } catch (error) {
    errorMessage.value = error.message || 'Failed to load subscription data'
  } finally {
    isLoading.value = false
  }
})

const selectPlan = (plan) => {
  selectedPlan.value = plan

  // If selecting current plan, don't show payment form
  if (plan.id === currentPlan.value?.id) {
    showPaymentForm.value = false
    return
  }

  // If selecting free plan, don't show payment form
  if (plan.price === 0) {
    processUpgrade()
  } else {
    showPaymentForm.value = true
  }
}

const processUpgrade = async () => {
  if (!selectedPlan.value) return

  try {
    isLoading.value = true
    errorMessage.value = ''

    // If payment is required and form is shown
    if (selectedPlan.value.price > 0 && showPaymentForm.value) {
      // Validate payment form
      if (!validatePaymentForm()) {
        return
      }

      // Process payment
      await subscriptionStore.processPayment(paymentData.value)
    }

    // Upgrade plan
    await subscriptionStore.upgradePlan(selectedPlan.value.id)

    // Update current plan
    currentPlan.value = selectedPlan.value

    // Show success message
    paymentSuccess.value = true
    showPaymentForm.value = false

    // Reset form
    paymentData.value = {
      cardNumber: '',
      cardName: '',
      expiryDate: '',
      cvv: ''
    }
  } catch (error) {
    errorMessage.value = error.message || 'Failed to upgrade subscription'
  } finally {
    isLoading.value = false
  }
}

const validatePaymentForm = () => {
  // Simple validation
  if (!paymentData.value.cardNumber ||
      !paymentData.value.cardName ||
      !paymentData.value.expiryDate ||
      !paymentData.value.cvv) {
    errorMessage.value = 'Please fill in all payment details'
    return false
  }

  return true
}

const cancelPayment = () => {
  showPaymentForm.value = false
  selectedPlan.value = null
  errorMessage.value = ''
}

const navigateToHome = () => {
  router.push('/home')
}

const navigateToMatches = () => {
  router.push('/matches')
}

const navigateToProfile = () => {
  router.push('/profile')
}

const handleStripePayment = async (paymentDetails) => {
  if (!selectedPlan.value) return

  try {
    isLoading.value = true
    errorMessage.value = ''

    // Upgrade plan with Stripe payment details
    await subscriptionStore.upgradePlan(selectedPlan.value.id, paymentDetails)

    // Update current plan
    currentPlan.value = selectedPlan.value

    // Show success message
    paymentSuccess.value = true
    showPaymentForm.value = false
  } catch (error) {
    errorMessage.value = error.message || 'Failed to process payment'
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <v-layout>
    <!-- App Bar -->
    <v-app-bar color="white" elevation="1">
      <v-app-bar-title class="text-primary font-weight-bold">Blind Date</v-app-bar-title>

      <v-spacer></v-spacer>

      <!-- Desktop Navigation -->
      <div class="d-none d-sm-flex">
        <v-btn variant="text" @click="navigateToHome">
          <v-icon start>mdi-home</v-icon>
          Home
        </v-btn>

        <v-btn variant="text" @click="navigateToMatches">
          <v-icon start>mdi-account-multiple</v-icon>
          Matches
        </v-btn>

        <v-btn variant="text" @click="navigateToProfile">
          <v-icon start>mdi-account</v-icon>
          Profile
        </v-btn>
      </div>

      <!-- Mobile Navigation -->
      <v-menu v-model="mobileMenu">
        <template v-slot:activator="{ props }">
          <v-btn
            icon
            v-bind="props"
            class="d-sm-none"
          >
            <v-icon>mdi-menu</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item @click="navigateToHome">
            <template v-slot:prepend>
              <v-icon>mdi-home</v-icon>
            </template>
            <v-list-item-title>Home</v-list-item-title>
          </v-list-item>

          <v-list-item @click="navigateToMatches">
            <template v-slot:prepend>
              <v-icon>mdi-account-multiple</v-icon>
            </template>
            <v-list-item-title>Matches</v-list-item-title>
          </v-list-item>

          <v-list-item @click="navigateToProfile">
            <template v-slot:prepend>
              <v-icon>mdi-account</v-icon>
            </template>
            <v-list-item-title>Profile</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <v-container>
        <v-row justify="center">
          <v-col cols="12">
            <v-card class="mb-6 text-center pa-4">
              <v-card-title class="text-h4 text-sm-h3 font-weight-bold text-truncate-none">Subscription Plans</v-card-title>
              <v-card-subtitle>Choose the plan that works best for you</v-card-subtitle>
            </v-card>

            <v-progress-circular
              v-if="isLoading"
              indeterminate
              color="primary"
              size="64"
              class="mx-auto my-12 d-block"
            ></v-progress-circular>

            <v-card v-else-if="paymentSuccess" class="text-center pa-6 mb-6">
              <v-card-item>
                <template v-slot:prepend>
                  <v-icon color="success" size="x-large">mdi-check-circle</v-icon>
                </template>

                <v-card-title class="text-h5">Payment Successful!</v-card-title>
                <v-card-subtitle>
                  Your subscription has been upgraded to {{ currentPlan?.name }}
                </v-card-subtitle>
              </v-card-item>

              <v-card-actions class="justify-center">
                <v-btn
                  color="primary"
                  variant="elevated"
                  @click="navigateToHome"
                >
                  Continue to Home
                </v-btn>
              </v-card-actions>
            </v-card>

            <template v-else>
              <v-alert
                v-if="errorMessage"
                type="error"
                variant="tonal"
                class="mb-6"
              >
                {{ errorMessage }}
              </v-alert>

              <!-- Subscription plans -->
              <v-row>
                <v-col
                  v-for="plan in plans"
                  :key="plan.id"
                  cols="12" sm="6" md="4"
                >
                  <v-card
                    :elevation="currentPlan?.id === plan.id ? 8 : 2"
                    :border="currentPlan?.id === plan.id"
                    :color="currentPlan?.id === plan.id ? 'primary-lighten-5' : undefined"
                    height="100%"
                    class="position-relative"
                  >
                    <v-chip
                      v-if="currentPlan?.id === plan.id"
                      color="primary"
                      class="position-absolute"
                      style="top: 10px; right: 10px;"
                    >
                      Current Plan
                    </v-chip>

                    <v-card-title class="text-h5 font-weight-bold pt-6">
                      {{ plan.name }}
                    </v-card-title>

                    <v-card-subtitle class="text-h4 font-weight-bold">
                      {{ plan.price > 0 ? `€${plan.price}` : 'Free' }}
                      <span v-if="plan.price > 0" class="text-body-1 font-weight-regular">/month</span>
                    </v-card-subtitle>

                    <v-card-text>
                      <v-list density="compact">
                        <v-list-item
                          v-for="feature in plan.features"
                          :key="feature"
                          prepend-icon="mdi-check-circle"
                          prepend-icon-color="success"
                          class="feature-item"
                        >
                          <template v-slot:title>
                            <span class="text-wrap">{{ feature }}</span>
                          </template>
                        </v-list-item>
                      </v-list>
                    </v-card-text>

                    <v-card-actions class="pa-4">
                      <v-btn
                        block
                        :color="currentPlan?.id === plan.id ? undefined : 'primary'"
                        :variant="currentPlan?.id === plan.id ? 'outlined' : 'elevated'"
                        :disabled="currentPlan?.id === plan.id || isLoading"
                        @click="selectPlan(plan)"
                      >
                        {{ currentPlan?.id === plan.id ? 'Current Plan' : 'Select Plan' }}
                      </v-btn>
                    </v-card-actions>
                  </v-card>
                </v-col>
              </v-row>
            </template>
          </v-col>
        </v-row>
      </v-container>
    </v-main>

    <!-- Payment Dialog -->
    <v-dialog v-model="showPaymentForm" max-width="500">
      <v-card>
        <v-card-title class="text-h5">
          Payment Details
        </v-card-title>

        <v-card-subtitle>
          Upgrade to {{ selectedPlan?.name }} for €{{ selectedPlan?.price }}/month
        </v-card-subtitle>

        <v-card-text>
          <StripePaymentForm
            :amount="selectedPlan?.price || 0"
            :is-loading="isLoading"
            @submit="handleStripePayment"
          />
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>

          <v-btn
            variant="text"
            @click="cancelPayment"
          >
            Cancel
          </v-btn>

          <v-btn
            color="primary"
            variant="elevated"
            :loading="isLoading"
            :disabled="true"
          >
            Continue with Payment Form
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<style scoped>
.text-truncate-none {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

.text-wrap {
  white-space: normal !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}

.feature-item {
  min-height: 48px;
  height: auto !important;
}

/* Ensure list items can wrap on small screens */
.v-list-item__content {
  white-space: normal !important;
}
</style>