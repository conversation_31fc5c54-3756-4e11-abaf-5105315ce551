﻿// <auto-generated />
using System;
using BlindDateApi.Data.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace BlindDateApi.Data.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250417175927_AddUserOnlineStatus")]
    partial class AddUserOnlineStatus
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.14")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("BlindDateApi.Data.Models.Conversation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("EncryptedPrivateKey")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PublicKey")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Conversations");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.ConversationParticipant", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ConversationId")
                        .HasColumnType("int");

                    b.Property<string>("EncryptedConversationKey")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasRevealedPicture")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastReadAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("MessageCount")
                        .HasColumnType("int");

                    b.Property<string>("PublicKey")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId");

                    b.HasIndex("UserId");

                    b.ToTable("ConversationParticipants");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Interest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Interests");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "Music and concerts",
                            Name = "Music"
                        },
                        new
                        {
                            Id = 2,
                            Description = "Films and cinema",
                            Name = "Movies"
                        },
                        new
                        {
                            Id = 3,
                            Description = "Reading and literature",
                            Name = "Books"
                        },
                        new
                        {
                            Id = 4,
                            Description = "Sports and athletics",
                            Name = "Sports"
                        },
                        new
                        {
                            Id = 5,
                            Description = "Traveling and exploring",
                            Name = "Travel"
                        },
                        new
                        {
                            Id = 6,
                            Description = "Cooking and dining",
                            Name = "Food"
                        },
                        new
                        {
                            Id = 7,
                            Description = "Visual arts and museums",
                            Name = "Art"
                        },
                        new
                        {
                            Id = 8,
                            Description = "Taking and viewing photos",
                            Name = "Photography"
                        },
                        new
                        {
                            Id = 9,
                            Description = "Tech and gadgets",
                            Name = "Technology"
                        },
                        new
                        {
                            Id = 10,
                            Description = "Video and board games",
                            Name = "Gaming"
                        },
                        new
                        {
                            Id = 11,
                            Description = "Exercise and wellness",
                            Name = "Fitness"
                        },
                        new
                        {
                            Id = 12,
                            Description = "Clothing and style",
                            Name = "Fashion"
                        },
                        new
                        {
                            Id = 13,
                            Description = "Outdoors and wildlife",
                            Name = "Nature"
                        },
                        new
                        {
                            Id = 14,
                            Description = "Animals and pet care",
                            Name = "Pets"
                        },
                        new
                        {
                            Id = 15,
                            Description = "Food preparation",
                            Name = "Cooking"
                        },
                        new
                        {
                            Id = 16,
                            Description = "Dance and movement",
                            Name = "Dancing"
                        });
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Match", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("User1Id")
                        .HasColumnType("int");

                    b.Property<bool?>("User1Response")
                        .HasColumnType("bit");

                    b.Property<int>("User2Id")
                        .HasColumnType("int");

                    b.Property<bool?>("User2Response")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("User1Id");

                    b.HasIndex("User2Id");

                    b.ToTable("Matches");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Message", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<int?>("ConversationId")
                        .HasColumnType("int");

                    b.Property<string>("EncryptedContent")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("IV")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<int?>("MatchId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("SenderId")
                        .HasColumnType("int");

                    b.Property<DateTime>("SentAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId");

                    b.HasIndex("MatchId");

                    b.HasIndex("SenderId");

                    b.ToTable("Messages");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Profile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Bio")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("BirthDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Gender")
                        .HasColumnType("int");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("MaxAgePreference")
                        .HasColumnType("int");

                    b.Property<int>("MinAgePreference")
                        .HasColumnType("int");

                    b.Property<string>("Pictures")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("Preference")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("Profiles");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.ProfileInterest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("InterestId")
                        .HasColumnType("int");

                    b.Property<int>("ProfileId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("InterestId");

                    b.HasIndex("ProfileId");

                    b.ToTable("ProfileInterests");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Subscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ChatsPerDay")
                        .HasColumnType("int");

                    b.Property<int>("MaxInterests")
                        .HasColumnType("int");

                    b.Property<int>("MaxPictures")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PictureRevealCondition")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("Subscriptions");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ChatsPerDay = 10,
                            MaxInterests = 2,
                            MaxPictures = 2,
                            Name = "Free",
                            PictureRevealCondition = "Match",
                            Price = 0m
                        },
                        new
                        {
                            Id = 2,
                            ChatsPerDay = 30,
                            MaxInterests = 4,
                            MaxPictures = 4,
                            Name = "Premium",
                            PictureRevealCondition = "10 Messages",
                            Price = 4.99m
                        },
                        new
                        {
                            Id = 3,
                            ChatsPerDay = -1,
                            MaxInterests = 6,
                            MaxPictures = 6,
                            Name = "VIP",
                            PictureRevealCondition = "Immediate",
                            Price = 9.99m
                        });
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOnline")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastActiveAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.UserSubscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("SubscriptionId")
                        .HasColumnType("int");

                    b.Property<string>("TransactionId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserSubscriptions");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.ConversationParticipant", b =>
                {
                    b.HasOne("BlindDateApi.Data.Models.Conversation", "Conversation")
                        .WithMany("Participants")
                        .HasForeignKey("ConversationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlindDateApi.Data.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Conversation");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Match", b =>
                {
                    b.HasOne("BlindDateApi.Data.Models.User", "User1")
                        .WithMany("InitiatedMatches")
                        .HasForeignKey("User1Id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("BlindDateApi.Data.Models.User", "User2")
                        .WithMany("ReceivedMatches")
                        .HasForeignKey("User2Id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User1");

                    b.Navigation("User2");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Message", b =>
                {
                    b.HasOne("BlindDateApi.Data.Models.Conversation", "Conversation")
                        .WithMany("Messages")
                        .HasForeignKey("ConversationId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("BlindDateApi.Data.Models.Match", "Match")
                        .WithMany("Messages")
                        .HasForeignKey("MatchId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("BlindDateApi.Data.Models.User", "Sender")
                        .WithMany("SentMessages")
                        .HasForeignKey("SenderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Conversation");

                    b.Navigation("Match");

                    b.Navigation("Sender");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Profile", b =>
                {
                    b.HasOne("BlindDateApi.Data.Models.User", "User")
                        .WithOne("Profile")
                        .HasForeignKey("BlindDateApi.Data.Models.Profile", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.ProfileInterest", b =>
                {
                    b.HasOne("BlindDateApi.Data.Models.Interest", "Interest")
                        .WithMany("ProfileInterests")
                        .HasForeignKey("InterestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlindDateApi.Data.Models.Profile", "Profile")
                        .WithMany("ProfileInterests")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Interest");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.UserSubscription", b =>
                {
                    b.HasOne("BlindDateApi.Data.Models.Subscription", "Subscription")
                        .WithMany("Users")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BlindDateApi.Data.Models.User", "User")
                        .WithMany("Subscriptions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Conversation", b =>
                {
                    b.Navigation("Messages");

                    b.Navigation("Participants");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Interest", b =>
                {
                    b.Navigation("ProfileInterests");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Match", b =>
                {
                    b.Navigation("Messages");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Profile", b =>
                {
                    b.Navigation("ProfileInterests");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.Subscription", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("BlindDateApi.Data.Models.User", b =>
                {
                    b.Navigation("InitiatedMatches");

                    b.Navigation("Profile");

                    b.Navigation("ReceivedMatches");

                    b.Navigation("SentMessages");

                    b.Navigation("Subscriptions");
                });
#pragma warning restore 612, 618
        }
    }
}
