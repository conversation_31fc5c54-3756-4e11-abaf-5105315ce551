using System.Collections.Generic;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.DTOs;

namespace BlindDateApi.Services
{
    public interface IChatService
    {
        Task<ConversationDto> CreateConversationAsync(int userId, CreateConversationDto dto);
        Task<List<ConversationDto>> GetConversationsAsync(int userId);
        Task<ConversationDto> GetConversationAsync(int userId, int conversationId);
        Task<List<ChatMessageDto>> GetMessagesAsync(int userId, int conversationId, int? lastMessageId = null, int count = 20);
        Task<ChatMessageDto> SendMessageAsync(int userId, SendChatMessageDto dto);
        Task<bool> MarkAsReadAsync(int userId, MarkAsReadDto dto);
        Task<bool> RequestRevealAsync(int userId, RevealRequestDto dto);
        Task<bool> RespondToRevealAsync(int userId, RevealResponseDto dto);
        Task<bool> DeleteConversationAsync(int userId, int conversationId);
        Task<bool> IsConversationExpiredAsync(int conversationId);
        Task<int> GetRemainingChatsAsync(int userId);
        Task<bool> HasReachedChatLimitAsync(int userId);
        Task<bool> UpdateMessagesWithMatchIdAsync(int conversationId, int matchId, int userId);
    }
}
