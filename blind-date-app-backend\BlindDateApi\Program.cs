using System.Text;
using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.Helpers;
using BlindDateApi.Hubs;
using BlindDateApi.Middleware;
using BlindDateApi.Repositories;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
var configuration = builder.Configuration;

// Configure JWT settings
var jwtSettingsSection = configuration.GetSection("JwtSettings");
builder.Services.Configure<JwtSettings>(jwtSettingsSection);

// Configure JWT authentication
var jwtSettings = jwtSettingsSection.Get<JwtSettings>() ?? new JwtSettings();
var key = Encoding.ASCII.GetBytes(jwtSettings.Secret);
builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(x =>
{
    x.RequireHttpsMetadata = false;
    x.SaveToken = true;
    x.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = jwtSettings.Audience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// Configure database
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));

// Register repositories
builder.Services.AddScoped(typeof(Repository<>));

// Register the generic repository with caching
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

// Register specific repositories with caching for frequently accessed entities
builder.Services.AddScoped<IRepository<Profile>>(provider =>
{
    var repository = provider.GetRequiredService<Repository<Profile>>();
    var cacheService = provider.GetRequiredService<ICacheService>();
    var logger = provider.GetRequiredService<ILogger<CachedRepository<Profile>>>();
    return new CachedRepository<Profile>(repository, cacheService, logger);
});

builder.Services.AddScoped<IRepository<Interest>>(provider =>
{
    var repository = provider.GetRequiredService<Repository<Interest>>();
    var cacheService = provider.GetRequiredService<ICacheService>();
    var logger = provider.GetRequiredService<ILogger<CachedRepository<Interest>>>();
    return new CachedRepository<Interest>(repository, cacheService, logger);
});

builder.Services.AddScoped<IRepository<Subscription>>(provider =>
{
    var repository = provider.GetRequiredService<Repository<Subscription>>();
    var cacheService = provider.GetRequiredService<ICacheService>();
    var logger = provider.GetRequiredService<ILogger<CachedRepository<Subscription>>>();
    return new CachedRepository<Subscription>(repository, cacheService, logger);
});

// Register the base UserRepository
builder.Services.AddScoped<UserRepository>();

// Register the CachedUserRepository as the implementation of IUserRepository
builder.Services.AddScoped<IUserRepository>(provider =>
{
    var userRepository = provider.GetRequiredService<UserRepository>();
    var cacheService = provider.GetRequiredService<ICacheService>();
    var logger = provider.GetRequiredService<ILogger<CachedUserRepository>>();
    return new CachedUserRepository(userRepository, cacheService, logger);
});

// Add SignalR
builder.Services.AddSignalR();

// Register services
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IEncryptionService, EncryptionService>();
builder.Services.AddScoped<IPasswordValidationService, PasswordValidationService>();
builder.Services.AddScoped<IChatNotificationService, SignalRChatNotificationService>();
builder.Services.AddScoped<IChatService, ChatService>();
builder.Services.AddScoped<IMatchService, MatchService>();
builder.Services.AddScoped<IOnlineStatusService, OnlineStatusService>();
builder.Services.AddScoped<ISearchingUsersService, SearchingUsersService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IGeoLocationService, GeoLocationService>();
builder.Services.AddScoped<IStripeService, StripeService>();
builder.Services.AddScoped<IS3Service, S3Service>();

// Register background services
builder.Services.AddHostedService<OnlineStatusCleanupService>();
builder.Services.AddHostedService<SearchingUsersCleanupService>();

// Register caching services
builder.Services.AddMemoryCache(); // Add in-memory cache
builder.Services.AddScoped<ICacheService, MemoryCacheService>(); // Use memory cache by default

// Configure Redis cache if enabled
var redisConnectionString = builder.Configuration.GetConnectionString("Redis");
if (!string.IsNullOrEmpty(redisConnectionString))
{
    builder.Services.AddStackExchangeRedisCache(options =>
    {
        options.Configuration = redisConnectionString;
        options.InstanceName = "BlindDateApi:";
    });

    // Override the cache service with Redis implementation when Redis is available
    builder.Services.AddScoped<ICacheService, RedisCacheService>();
}

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowVueApp", builder =>
    {
        builder.WithOrigins("http://localhost:5173", "https://blyndx.com", "http://blyndx.com", "https://www.blyndx.com", "http://www.blyndx.com")
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials();
    });
});

// Add HttpContextAccessor
builder.Services.AddHttpContextAccessor();

builder.Services.AddControllers();

// Configure Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Blind Date API", Version = "v1" });

    // Configure Swagger to use JWT Authentication
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

var app = builder.Build();

// Initialize the database
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        // Initialize the database with seed data
        await BlindDateApi.Data.Seed.DbInitializer.Initialize(services);
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while initializing the database.");
    }
}

// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment())
//{
    app.UseSwagger();
    app.UseSwaggerUI();
//}

app.UseHttpsRedirection();

// Use CORS
app.UseCors("AllowVueApp");

// Use JWT middleware
app.UseJwtMiddleware();

// Use authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Map SignalR hubs
app.MapHub<ChatHub>("/hubs/chat", options =>
{
    options.Transports =
        HttpTransportType.WebSockets |
        HttpTransportType.LongPolling;
});

app.Run();
