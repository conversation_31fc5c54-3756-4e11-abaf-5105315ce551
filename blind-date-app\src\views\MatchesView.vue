<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useMatchesStore } from '../stores/matches'

const router = useRouter()
const authStore = useAuthStore()
const matchesStore = useMatchesStore()

const isLoading = ref(false)
const errorMessage = ref('')
const matches = ref([])
const mobileMenu = ref(false)

onMounted(async () => {
  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''

    // Fetch all matches
    matches.value = await matchesStore.fetchAllMatches()
  } catch (error) {
    errorMessage.value = error.message || 'Failed to load matches'
  } finally {
    isLoading.value = false
  }
})

const navigateToChat = (matchId) => {
  // Navigate to chat without the source=matching parameter
  // This ensures the timer won't show for existing matches
  router.push(`/chat/${matchId}`)
}

const navigateToHome = () => {
  router.push('/home')
}

const navigateToProfile = () => {
  router.push('/profile')
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}
</script>

<template>
  <v-layout>
    <!-- App Bar -->
    <v-app-bar color="white" elevation="1">
      <v-app-bar-title class="text-primary font-weight-bold">Blind Date</v-app-bar-title>

      <v-spacer></v-spacer>

      <!-- Desktop Navigation -->
      <div class="d-none d-sm-flex">
        <v-btn variant="text" @click="navigateToHome">
          <v-icon start>mdi-home</v-icon>
          Home
        </v-btn>

        <v-btn variant="text" @click="navigateToProfile">
          <v-icon start>mdi-account</v-icon>
          Profile
        </v-btn>

        <v-btn variant="text" @click="logout">
          <v-icon start>mdi-logout</v-icon>
          Logout
        </v-btn>
      </div>

      <!-- Mobile Navigation -->
      <v-menu v-model="mobileMenu">
        <template v-slot:activator="{ props }">
          <v-btn
            icon
            v-bind="props"
            class="d-sm-none"
          >
            <v-icon>mdi-menu</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item @click="navigateToHome">
            <template v-slot:prepend>
              <v-icon>mdi-home</v-icon>
            </template>
            <v-list-item-title>Home</v-list-item-title>
          </v-list-item>

          <v-list-item @click="navigateToProfile">
            <template v-slot:prepend>
              <v-icon>mdi-account</v-icon>
            </template>
            <v-list-item-title>Profile</v-list-item-title>
          </v-list-item>

          <v-list-item @click="logout">
            <template v-slot:prepend>
              <v-icon>mdi-logout</v-icon>
            </template>
            <v-list-item-title>Logout</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <v-container>
        <v-row justify="center">
          <v-col cols="12" sm="10" md="8">
            <v-card class="mb-6">
              <v-card-title class="text-h5">Your Matches</v-card-title>
              <v-card-subtitle>Continue chatting with your matches</v-card-subtitle>

              <v-card-text>
                <v-alert
                  v-if="errorMessage"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                >
                  {{ errorMessage }}
                </v-alert>

                <v-progress-circular
                  v-if="isLoading"
                  indeterminate
                  color="primary"
                  size="64"
                  class="mx-auto my-12 d-block"
                ></v-progress-circular>

                <div v-else-if="matches.length === 0" class="text-center py-8">
                  <v-icon size="large" color="grey-lighten-1" class="mb-4">mdi-account-multiple</v-icon>
                  <div class="text-h6 text-grey">No matches yet</div>
                  <div class="text-body-2 text-grey-darken-1 mt-2">
                    Start searching to find your perfect match!
                  </div>
                </div>

                <v-list v-else lines="two">
                  <v-list-subheader>
                    {{ matches.length }} {{ matches.length === 1 ? 'match' : 'matches' }} found
                  </v-list-subheader>

                  <v-list-item
                    v-for="match in matches"
                    :key="match.id"
                    :title="`${match.firstName} ${match.lastName}`"
                    @click="navigateToChat(match.id)"
                  >
                    <template v-slot:subtitle>
                      <div class="d-flex align-center">
                        <span v-if="match.lastMessage || match.lastMessagePreview" class="text-truncate">
                          {{ match.lastMessage || match.lastMessagePreview }}
                        </span>
                        <span v-else>Continue conversation</span>

                        <v-spacer></v-spacer>

                        <!-- Online status indicator removed from here -->

                        <!-- Distance indicator if available -->
                        <v-chip
                          v-if="match.distance"
                          size="x-small"
                          class="ml-2"
                          color="primary"
                          variant="outlined"
                        >
                          {{ match.distance }} km
                        </v-chip>
                      </div>
                    </template>
                    <template v-slot:prepend>
                      <v-avatar color="primary" v-if="match.revealPicture && match.pictures && match.pictures.length > 0">
                        <v-img :src="match.pictures[0]" alt="Profile picture"></v-img>
                      </v-avatar>
                      <v-avatar color="grey-lighten-1" v-else>
                        <v-icon color="white">mdi-account</v-icon>
                      </v-avatar>
                    </template>

                    <template v-slot:append>
                      <v-chip
                        v-if="match.unreadCount"
                        color="primary"
                        size="small"
                        class="mr-2"
                      >
                        {{ match.unreadCount }}
                      </v-chip>

                      <!-- Online status indicator -->
                      <v-tooltip location="bottom">
                        <template v-slot:activator="{ props }">
                          <v-icon
                            v-bind="props"
                            :color="match.isOnline ? 'success' : 'grey'"
                            size="small"
                            class="mr-2"
                          >
                            mdi-circle
                          </v-icon>
                        </template>
                        <span>{{ match.isOnline ? 'Online' : 'Offline' }}</span>
                      </v-tooltip>

                      <v-icon color="grey">mdi-chevron-right</v-icon>
                    </template>
                  </v-list-item>
                </v-list>
              </v-card-text>
            </v-card>

            <v-card>
              <v-card-text class="text-center">
                <v-btn
                  color="primary"
                  variant="elevated"
                  size="large"
                  @click="navigateToHome"
                >
                  <v-icon start>mdi-magnify</v-icon>
                  Find New Matches
                </v-btn>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  </v-layout>
</template>

<style scoped>
.v-list-item {
  cursor: pointer;
  transition: background-color 0.2s;
}

.v-list-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.05);
}
</style>
