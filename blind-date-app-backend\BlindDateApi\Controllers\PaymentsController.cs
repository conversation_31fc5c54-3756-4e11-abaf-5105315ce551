using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PaymentsController : ControllerBase
    {
        private readonly IStripeService _stripeService;
        private readonly IRepository<Subscription> _subscriptionRepository;
        private readonly IUserRepository _userRepository;
        private readonly ILogger<PaymentsController> _logger;
        private readonly IConfiguration _configuration;

        public PaymentsController(
            IStripeService stripeService,
            IRepository<Subscription> subscriptionRepository,
            IUserRepository userRepository,
            ILogger<PaymentsController> logger,
            IConfiguration configuration)
        {
            _stripeService = stripeService;
            _subscriptionRepository = subscriptionRepository;
            _userRepository = userRepository;
            _logger = logger;
            _configuration = configuration;
        }

        [HttpGet("config")]
        public ActionResult GetConfig()
        {
            return Ok(new
            {
                PublishableKey = _configuration["Stripe:PublishableKey"]
            });
        }

        [HttpPost("create-payment-intent")]
        [Authorize]
        public async Task<ActionResult> CreatePaymentIntent(SubscriptionPurchaseDto purchaseDto)
        {
            try
            {
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return Unauthorized();

                var clientSecret = await _stripeService.CreatePaymentIntentAsync(purchaseDto.SubscriptionId, userId);
                
                return Ok(new { clientSecret });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment intent");
                return StatusCode(500, "An error occurred while creating payment intent");
            }
        }

        [HttpPost("confirm-payment")]
        [Authorize]
        public async Task<ActionResult<UserSubscriptionDto>> ConfirmPayment(PaymentConfirmationDto confirmationDto)
        {
            try
            {
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return Unauthorized();

                var subscription = await _stripeService.ConfirmPaymentAsync(confirmationDto.PaymentIntentId, userId);
                
                return Ok(subscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming payment");
                return StatusCode(500, "An error occurred while confirming payment");
            }
        }

        [HttpPost("cancel-subscription")]
        [Authorize]
        public async Task<ActionResult> CancelSubscription()
        {
            try
            {
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return Unauthorized();

                var result = await _stripeService.CancelSubscriptionAsync(userId);
                
                if (result)
                {
                    return Ok(new { success = true, message = "Subscription cancelled successfully" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "No active subscription to cancel" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling subscription");
                return StatusCode(500, "An error occurred while cancelling subscription");
            }
        }
    }
}
