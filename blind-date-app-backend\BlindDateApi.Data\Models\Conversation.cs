using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlindDateApi.Data.Models
{
    public class Conversation
    {
        [Key]
        public int Id { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ExpiresAt { get; set; }

        public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow && MatchId == null;

        // Reference to a Match if this conversation is associated with a match
        public int? MatchId { get; set; }

        // Navigation properties
        public virtual ICollection<Message> Messages { get; set; } = new List<Message>();

        public virtual ICollection<ConversationParticipant> Participants { get; set; } = new List<ConversationParticipant>();

        // Encryption keys for the conversation
        public string PublicKey { get; set; } = string.Empty;

        // This is stored encrypted with each participant's public key
        public string EncryptedPrivateKey { get; set; } = string.Empty;
    }

    public class ConversationParticipant
    {
        [Key]
        public int Id { get; set; }

        public int ConversationId { get; set; }

        public int UserId { get; set; }

        // The conversation's private key encrypted with this user's public key
        public string EncryptedConversationKey { get; set; } = string.Empty;

        // Navigation properties
        [ForeignKey("ConversationId")]
        public virtual Conversation? Conversation { get; set; }

        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        // User's public key for this conversation
        public string PublicKey { get; set; } = string.Empty;

        // Last time the user read messages in this conversation
        public DateTime? LastReadAt { get; set; }

        // Whether the user has revealed their profile picture
        public bool HasRevealedPicture { get; set; } = false;

        // Number of messages sent in this conversation by this user
        public int MessageCount { get; set; } = 0;
    }
}
