{
  "ConnectionStrings": {
    "DefaultConnection": "Server=blyndxdb.cyn06ea4ogj7.us-east-1.rds.amazonaws.com,1433;Initial Catalog=blyndxdb;MultipleActiveResultSets=true;TrustServerCertificate=True;User ID=admin;Password=*************",
    "Redis": "" // Empty to use in-memory cache in development
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "BlindDateApi.Services": "Debug" // Enable debug logging for cache services
    }
  }
}
