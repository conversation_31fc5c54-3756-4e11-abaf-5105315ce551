using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using BlindDateApi.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InterestsController : ControllerBase
    {
        private readonly IRepository<Interest> _interestRepository;
        private readonly ILogger<InterestsController> _logger;

        public InterestsController(
            IRepository<Interest> interestRepository,
            ILogger<InterestsController> logger)
        {
            _interestRepository = interestRepository;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<InterestDto>>> GetAllInterests()
        {
            var interests = await _interestRepository.GetAllAsync();

            var interestDtos = interests.Select(i => new InterestDto
            {
                Id = i.Id,
                Name = i.Name,
                Description = i.Description
            }).ToList();

            return Ok(interestDtos);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<InterestDto>> GetInterest(int id)
        {
            var interest = await _interestRepository.GetByIdAsync(id);

            if (interest == null)
                return NotFound();

            var interestDto = new InterestDto
            {
                Id = interest.Id,
                Name = interest.Name,
                Description = interest.Description
            };

            return Ok(interestDto);
        }
    }
}
