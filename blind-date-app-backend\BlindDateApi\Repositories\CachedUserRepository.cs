using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Repositories
{
    public class CachedUserRepository : IUserRepository
    {
        private readonly IUserRepository _userRepository;
        private readonly ICacheService _cacheService;
        private readonly ILogger<CachedUserRepository> _logger;

        // Cache keys
        private const string UserByEmailKey = "User_Email_{0}";
        private const string UserWithProfileKey = "User_Profile_{0}";
        private const string UserWithSubscriptionsKey = "User_Subscriptions_{0}";

        // Cache expiration times
        private readonly TimeSpan _userCacheTime = TimeSpan.FromMinutes(10);
        private readonly TimeSpan _profileCacheTime = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _subscriptionCacheTime = TimeSpan.FromMinutes(15);

        public CachedUserRepository(
            IUserRepository userRepository,
            ICacheService cacheService,
            ILogger<CachedUserRepository> logger)
        {
            _userRepository = userRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task<User> GetUserByEmailAsync(string email)
        {
            var cacheKey = string.Format(UserByEmailKey, email);
            var cachedUser = await _cacheService.GetAsync<User>(cacheKey);

            if (cachedUser != null)
            {
                _logger.LogDebug("User with email {Email} retrieved from cache", email);
                return cachedUser;
            }

            var user = await _userRepository.GetUserByEmailAsync(email);
            if (user != null)
            {
                await _cacheService.SetAsync(cacheKey, user, _userCacheTime);
                _logger.LogDebug("User with email {Email} added to cache", email);
            }

            return user;
        }

        public async Task<User> GetUserWithProfileAsync(int userId)
        {
            var cacheKey = string.Format(UserWithProfileKey, userId);
            var cachedUser = await _cacheService.GetAsync<User>(cacheKey);

            if (cachedUser != null)
            {
                _logger.LogDebug("User with profile for ID {UserId} retrieved from cache", userId);
                return cachedUser;
            }

            var user = await _userRepository.GetUserWithProfileAsync(userId);
            if (user != null)
            {
                await _cacheService.SetAsync(cacheKey, user, _profileCacheTime);
                _logger.LogDebug("User with profile for ID {UserId} added to cache", userId);
            }

            return user;
        }

        public async Task<User> GetUserWithSubscriptionsAsync(int userId)
        {
            var cacheKey = string.Format(UserWithSubscriptionsKey, userId);
            var cachedUser = await _cacheService.GetAsync<User>(cacheKey);

            if (cachedUser != null)
            {
                _logger.LogDebug("User with subscriptions for ID {UserId} retrieved from cache", userId);
                return cachedUser;
            }

            var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
            if (user != null)
            {
                await _cacheService.SetAsync(cacheKey, user, _subscriptionCacheTime);
                _logger.LogDebug("User with subscriptions for ID {UserId} added to cache", userId);
            }

            return user;
        }

        // IRepository<User> implementation - delegate to the wrapped repository
        public async Task<IEnumerable<User>> GetAllAsync(Expression<Func<User, bool>>? predicate = null, Func<IQueryable<User>, IIncludableQueryable<User, object>>? include = null)
        {
            return await _userRepository.GetAllAsync(predicate, include);
        }

        public async Task<IEnumerable<User>> FindAsync(Expression<Func<User, bool>> predicate, Func<IQueryable<User>, IIncludableQueryable<User, object>>? include = null)
        {
            return await _userRepository.FindAsync(predicate, include);
        }

        public async Task<User> GetByIdAsync(int id)
        {
            return await _userRepository.GetByIdAsync(id);
        }

        public async Task<User?> SingleOrDefaultAsync(Expression<Func<User, bool>> predicate, Func<IQueryable<User>, IIncludableQueryable<User, object>>? include = null)
        {
            return await _userRepository.SingleOrDefaultAsync(predicate, include);
        }

        public async Task AddAsync(User entity)
        {
            await _userRepository.AddAsync(entity);

            // Invalidate cache
            if (!string.IsNullOrEmpty(entity.Email))
            {
                var cacheKey = string.Format(UserByEmailKey, entity.Email);
                await _cacheService.RemoveAsync(cacheKey);
            }
        }

        public async Task AddRangeAsync(IEnumerable<User> entities)
        {
            await _userRepository.AddRangeAsync(entities);

            // Invalidate cache for each entity
            foreach (var entity in entities)
            {
                if (!string.IsNullOrEmpty(entity.Email))
                {
                    var cacheKey = string.Format(UserByEmailKey, entity.Email);
                    await _cacheService.RemoveAsync(cacheKey);
                }
            }
        }

        public void Update(User entity)
        {
            _userRepository.Update(entity);

            // Invalidate cache
            if (!string.IsNullOrEmpty(entity.Email))
            {
                var cacheKey = string.Format(UserByEmailKey, entity.Email);
                _cacheService.Remove(cacheKey);
            }

            var userProfileCacheKey = string.Format(UserWithProfileKey, entity.Id);
            _cacheService.Remove(userProfileCacheKey);

            var userSubscriptionsCacheKey = string.Format(UserWithSubscriptionsKey, entity.Id);
            _cacheService.Remove(userSubscriptionsCacheKey);
        }

        public void Remove(User entity)
        {
            _userRepository.Remove(entity);

            // Invalidate cache
            if (!string.IsNullOrEmpty(entity.Email))
            {
                var cacheKey = string.Format(UserByEmailKey, entity.Email);
                _cacheService.Remove(cacheKey);
            }

            var userProfileCacheKey = string.Format(UserWithProfileKey, entity.Id);
            _cacheService.Remove(userProfileCacheKey);

            var userSubscriptionsCacheKey = string.Format(UserWithSubscriptionsKey, entity.Id);
            _cacheService.Remove(userSubscriptionsCacheKey);
        }

        public void RemoveRange(IEnumerable<User> entities)
        {
            _userRepository.RemoveRange(entities);

            // Invalidate cache for each entity
            foreach (var entity in entities)
            {
                if (!string.IsNullOrEmpty(entity.Email))
                {
                    var cacheKey = string.Format(UserByEmailKey, entity.Email);
                    _cacheService.Remove(cacheKey);
                }

                var userProfileCacheKey = string.Format(UserWithProfileKey, entity.Id);
                _cacheService.Remove(userProfileCacheKey);

                var userSubscriptionsCacheKey = string.Format(UserWithSubscriptionsKey, entity.Id);
                _cacheService.Remove(userSubscriptionsCacheKey);
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            // Get the user first to invalidate cache
            var user = await GetByIdAsync(id);
            if (user != null)
            {
                var result = await _userRepository.DeleteAsync(id);

                if (result)
                {
                    // Invalidate cache
                    if (!string.IsNullOrEmpty(user.Email))
                    {
                        var cacheKey = string.Format(UserByEmailKey, user.Email);
                        await _cacheService.RemoveAsync(cacheKey);
                    }

                    var userProfileCacheKey = string.Format(UserWithProfileKey, id);
                    await _cacheService.RemoveAsync(userProfileCacheKey);

                    var userSubscriptionsCacheKey = string.Format(UserWithSubscriptionsKey, id);
                    await _cacheService.RemoveAsync(userSubscriptionsCacheKey);
                }

                return result;
            }
            else
            {
                return await _userRepository.DeleteAsync(id);
            }
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _userRepository.SaveChangesAsync();
        }

        public async Task<IDbContextTransaction> BeginTransactionAsync()
        {
            return await _userRepository.BeginTransactionAsync();
        }
    }
}
