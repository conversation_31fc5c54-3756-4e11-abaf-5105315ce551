using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using BlindDateApi.Data.Models;

namespace BlindDateApi.DTOs
{
    public class ConversationDto
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public bool IsExpired { get; set; }
        public int? MatchId { get; set; } // Added MatchId property
        public List<ParticipantDto> Participants { get; set; } = new List<ParticipantDto>();
        public List<int> ParticipantIds => Participants.Select(p => p.UserId).ToList();
        public ChatMessageDto? LastMessage { get; set; }
        public int UnreadCount { get; set; }
        public string PublicKey { get; set; } = string.Empty;
    }

    public class ParticipantDto
    {
        public int UserId { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string? LastName { get; set; }
        public int Age { get; set; }
        public string? ProfilePictureUrl { get; set; }
        public bool HasRevealedPicture { get; set; }
        public string PublicKey { get; set; } = string.Empty;
        public DateTime? LastReadAt { get; set; }
        public int MessageCount { get; set; }
    }

    public class ChatMessageDto
    {
        public int Id { get; set; }
        public int ConversationId { get; set; }
        public int SenderId { get; set; }
        public string SenderName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime SentAt { get; set; }
        public string EncryptedContent { get; set; } = string.Empty;
        public string EncryptedKey { get; set; } = string.Empty;
        public string IV { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public MessageType Type { get; set; }
        public List<int> ParticipantIds { get; set; } = new List<int>();
    }

    public class CreateConversationDto
    {
        [Required]
        public List<int> ParticipantIds { get; set; } = new List<int>();

        [Required]
        public string PublicKey { get; set; } = string.Empty;

        public Dictionary<int, string> EncryptedKeys { get; set; } = new Dictionary<int, string>();
    }

    public class SendChatMessageDto
    {
        [Required]
        public int ConversationId { get; set; }

        [Required]
        [MaxLength(2000)]
        public string EncryptedContent { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string EncryptedKey { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string Content { get; set; } = string.Empty;

        [Required]
        public string IV { get; set; } = string.Empty;

        public MessageType Type { get; set; } = MessageType.Text;
    }

    public class RevealRequestDto
    {
        [Required]
        public int ConversationId { get; set; }

        [Required]
        public int RequestedUserId { get; set; }
    }

    public class RevealResponseDto
    {
        [Required]
        public int ConversationId { get; set; }

        [Required]
        public bool Accept { get; set; }
    }

    public class MarkAsReadDto
    {
        [Required]
        public int ConversationId { get; set; }

        public int? LastMessageId { get; set; }
    }
}
