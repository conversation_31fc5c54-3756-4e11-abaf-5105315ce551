using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Threading.Tasks;
using BlindDateApi.Controllers;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlindDateApi.Tests.Controllers
{
    public class ProfilesControllerTests
    {
        private readonly Mock<IRepository<Profile>> _mockProfileRepository;
        private readonly Mock<IRepository<Interest>> _mockInterestRepository;
        private readonly Mock<IRepository<ProfileInterest>> _mockProfileInterestRepository;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<ILogger<ProfilesController>> _mockLogger;
        private readonly ProfilesController _controller;

        public ProfilesControllerTests()
        {
            _mockProfileRepository = new Mock<IRepository<Profile>>();
            _mockInterestRepository = new Mock<IRepository<Interest>>();
            _mockProfileInterestRepository = new Mock<IRepository<ProfileInterest>>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockLogger = new Mock<ILogger<ProfilesController>>();

            _controller = new ProfilesController(
                _mockProfileRepository.Object,
                _mockInterestRepository.Object,
                _mockProfileInterestRepository.Object,
                _mockUserRepository.Object,
                _mockLogger.Object
            );

            // Setup HttpContext with a User
            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                Profile = new Profile
                {
                    Id = 1,
                    UserId = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    BirthDate = new DateTime(1990, 1, 1),
                    Gender = 1,
                    Preference = 2
                }
            };

            var httpContext = new DefaultHttpContext();
            httpContext.Items["User"] = user;

            // Setup claims principal
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, "1"),
                new Claim(ClaimTypes.Name, "<EMAIL>")
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var claimsPrincipal = new ClaimsPrincipal(identity);
            httpContext.User = claimsPrincipal;

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };
        }

        [Fact]
        public async Task GetMyProfile_UserHasProfile_ReturnsOk()
        {
            // Arrange
            var profile = new Profile
            {
                Id = 1,
                UserId = 1,
                FirstName = "John",
                LastName = "Doe",
                BirthDate = new DateTime(1990, 1, 1),
                Gender = 1,
                Preference = 2
            };

            _mockProfileRepository
                .Setup(repo => repo.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<Profile, bool>>>(),
                    It.IsAny<Func<IQueryable<Profile>, Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<Profile, object>>>()))
                .ReturnsAsync(profile);

            // Act
            var result = await _controller.GetMyProfile();

            // Assert
            var actionResult = Assert.IsType<ActionResult<ProfileDto>>(result);
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var profileDto = Assert.IsType<ProfileDto>(okResult.Value);
            Assert.Equal("John", profileDto.FirstName);
            Assert.Equal("Doe", profileDto.LastName);
            Assert.Equal(1, profileDto.Gender);
            Assert.Equal(2, profileDto.Preference);
        }

        [Fact]
        public async Task GetMyProfile_UserHasNoProfile_ReturnsNotFound()
        {
            // Arrange
            _mockProfileRepository
                .Setup(repo => repo.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<Profile, bool>>>(),
                    It.IsAny<Func<IQueryable<Profile>, Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<Profile, object>>>()))
                .ReturnsAsync((Profile)null);

            // Act
            var result = await _controller.GetMyProfile();

            // Assert
            var actionResult = Assert.IsType<ActionResult<ProfileDto>>(result);
            Assert.IsType<NotFoundResult>(actionResult.Result);
        }

        [Fact]
        public async Task UpdateMyProfile_ValidData_ReturnsOk()
        {
            // Arrange
            var updateProfileDto = new UpdateProfileDto
            {
                FirstName = "Updated",
                LastName = "Name",
                Bio = "Updated bio",
                Gender = 2,
                Preference = 1
            };

            var existingProfile = new Profile
            {
                Id = 1,
                UserId = 1,
                FirstName = "John",
                LastName = "Doe",
                BirthDate = new DateTime(1990, 1, 1),
                Gender = 1,
                Preference = 2
            };

            // Setup user with subscription in the HttpContext
            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>"
            };

            var subscription = new Subscription
            {
                Id = 1,
                Name = "Free",
                MaxInterests = 5,
                MaxPictures = 2
            };

            var userSubscription = new UserSubscription
            {
                UserId = 1,
                SubscriptionId = 1,
                Subscription = subscription
            };

            // Set up the subscription to be current (not expired)
            userSubscription.ExpiresAt = DateTime.UtcNow.AddDays(30); // Expires in 30 days
            user.Subscriptions = new List<UserSubscription> { userSubscription };

            var httpContext = new DefaultHttpContext();
            httpContext.Items["User"] = user;

            // Setup claims principal
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, "1"),
                new Claim(ClaimTypes.Name, "<EMAIL>")
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var claimsPrincipal = new ClaimsPrincipal(identity);
            httpContext.User = claimsPrincipal;

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            // Setup mock for GetUserWithSubscriptionsAsync
            _mockUserRepository.Setup(r => r.GetUserWithSubscriptionsAsync(1))
                .ReturnsAsync(user);

            _mockProfileRepository
                .Setup(repo => repo.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<Profile, bool>>>(),
                    It.IsAny<Func<IQueryable<Profile>, Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<Profile, object>>>()))
                .ReturnsAsync(existingProfile);

            // Act
            var result = await _controller.UpdateMyProfile(updateProfileDto);

            // Assert
            Assert.IsType<NoContentResult>(result);

            // Verify the repository was called to update the profile
            _mockProfileRepository.Verify(repo => repo.Update(It.IsAny<Profile>()), Times.Once);
            _mockProfileRepository.Verify(repo => repo.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateMyProfile_ProfileNotFound_ReturnsNotFound()
        {
            // Arrange
            var updateProfileDto = new UpdateProfileDto
            {
                FirstName = "Updated",
                LastName = "Name"
            };

            _mockProfileRepository
                .Setup(repo => repo.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<Profile, bool>>>(),
                    It.IsAny<Func<IQueryable<Profile>, Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<Profile, object>>>()))
                .ReturnsAsync((Profile)null);

            // Act
            var result = await _controller.UpdateMyProfile(updateProfileDto);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task UpdateMyProfile_NoUser_ReturnsUnauthorized()
        {
            // Arrange
            var updateProfileDto = new UpdateProfileDto
            {
                FirstName = "Updated",
                LastName = "Name"
            };

            // Setup HttpContext without a User
            var httpContext = new DefaultHttpContext();
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            // Act
            var result = await _controller.UpdateMyProfile(updateProfileDto);

            // Assert
            Assert.IsType<UnauthorizedResult>(result);
        }
    }
}
