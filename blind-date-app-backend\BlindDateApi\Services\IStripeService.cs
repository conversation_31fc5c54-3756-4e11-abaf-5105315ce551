using BlindDateApi.DTOs;
using Stripe;
using System.Threading.Tasks;

namespace BlindDateApi.Services
{
    public interface IStripeService
    {
        /// <summary>
        /// Creates a payment intent for a subscription
        /// </summary>
        /// <param name="subscriptionId">The subscription ID</param>
        /// <param name="userId">The user ID</param>
        /// <returns>Payment intent client secret</returns>
        Task<string> CreatePaymentIntentAsync(int subscriptionId, int userId);
        
        /// <summary>
        /// Confirms a payment and creates a subscription
        /// </summary>
        /// <param name="paymentIntentId">The payment intent ID</param>
        /// <param name="userId">The user ID</param>
        /// <returns>The created user subscription</returns>
        Task<UserSubscriptionDto> ConfirmPaymentAsync(string paymentIntentId, int userId);
        
        /// <summary>
        /// Cancels a subscription
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>True if successful</returns>
        Task<bool> CancelSubscriptionAsync(int userId);
        
        /// <summary>
        /// Creates a Stripe customer for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="email">The user's email</param>
        /// <returns>The Stripe customer ID</returns>
        Task<string> CreateCustomerAsync(int userId, string email);
        
        /// <summary>
        /// Gets a Stripe customer by user ID
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>The Stripe customer</returns>
        Task<Customer> GetCustomerAsync(int userId);
    }
}
