using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.Services;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Logging;
using Moq;
using Match = BlindDateApi.Data.Models.Match;
using User = BlindDateApi.Data.Models.User;
using Profile = BlindDateApi.Data.Models.Profile;

namespace BlindDateApi.Tests.Services
{
    public class OnlineMatchingTests
    {
        private readonly Mock<IRepository<Match>> _mockMatchRepository;
        private readonly Mock<IRepository<Profile>> _mockProfileRepository;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<ICacheService> _mockCacheService;
        private readonly Mock<IOnlineStatusService> _mockOnlineStatusService;
        private readonly Mock<IGeoLocationService> _mockGeoLocationService;
        private readonly Mock<ILogger<MatchService>> _mockLogger;
        private readonly MatchService _matchService;

        public OnlineMatchingTests()
        {
            _mockMatchRepository = new Mock<IRepository<Match>>();
            _mockProfileRepository = new Mock<IRepository<Profile>>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockCacheService = new Mock<ICacheService>();
            _mockOnlineStatusService = new Mock<IOnlineStatusService>();
            _mockGeoLocationService = new Mock<IGeoLocationService>();
            _mockLogger = new Mock<ILogger<MatchService>>();

            // Setup GeoLocationService mock
            _mockGeoLocationService.Setup(g => g.CalculateDistance(
                It.IsAny<double>(), It.IsAny<double>(),
                It.IsAny<double>(), It.IsAny<double>()))
                .Returns(10.0); // Default distance of 10km

            _mockGeoLocationService.Setup(g => g.IsWithinDistance(
                It.IsAny<double>(), It.IsAny<double>(),
                It.IsAny<double>(), It.IsAny<double>(),
                It.IsAny<int>()))
                .Returns(true); // Default to within distance

            _matchService = new MatchService(
                _mockMatchRepository.Object,
                _mockProfileRepository.Object,
                _mockUserRepository.Object,
                _mockCacheService.Object,
                _mockOnlineStatusService.Object,
                _mockGeoLocationService.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task OnlineUser_ShouldFindMatch_WithOtherOnlineUser()
        {
            // Arrange
            // Create two users with compatible preferences
            var user1 = new User
            {
                Id = 1,
                IsOnline = true,
                IsActive = true,
                Profile = new Profile
                {
                    UserId = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    BirthDate = DateTime.UtcNow.AddYears(-30),
                    Gender = 1, // Male
                    Preference = 2, // Prefers women
                    MinAgePreference = 20,
                    MaxAgePreference = 40,
                    Pictures = "profile1.jpg",
                    Latitude = 40.7128, // New York
                    Longitude = -74.0060,
                    MaxDistance = 50
                }
            };

            // Set the User property on the Profile
            user1.Profile.User = user1;

            var user2 = new User
            {
                Id = 2,
                IsOnline = true,
                IsActive = true,
                Profile = new Profile
                {
                    UserId = 2,
                    FirstName = "Jane",
                    LastName = "Smith",
                    BirthDate = DateTime.UtcNow.AddYears(-28),
                    Gender = 2, // Female
                    Preference = 1, // Prefers men
                    MinAgePreference = 25,
                    MaxAgePreference = 45,
                    Pictures = "profile2.jpg",
                    Latitude = 40.7282, // Also in New York, but slightly different location
                    Longitude = -73.9942,
                    MaxDistance = 50
                }
            };

            // Set the User property on the Profile
            user2.Profile.User = user2;

            // Setup for GetUserWithSubscriptionsAsync
            var subscription = new Subscription
            {
                Id = 1,
                ChatsPerDay = 10
            };

            var user1Subscription = new UserSubscription
            {
                Id = 1,
                UserId = user1.Id,
                SubscriptionId = 1,
                Subscription = subscription,
                StartDate = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddYears(1)
            };

            var user1WithSubscription = new User
            {
                Id = user1.Id,
                Subscriptions = new List<UserSubscription> { user1Subscription }
            };

            var user2Subscription = new UserSubscription
            {
                Id = 1,
                UserId = user2.Id,
                SubscriptionId = 1,
                Subscription = subscription,
                StartDate = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddYears(1)
            };

            var user2WithSubscription = new User
            {
                Id = user2.Id,
                Subscriptions = new List<UserSubscription> { user2Subscription }
            };

            // Setup user repository
            _mockUserRepository.Setup(r => r.GetUserWithProfileAsync(1))
                .ReturnsAsync(user1);
            _mockUserRepository.Setup(r => r.GetUserWithProfileAsync(2))
                .ReturnsAsync(user2);
            _mockUserRepository.Setup(r => r.GetUserWithSubscriptionsAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => id == 1 ? user1WithSubscription : user2WithSubscription);

            // Setup profile repository to return profiles based on which user is searching
            _mockProfileRepository.Setup(r => r.GetAllAsync(
                    It.IsAny<Expression<Func<Profile, bool>>>(),
                    It.IsAny<Func<IQueryable<Profile>, IIncludableQueryable<Profile, object>>>()))
                .ReturnsAsync((Expression<Func<Profile, bool>> predicate, Func<IQueryable<Profile>, IIncludableQueryable<Profile, object>> include) =>
                {
                    // Convert the predicate to a string to check which user is searching
                    var predicateString = predicate.ToString();

                    // If the predicate contains "p.UserId != 1", then user1 is searching
                    if (predicateString.Contains("p.UserId != 1"))
                    {
                        return new List<Profile> { user2.Profile };
                    }
                    // If the predicate contains "p.UserId != 2", then user2 is searching
                    else if (predicateString.Contains("p.UserId != 2"))
                    {
                        return new List<Profile> { user1.Profile };
                    }

                    // Default case
                    return new List<Profile>();
                });

            // Setup match repository to return no existing matches
            _mockMatchRepository.Setup(r => r.GetAllAsync(
                It.IsAny<Expression<Func<Match, bool>>>(),
                It.IsAny<Func<IQueryable<Match>, IIncludableQueryable<Match, object>>>()
            )).ReturnsAsync(new List<Match>());

            // Setup online status service to return both users as online
            _mockOnlineStatusService.Setup(s => s.GetOnlineUserIdsAsync())
                .ReturnsAsync(new List<int> { 1, 2 });

            // Create a shared match object that will be used for both users
            var sharedMatch = new Match
            {
                Id = 1,
                User1Id = 1,
                User2Id = 2,
                CreatedAt = DateTime.UtcNow,
                Status = 1, // Matched
                User1Response = true,
                User2Response = true
            };

            // Setup match repository to return the shared match for the second user's search
            var matchExists = false;
            _mockMatchRepository.Setup(r => r.SingleOrDefaultAsync(
                It.IsAny<Expression<Func<Match, bool>>>(),
                It.IsAny<Func<IQueryable<Match>, IIncludableQueryable<Match, object>>>()
            )).ReturnsAsync(() => {
                // For the first call, return null (no match exists yet)
                if (!matchExists)
                {
                    matchExists = true;
                    return null;
                }
                // For the second call, return the shared match
                return sharedMatch;
            });

            // Setup cache service for remaining matches
            _mockCacheService.Setup(c => c.GetAsync<int?>(It.IsAny<string>()))
                .ReturnsAsync(10); // Both users have 10 remaining matches

            // Setup match repository to add matches
            _mockMatchRepository.Setup(r => r.AddAsync(It.IsAny<Match>()))
                .Callback<Match>(m => {
                    // Set the match properties to match our shared match
                    m.Id = sharedMatch.Id;
                    m.User1Id = sharedMatch.User1Id;
                    m.User2Id = sharedMatch.User2Id;
                    m.Status = sharedMatch.Status;
                    m.User1Response = sharedMatch.User1Response;
                    m.User2Response = sharedMatch.User2Response;
                    m.CreatedAt = sharedMatch.CreatedAt;
                })
                .Returns(Task.CompletedTask);

            _mockMatchRepository.Setup(r => r.SaveChangesAsync())
                .ReturnsAsync(1);

            // Act
            // Simulate both users searching at the same time
            var matchForUser1Task = _matchService.FindMatchAsync(1);
            var matchForUser2Task = _matchService.FindMatchAsync(2);

            // Wait for both tasks to complete
            await Task.WhenAll(matchForUser1Task, matchForUser2Task);

            var matchForUser1 = await matchForUser1Task;
            var matchForUser2 = await matchForUser2Task;

            // Assert
            Assert.NotNull(matchForUser1);
            Assert.NotNull(matchForUser2);

            // User1 should be matched with User2
            Assert.Equal(2, matchForUser1.OtherUserId);

            // User2 should be matched with User1
            Assert.Equal(1, matchForUser2.OtherUserId);

            // Both matches should have the same ID (they're the same match)
            Assert.Equal(matchForUser1.Id, matchForUser2.Id);
        }

        [Fact]
        public async Task OfflineUser_ShouldNotBeFound_WhenSearchingForMatches()
        {
            // Arrange
            // Create two users with compatible preferences
            var user1 = new User
            {
                Id = 1,
                IsOnline = true,
                IsActive = true,
                Profile = new Profile
                {
                    UserId = 1,
                    FirstName = "John",
                    LastName = "Doe",
                    BirthDate = DateTime.UtcNow.AddYears(-30),
                    Gender = 1, // Male
                    Preference = 2, // Prefers women
                    MinAgePreference = 20,
                    MaxAgePreference = 40,
                    Pictures = "profile1.jpg",
                    Latitude = 40.7128, // New York
                    Longitude = -74.0060,
                    MaxDistance = 50
                }
            };

            // Set the User property on the Profile
            user1.Profile.User = user1;

            var user2 = new User
            {
                Id = 2,
                IsOnline = false, // User2 is OFFLINE
                IsActive = true,
                Profile = new Profile
                {
                    UserId = 2,
                    FirstName = "Jane",
                    LastName = "Smith",
                    BirthDate = DateTime.UtcNow.AddYears(-28),
                    Gender = 2, // Female
                    Preference = 1, // Prefers men
                    MinAgePreference = 25,
                    MaxAgePreference = 45,
                    Pictures = "profile2.jpg",
                    Latitude = 40.7282, // Also in New York, but slightly different location
                    Longitude = -73.9942,
                    MaxDistance = 50
                }
            };

            // Set the User property on the Profile
            user2.Profile.User = user2;

            // Setup for GetUserWithSubscriptionsAsync
            var subscription = new Subscription
            {
                Id = 1,
                ChatsPerDay = 10
            };

            var user1Subscription = new UserSubscription
            {
                Id = 1,
                UserId = user1.Id,
                SubscriptionId = 1,
                Subscription = subscription,
                StartDate = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddYears(1)
            };

            var user1WithSubscription = new User
            {
                Id = user1.Id,
                Subscriptions = new List<UserSubscription> { user1Subscription }
            };

            // Setup user repository
            _mockUserRepository.Setup(r => r.GetUserWithProfileAsync(1))
                .ReturnsAsync(user1);
            _mockUserRepository.Setup(r => r.GetUserWithSubscriptionsAsync(1))
                .ReturnsAsync(user1WithSubscription);

            // Setup profile repository to return user2's profile when user1 searches
            _mockProfileRepository.Setup(r => r.GetAllAsync(
                    It.IsAny<Expression<Func<Profile, bool>>>(),
                    It.IsAny<Func<IQueryable<Profile>, IIncludableQueryable<Profile, object>>>()))
                .ReturnsAsync(new List<Profile> { user2.Profile });

            // Important: Make sure the User property is properly set on the Profile
            user2.Profile.User = user2;

            // Setup match repository to return no existing matches
            _mockMatchRepository.Setup(r => r.GetAllAsync(
                It.IsAny<Expression<Func<Match, bool>>>(),
                It.IsAny<Func<IQueryable<Match>, IIncludableQueryable<Match, object>>>()
            )).ReturnsAsync(new List<Match>());

            // Setup online status service to return only user1 as online
            _mockOnlineStatusService.Setup(s => s.GetOnlineUserIdsAsync())
                .ReturnsAsync(new List<int> { 1 }); // Only user1 is online

            // Setup match repository to return no existing match for SingleOrDefaultAsync
            _mockMatchRepository.Setup(r => r.SingleOrDefaultAsync(
                It.IsAny<Expression<Func<Match, bool>>>(),
                It.IsAny<Func<IQueryable<Match>, IIncludableQueryable<Match, object>>>()
            )).ReturnsAsync((Match)null);

            // Setup cache service for remaining matches
            _mockCacheService.Setup(c => c.GetAsync<int?>(It.IsAny<string>()))
                .ReturnsAsync(10); // User has 10 remaining matches

            // Act
            var match = await _matchService.FindMatchAsync(1);

            // Assert
            Assert.Null(match); // No match should be found since user2 is offline
        }
    }
}
