// Vuetify
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

// Material Design Icons
import '@mdi/font/css/materialdesignicons.css'

// Custom theme
const lightTheme = {
  dark: false,
  colors: {
    primary: '#E91E63', // Pink
    secondary: '#9C27B0', // Purple
    accent: '#FF4081',
    error: '#F44336',
    warning: '#FFC107',
    info: '#2196F3',
    success: '#4CAF50',
    background: '#F5F5F5',
    surface: '#FFFFFF',
  }
}

const darkTheme = {
  dark: true,
  colors: {
    primary: '#E91E63', // Pink
    secondary: '#9C27B0', // Purple
    accent: '#FF4081',
    error: '#F44336',
    warning: '#FFC107',
    info: '#2196F3',
    success: '#4CAF50',
    background: '#121212',
    surface: '#1E1E1E',
  }
}

export default createVuetify({
  components,
  directives,
  theme: {
    defaultTheme: 'lightTheme',
    themes: {
      lightTheme,
      darkTheme
    }
  }
})
