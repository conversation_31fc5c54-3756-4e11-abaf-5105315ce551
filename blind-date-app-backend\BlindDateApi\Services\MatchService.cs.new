using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class MatchService : IMatchService
    {
        private readonly IRepository<Match> _matchRepository;
        private readonly IRepository<Profile> _profileRepository;
        private readonly IUserRepository _userRepository;
        private readonly ICacheService _cacheService;
        private readonly IOnlineStatusService _onlineStatusService;
        private readonly ILogger<MatchService> _logger;

        private const string RemainingMatchesKey = "user:{0}:remaining_matches:{1}";

        public MatchService(
            IRepository<Match> matchRepository,
            IRepository<Profile> profileRepository,
            IUserRepository userRepository,
            ICacheService cacheService,
            IOnlineStatusService onlineStatusService,
            ILogger<MatchService> logger)
        {
            _matchRepository = matchRepository;
            _profileRepository = profileRepository;
            _userRepository = userRepository;
            _cacheService = cacheService;
            _onlineStatusService = onlineStatusService;
            _logger = logger;
        }

        public async Task<List<MatchDto>> GetMatchesAsync(int userId)
        {
            try
            {
                // Get all matches for the user
                var matches = await _matchRepository.GetAllAsync(
                    m => (m.User1Id == userId || m.User2Id == userId) && m.Status == 1, // Only matched
                    query => query.Include(m => m.User1)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.User2)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.Messages)
                                  .OrderByDescending(m => m.CreatedAt)
                );

                var matchDtos = new List<MatchDto>();

                foreach (var match in matches)
                {
                    // Get the other user in the match
                    var otherUser = match.GetOtherUser(userId);
                    if (otherUser == null || otherUser.Profile == null)
                    {
                        continue;
                    }

                    // Calculate age
                    int? age = null;
                    var today = DateTime.Today;
                    var birthDate = otherUser.Profile.BirthDate;
                    var ageValue = today.Year - birthDate.Year;
                    if (birthDate.Date > today.AddYears(-ageValue))
                    {
                        ageValue--;
                    }
                    age = ageValue;

                    // Get the last message if any
                    var lastMessage = match.Messages.OrderByDescending(m => m.SentAt).FirstOrDefault();
                    var lastMessagePreview = lastMessage?.Content != null ?
                        lastMessage.Content.Substring(0, Math.Min(50, lastMessage.Content.Length)) : null;
                    var lastMessageAt = lastMessage?.SentAt;

                    // Count unread messages (messages sent by the other user that haven't been read)
                    var unreadCount = match.Messages.Count(m =>
                        m.SenderId != userId &&
                        (!m.IsRead));

                    // Create the match DTO
                    var matchDto = new MatchDto
                    {
                        Id = match.Id,
                        OtherUserId = otherUser.Id,
                        FirstName = otherUser.Profile.FirstName,
                        LastName = otherUser.Profile.LastName,
                        Age = age,
                        Bio = otherUser.Profile.Bio,
                        CreatedAt = match.CreatedAt,
                        IsMatched = match.IsMatched,
                        HasResponded = match.GetUserResponse(userId).HasValue,
                        Pictures = otherUser.Profile.Pictures?.Split(',').ToList() ?? new List<string>(),
                        LastMessageAt = lastMessageAt,
                        LastMessagePreview = lastMessagePreview,
                        UnreadCount = unreadCount
                    };

                    matchDtos.Add(matchDto);
                }

                return matchDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting matches for user {UserId}", userId);
                throw;
            }
        }

        public async Task<MatchDto> FindMatchAsync(int userId)
        {
            try
            {
                // Check if user has reached their match limit
                if (await HasReachedMatchLimitAsync(userId))
                {
                    throw new InvalidOperationException("You have reached your daily match limit");
                }

                // Get the user's profile
                var user = await _userRepository.GetUserWithProfileAsync(userId);
                if (user == null || user.Profile == null)
                {
                    throw new InvalidOperationException("User profile not found");
                }

                // Get online users
                var onlineUserIds = await _onlineStatusService.GetOnlineUserIdsAsync();
                
                // STEP 1: Check if there's already a pending match where this user is User2
                // This is the key to handling simultaneous matching
                var pendingMatch = await _matchRepository.SingleOrDefaultAsync(
                    m => m.User2Id == userId && 
                         m.Status == 0 && 
                         m.User2Response == null &&
                         onlineUserIds.Contains(m.User1Id)
                );

                if (pendingMatch != null)
                {
                    // Found a pending match where this user is User2
                    // Get the other user's profile
                    var otherUser = await _userRepository.GetUserWithProfileAsync(pendingMatch.User1Id);
                    if (otherUser != null && otherUser.Profile != null)
                    {
                        // Accept the match
                        pendingMatch.User2Response = true;
                        
                        // If both users have responded positively, mark as matched
                        if (pendingMatch.User1Response == true)
                        {
                            pendingMatch.Status = 1; // Matched
                        }
                        
                        // Save changes
                        await _matchRepository.SaveChangesAsync();
                        
                        // Calculate age
                        int? age = null;
                        var today = DateTime.Today;
                        var birthDate = otherUser.Profile.BirthDate;
                        var ageValue = today.Year - birthDate.Year;
                        if (birthDate.Date > today.AddYears(-ageValue))
                        {
                            ageValue--;
                        }
                        age = ageValue;
                        
                        // Decrement the remaining matches count in cache
                        await DecrementRemainingMatchesAsync(userId);
                        
                        // Return the match DTO
                        return new MatchDto
                        {
                            Id = pendingMatch.Id,
                            OtherUserId = otherUser.Id,
                            FirstName = otherUser.Profile.FirstName,
                            LastName = otherUser.Profile.LastName,
                            Bio = otherUser.Profile.Bio,
                            Age = age,
                            CreatedAt = pendingMatch.CreatedAt,
                            IsMatched = pendingMatch.Status == 1,
                            HasResponded = true, // We just responded
                            Pictures = otherUser.Profile.Pictures?.Split(',').ToList() ?? new List<string>(),
                            LastMessageAt = null,
                            LastMessagePreview = null,
                            UnreadCount = 0
                        };
                    }
                }
                
                // STEP 2: If no pending match found, find a new match
                
                // Calculate min and max birth dates based on preferences
                var today = DateTime.Today;
                var minBirthDate = today.AddYears(-user.Profile.MaxAgePreference - 1).AddDays(1);
                var maxBirthDate = today.AddYears(-user.Profile.MinAgePreference);
                
                // Get existing matches
                var existingMatches = await _matchRepository.GetAllAsync(
                    m => (m.User1Id == userId || m.User2Id == userId)
                );
                
                var matchedUserIds = existingMatches.Select(m =>
                    m.User1Id == userId ? m.User2Id : m.User1Id
                ).ToList();
                
                // Find potential matches based on preferences
                var potentialMatches = await _profileRepository.GetAllAsync(
                    p => p.UserId != userId &&
                         p.Gender == user.Profile.Preference &&
                         p.BirthDate >= minBirthDate &&
                         p.BirthDate <= maxBirthDate &&
                         !matchedUserIds.Contains(p.UserId) &&
                         onlineUserIds.Contains(p.UserId),
                    query => query.Include(p => p.User)
                );
                
                // If no potential matches, return null
                if (!potentialMatches.Any())
                {
                    return null;
                }
                
                // Select a random match
                var random = new Random();
                var selectedProfile = potentialMatches[random.Next(potentialMatches.Count)];
                
                // Create a new match
                var match = new Match
                {
                    User1Id = userId,
                    User2Id = selectedProfile.UserId,
                    CreatedAt = DateTime.UtcNow,
                    Status = 0, // Pending
                    User1Response = true, // Current user (User1) likes the match
                    User2Response = null  // Other user hasn't responded yet
                };
                
                // Add and save immediately to ensure the match is in the database
                await _matchRepository.AddAsync(match);
                await _matchRepository.SaveChangesAsync();
                
                // Calculate age
                int? age = null;
                var birthDate = selectedProfile.BirthDate;
                var ageValue = today.Year - birthDate.Year;
                if (birthDate.Date > today.AddYears(-ageValue))
                {
                    ageValue--;
                }
                age = ageValue;
                
                // Decrement the remaining matches count in cache
                await DecrementRemainingMatchesAsync(userId);
                
                // Return the match DTO
                return new MatchDto
                {
                    Id = match.Id,
                    OtherUserId = selectedProfile.UserId,
                    FirstName = selectedProfile.FirstName,
                    LastName = selectedProfile.LastName,
                    Bio = selectedProfile.Bio,
                    Age = age,
                    CreatedAt = match.CreatedAt,
                    IsMatched = false, // New match is not yet matched
                    HasResponded = true, // Current user has responded
                    Pictures = selectedProfile.Pictures?.Split(',').ToList() ?? new List<string>(),
                    LastMessageAt = null,
                    LastMessagePreview = null,
                    UnreadCount = 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding match for user {UserId}", userId);
                throw;
            }
        }

        public async Task<bool> RespondToMatchAsync(int userId, MatchResponseDto response)
        {
            try
            {
                // Get the match
                var match = await _matchRepository.GetByIdAsync(response.MatchId);
                if (match == null)
                {
                    throw new InvalidOperationException("Match not found");
                }

                // Verify the user is part of the match
                if (match.User1Id != userId && match.User2Id != userId)
                {
                    throw new InvalidOperationException("User is not part of this match");
                }

                // Set the user's response
                match.SetUserResponse(userId, response.Like);

                // Save changes
                await _matchRepository.SaveChangesAsync();

                // Return whether it's a mutual match
                return match.IsMatched;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error responding to match {MatchId} for user {UserId}", response.MatchId, userId);
                throw;
            }
        }

        public async Task<MatchDto> GetMatchByIdAsync(int userId, int matchId)
        {
            try
            {
                // Get the match
                var match = await _matchRepository.SingleOrDefaultAsync(
                    m => m.Id == matchId && (m.User1Id == userId || m.User2Id == userId),
                    query => query.Include(m => m.User1)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.User2)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.Messages)
                );

                if (match == null)
                {
                    throw new InvalidOperationException("Match not found");
                }

                // Get the other user in the match
                var otherUser = match.GetOtherUser(userId);
                if (otherUser == null || otherUser.Profile == null)
                {
                    throw new InvalidOperationException("Other user not found");
                }

                // Calculate age
                int? age = null;
                var today = DateTime.Today;
                var birthDate = otherUser.Profile.BirthDate;
                var ageValue = today.Year - birthDate.Year;
                if (birthDate.Date > today.AddYears(-ageValue))
                {
                    ageValue--;
                }
                age = ageValue;

                // Get the last message if any
                var lastMessage = match.Messages.OrderByDescending(m => m.SentAt).FirstOrDefault();
                var lastMessagePreview = lastMessage?.Content != null ?
                    lastMessage.Content.Substring(0, Math.Min(50, lastMessage.Content.Length)) : null;
                var lastMessageAt = lastMessage?.SentAt;

                // Count unread messages (messages sent by the other user that haven't been read)
                var unreadCount = match.Messages.Count(m =>
                    m.SenderId != userId &&
                    (!m.IsRead));

                // Create the match DTO
                return new MatchDto
                {
                    Id = match.Id,
                    OtherUserId = otherUser.Id,
                    FirstName = otherUser.Profile.FirstName,
                    LastName = otherUser.Profile.LastName,
                    Age = age,
                    CreatedAt = match.CreatedAt,
                    IsMatched = match.IsMatched,
                    HasResponded = match.GetUserResponse(userId).HasValue,
                    Pictures = otherUser.Profile.Pictures?.Split(',').ToList() ?? new List<string>(),
                    LastMessageAt = lastMessageAt,
                    LastMessagePreview = lastMessagePreview,
                    UnreadCount = unreadCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting match {MatchId} for user {UserId}", matchId, userId);
                throw;
            }
        }

        public async Task<bool> HasReachedMatchLimitAsync(int userId)
        {
            var remaining = await GetRemainingMatchesAsync(userId);
            return remaining <= 0;
        }

        public async Task<int> GetRemainingMatchesAsync(int userId)
        {
            // Get the user's subscription
            var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
            if (user == null || user.CurrentSubscription == null)
            {
                return 0;
            }

            var subscription = user.CurrentSubscription.Subscription;
            if (subscription == null)
            {
                return 0;
            }

            // If unlimited matches (-1), return a high number
            if (subscription.ChatsPerDay < 0)
            {
                return 999;
            }

            // Check cache for remaining matches
            var today = DateTime.UtcNow.Date.ToString("yyyyMMdd");
            var cacheKey = string.Format(RemainingMatchesKey, userId, today);

            var remainingMatches = await _cacheService.GetAsync<int?>(cacheKey);
            if (remainingMatches.HasValue)
            {
                return remainingMatches.Value;
            }

            // If not in cache, calculate remaining matches
            var startOfDay = DateTime.UtcNow.Date;
            var endOfDay = startOfDay.AddDays(1);

            var matchesStartedToday = await _matchRepository.GetAllAsync(
                m => (m.User1Id == userId || m.User2Id == userId) &&
                     m.CreatedAt >= startOfDay &&
                     m.CreatedAt < endOfDay
            );

            var matchesUsed = matchesStartedToday.Count();
            var remaining = Math.Max(0, subscription.ChatsPerDay - matchesUsed);

            // Cache the result
            await _cacheService.SetAsync(cacheKey, remaining, TimeSpan.FromHours(1));

            return remaining;
        }

        private async Task DecrementRemainingMatchesAsync(int userId)
        {
            var today = DateTime.UtcNow.Date.ToString("yyyyMMdd");
            var cacheKey = string.Format(RemainingMatchesKey, userId, today);

            var remainingMatches = await _cacheService.GetAsync<int?>(cacheKey);
            if (remainingMatches.HasValue)
            {
                var newValue = Math.Max(0, remainingMatches.Value - 1);
                await _cacheService.SetAsync(cacheKey, newValue, TimeSpan.FromHours(1));
            }
        }
    }
}
