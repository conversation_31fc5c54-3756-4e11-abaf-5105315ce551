using System;

namespace BlindDateApi.DTOs
{
    public class MessageDto
    {
        public int Id { get; set; }
        public int? MatchId { get; set; }
        public int? ConversationId { get; set; }
        public int SenderId { get; set; }
        public DateTime SentAt { get; set; }
        public string Content { get; set; } = string.Empty;
        public string EncryptedContent { get; set; } = string.Empty;
        public string IV { get; set; } = string.Empty;
        public string EncryptedKey { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public DateTime? ReadAt { get; set; }
        public bool IsFromCurrentUser { get; set; }
        public int Type { get; set; } = 0;
    }

    public class SendMessageDto
    {
        public int? MatchId { get; set; }
        public int? ConversationId { get; set; }
        public string Content { get; set; } = string.Empty;
        public string EncryptedContent { get; set; } = string.Empty;
        public string IV { get; set; } = string.Empty;
        public string EncryptedKey { get; set; } = string.Empty;
        public int Type { get; set; } = 0;
    }

    public class MessageEncryptionDto
    {
        public string PublicKey { get; set; } = string.Empty;
        public string PrivateKey { get; set; } = string.Empty;
    }
}
