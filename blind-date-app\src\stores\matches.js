import { defineStore } from 'pinia'
import { useAuthStore } from './auth'
import { matchService } from '../services/api'

export const useMatchesStore = defineStore('matches', {
  state: () => ({
    matches: [],
    currentMatch: null,
    isSearching: false,
    isLoading: false,
    error: null,
    chats: {},
    searchTimer: null,
    searchingStatus: {
      isSearching: false,
      searchingCount: 0,
      canSearch: true
    }
  }),

  actions: {
    async startSearch() {
      this.isSearching = true
      this.error = null

      // Clear any existing timer
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      try {
        // Start searching on the server
        await this.startSearching()

        // Get initial searching status
        await this.getSearchingStatus()

        // Set up polling to check for matches
        return new Promise((resolve) => {
          const checkForMatches = async () => {
            try {
              // Get current searching status
              const status = await this.getSearchingStatus()

              // If there are other users searching, try to find a match
              if (status.searchingCount > 0) {
                const match = await this.findMatch()
                if (match) {
                  // If we found a match, resolve the promise
                  clearInterval(this.searchTimer)
                  this.searchTimer = null
                  resolve(match)
                  return
                }
              }

              // Update the UI with current status
              this.error = status.searchingCount > 0
                ? null
                : 'No other users are currently searching. Please try again later.'

            } catch (error) {
              console.error('Error checking for matches:', error)
            }
          }

          // Check immediately
          checkForMatches()

          // Then set up interval to check every 5 seconds
          this.searchTimer = setInterval(checkForMatches, 5000)

          // After 2 minutes, stop searching if no match found
          setTimeout(() => {
            if (this.searchTimer) {
              clearInterval(this.searchTimer)
              this.searchTimer = null
              this.stopSearching().catch(console.error)
              this.isSearching = false
              resolve(null)
            }
          }, 2 * 60 * 1000) // 2 minutes
        })
      } catch (error) {
        this.error = error.message || 'Failed to start searching'
        this.isSearching = false
        return null
      }
    },

    async cancelSearch() {
      if (this.searchTimer) {
        clearInterval(this.searchTimer)
        this.searchTimer = null
      }

      // Stop searching on the server
      try {
        await this.stopSearching()
      } catch (error) {
        console.error('Error stopping search:', error)
      }

      this.isSearching = false
      this.currentMatch = null
    },

    async getSearchingStatus() {
      try {
        const response = await matchService.getSearchingStatus()
        if (response && response.data) {
          this.searchingStatus = response.data
        }
        return this.searchingStatus
      } catch (error) {
        console.error('Failed to get searching status:', error)
        return this.searchingStatus
      }
    },

    async startSearching() {
      this.isLoading = true
      this.error = null

      try {
        // Call the API to start searching
        await matchService.startSearching()

        // Update the searching status
        this.searchingStatus.isSearching = true

        return true
      } catch (error) {
        this.error = error.message || 'Failed to start searching'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async stopSearching() {
      this.isLoading = true
      this.error = null

      try {
        // Call the API to stop searching
        await matchService.stopSearching()

        // Update the searching status
        this.searchingStatus.isSearching = false

        return true
      } catch (error) {
        this.error = error.message || 'Failed to stop searching'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async findMatch() {
      this.isLoading = true

      try {
        // Call the API to search for a match
        const response = await matchService.findMatch()

        if (!response || !response.data) {
          this.currentMatch = null
          this.isSearching = false
          return null
        }

        const match = response.data

        // Format the match data
        this.currentMatch = {
          id: match.id,
          otherUserId: match.otherUserId, // Add the otherUserId property
          firstName: match.firstName,
          lastName: match.lastName || '',
          birthDate: match.birthDate,
          bio: match.bio || 'No bio available',
          pictures: match.pictures || [],
          interests: match.interests || [],
          age: match.age,
          revealPicture: match.pictures && match.pictures.length > 0,
          revealAge: match.age !== null,
          showLastName: match.lastName && match.lastName.length > 0,
          isOnline: match.isOnline,
          distance: match.distance,
          location: match.location
        }

        this.isSearching = false

        // Initialize chat for this match
        this.chats[match.id] = {
          messages: [],
          startTime: new Date(),
          timeLimit: 2 * 60 * 1000, // 2 minutes for all plans
          liked: false,
          matched: false
        }

        return this.currentMatch
      } catch (error) {
        this.error = error.message || 'Failed to find a match'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async sendMessage(matchId, content) {
      // Normalize matchId to string
      const normalizedId = String(matchId)

      // Check if chat exists with exact ID
      if (!this.chats[normalizedId]) {
        // Try to find by substring
        const chatId = Object.keys(this.chats).find(id => {
          return String(id).includes(normalizedId) || normalizedId.includes(String(id))
        })

        if (chatId) {
          matchId = chatId
        } else {
          throw new Error('Chat not found')
        }
      }

      // Add message to chat
      const message = {
        id: `msg_${Date.now()}`,
        sender: 'user',
        content,
        timestamp: new Date()
      }

      this.chats[matchId].messages.push(message)

      // Simulate response after a short delay
      setTimeout(() => {
        const responseMessage = {
          id: `msg_${Date.now() + 1}`,
          sender: 'match',
          content: 'Thanks for your message! How are you doing today?',
          timestamp: new Date()
        }

        this.chats[matchId].messages.push(responseMessage)
      }, 1000)

      return message
    },

    async likeMatch(matchId) {
      // Normalize matchId to string
      const normalizedId = String(matchId)

      // Check if chat exists with exact ID
      if (!this.chats[normalizedId]) {
        // Try to find by substring
        const chatId = Object.keys(this.chats).find(id => {
          return String(id).includes(normalizedId) || normalizedId.includes(String(id))
        })

        if (chatId) {
          matchId = chatId
        } else {
          throw new Error('Match not found')
        }
      }

      try {
        // Call the API to like the match
        const response = await matchService.likeMatch(matchId)

        if (!response || !response.data) {
          return false
        }

        const { isMatched } = response.data

        this.chats[matchId].liked = true

        if (isMatched) {
          this.chats[matchId].matched = true

          // Add to matches list if it's a match
          if (this.currentMatch) {
            const match = { ...this.currentMatch, matched: true }
            this.matches.push(match)
          }

          return true
        }

        return false
      } catch (error) {
        throw error
      }
    },

    async dislikeMatch(matchId) {
      // Normalize matchId to string
      const normalizedId = String(matchId)

      // Check if chat exists with exact ID
      if (!this.chats[normalizedId]) {
        // Try to find by substring
        const chatId = Object.keys(this.chats).find(id => {
          return String(id).includes(normalizedId) || normalizedId.includes(String(id))
        })

        if (chatId) {
          matchId = chatId
        } else {
          throw new Error('Match not found')
        }
      }

      try {
        // Call the API to dislike the match
        await matchService.dislikeMatch(matchId)

        // Remove the chat locally
        delete this.chats[matchId]
        this.currentMatch = null
      } catch (error) {
        throw error
      }
    },

    async fetchMatches() {
      this.isLoading = true
      this.error = null

      try {
        // Call the API to get matches
        const response = await matchService.getMatches()

        if (!response || !response.data) {
          return []
        }

        // Format the matches data
        this.matches = response.data.map(match => {

          return {
            id: match.id,
            otherUserId: match.otherUserId, // Add the otherUserId property
            firstName: match.firstName,
            lastName: match.lastName || '',
            birthDate: match.birthDate,
            bio: match.bio || 'No bio available',
            pictures: match.pictures || [],
            interests: match.interests || [],
            age: match.age,
            matched: match.isMatched,
            lastMessage: null, // Will be set in fetchAllMatches
            lastMessagePreview: match.lastMessagePreview,
            lastMessageAt: match.lastMessageAt,
            unreadCount: match.unreadCount,
            revealPicture: match.pictures && match.pictures.length > 0,
            revealAge: match.age !== null,
            showLastName: match.lastName && match.lastName.length > 0,
            isOnline: match.isOnline,
            distance: match.distance,
            location: match.location
          }
        })

        // Initialize chats for these matches
        this.matches.forEach(match => {
          if (!this.chats[match.id]) {
            this.chats[match.id] = {
              messages: [],
              startTime: match.lastMessageAt ? new Date(match.lastMessageAt) : new Date(),
              liked: true,
              matched: true
            }
          }
        })

        return this.matches
      } catch (error) {
        this.error = error.message || 'Failed to fetch matches'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async fetchAllMatches() {
      this.isLoading = true
      this.error = null

      try {
        // Fetch matches from the API
        await this.fetchMatches()

        // Make sure all matches have chat objects
        this.matches.forEach(match => {
          if (!this.chats[match.id]) {
            this.chats[match.id] = {
              messages: [],
              startTime: match.lastMessageAt ? new Date(match.lastMessageAt) : new Date(),
              timeLimit: 2 * 60 * 1000, // 2 minutes
              liked: true,
              matched: true
            }
          }
        })

        // Add additional details to each match
        const matchesWithDetails = await Promise.all(this.matches.map(async match => {
          // Get the chat for this match
          const chat = this.chats[match.id] || { messages: [] }

          // Get message from local chat if available
          const chatMessage = chat.messages.length > 0 ?
            chat.messages[chat.messages.length - 1].content : null

          // Try to decrypt the lastMessagePreview if it looks encrypted
          let lastMessage = match.lastMessagePreview || chatMessage

          // Check if the message looks like it's encrypted (base64 format)
          if (lastMessage) {
            try {
              // Get the auth store to access the user's private key
              const authStore = useAuthStore()

              // Try to get the private key
              let privateKey = authStore.privateKey

              // If still not found, try general localStorage
              if (!privateKey) {
                privateKey = localStorage.getItem('privateKey')
              }

              if (privateKey) {
                // Import the decryption function
                const { decryptMessage } = await import('../services/encryption')

                // Try to decrypt
                try {
                  const decrypted = await decryptMessage(lastMessage, privateKey)
                  if (decrypted && decrypted !== '[Decryption failed]') {
                    lastMessage = decrypted
                  }
                } catch (decryptError) {
                  console.error('Failed to decrypt last message preview:', decryptError)

                  // Try Base64 decoding as fallback for messages
                  try {
                    lastMessage = atob(lastMessage)
                  } catch (b64Error) {
                    console.error('Base64 decoding failed:', b64Error)
                  }
                }
              }
            } catch (error) {
              console.error('Error handling encrypted message:', error)
            }
          }

          // Use the unreadCount from the API or default to 0
          const unreadCount = match.unreadCount || 0

          return {
            ...match,
            lastMessage,
            unreadCount,
            revealPicture: true // For matched users, pictures are revealed
          }
        }))

        return matchesWithDetails
      } catch (error) {
        this.error = error.message || 'Failed to fetch all matches'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    clearCurrentMatch() {
      this.currentMatch = null
      this.error = null
    }
  }
})
