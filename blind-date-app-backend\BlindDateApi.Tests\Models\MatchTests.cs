using BlindDateApi.Data.Models;
using Xunit;

namespace BlindDateApi.Tests.Models
{
    public class MatchTests
    {
        [Fact]
        public void IsMatched_BothUsersLiked_ReturnsTrue()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = true,
                User2Response = true
            };

            // Act
            var isMatched = match.IsMatched;

            // Assert
            Assert.True(isMatched);
        }

        [Fact]
        public void IsMatched_OneUserLiked_ReturnsFalse()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = true,
                User2Response = null
            };

            // Act
            var isMatched = match.IsMatched;

            // Assert
            Assert.False(isMatched);
        }

        [Fact]
        public void IsRejected_OneUserDisliked_ReturnsTrue()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = false,
                User2Response = null
            };

            // Act
            var isRejected = match.IsRejected;

            // Assert
            Assert.True(isRejected);
        }

        [Fact]
        public void IsRejected_BothUsersLiked_ReturnsFalse()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = true,
                User2Response = true
            };

            // Act
            var isRejected = match.IsRejected;

            // Assert
            Assert.False(isRejected);
        }

        [Fact]
        public void GetOtherUser_User1_ReturnsUser2()
        {
            // Arrange
            var user1 = new User { Id = 1 };
            var user2 = new User { Id = 2 };
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1 = user1,
                User2 = user2
            };

            // Act
            var otherUser = match.GetOtherUser(1);

            // Assert
            Assert.Equal(user2, otherUser);
        }

        [Fact]
        public void GetOtherUser_User2_ReturnsUser1()
        {
            // Arrange
            var user1 = new User { Id = 1 };
            var user2 = new User { Id = 2 };
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1 = user1,
                User2 = user2
            };

            // Act
            var otherUser = match.GetOtherUser(2);

            // Assert
            Assert.Equal(user1, otherUser);
        }

        [Fact]
        public void GetUserResponse_User1_ReturnsUser1Response()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = true,
                User2Response = false
            };

            // Act
            var response = match.GetUserResponse(1);

            // Assert
            Assert.Equal(true, response);
        }

        [Fact]
        public void GetUserResponse_User2_ReturnsUser2Response()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = true,
                User2Response = false
            };

            // Act
            var response = match.GetUserResponse(2);

            // Assert
            Assert.Equal(false, response);
        }

        [Fact]
        public void SetUserResponse_User1Likes_UpdatesUser1Response()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = null,
                User2Response = null
            };

            // Act
            match.SetUserResponse(1, true);

            // Assert
            Assert.Equal(true, match.User1Response);
            Assert.Null(match.User2Response);
            Assert.Equal(0, match.Status); // Still pending
        }

        [Fact]
        public void SetUserResponse_BothLike_UpdatesStatusToMatched()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = true,
                User2Response = null
            };

            // Act
            match.SetUserResponse(2, true);

            // Assert
            Assert.Equal(true, match.User1Response);
            Assert.Equal(true, match.User2Response);
            Assert.Equal(1, match.Status); // Matched
        }

        [Fact]
        public void SetUserResponse_OneDislike_UpdatesStatusToRejected()
        {
            // Arrange
            var match = new Match
            {
                User1Id = 1,
                User2Id = 2,
                User1Response = true,
                User2Response = null
            };

            // Act
            match.SetUserResponse(2, false);

            // Assert
            Assert.Equal(true, match.User1Response);
            Assert.Equal(false, match.User2Response);
            Assert.Equal(2, match.Status); // Rejected
        }
    }
}
