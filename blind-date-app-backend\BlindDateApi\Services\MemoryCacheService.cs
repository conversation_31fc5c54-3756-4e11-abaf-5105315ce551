using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly TimeSpan _defaultCacheTime = TimeSpan.FromMinutes(10);

        public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger)
        {
            _memoryCache = memoryCache;
            _logger = logger;
        }

        public T Get<T>(string key)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out T cachedItem))
                {
                    _logger.LogDebug("Cache hit for key: {Key}", key);
                    return cachedItem;
                }

                _logger.LogDebug("Cache miss for key: {Key}", key);
                return default;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item from cache with key: {Key}", key);
                return default;
            }
        }

        public Task<T> GetAsync<T>(string key)
        {
            return Task.FromResult(Get<T>(key));
        }

        public void Set<T>(string key, T value, TimeSpan? absoluteExpiration = null)
        {
            try
            {
                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetAbsoluteExpiration(absoluteExpiration ?? _defaultCacheTime);

                _memoryCache.Set(key, value, cacheEntryOptions);
                _logger.LogDebug("Item added to cache with key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting item in cache with key: {Key}", key);
            }
        }

        public Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpiration = null)
        {
            Set(key, value, absoluteExpiration);
            return Task.CompletedTask;
        }

        public void Remove(string key)
        {
            try
            {
                _memoryCache.Remove(key);
                _logger.LogDebug("Item removed from cache with key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing item from cache with key: {Key}", key);
            }
        }

        public Task RemoveAsync(string key)
        {
            Remove(key);
            return Task.CompletedTask;
        }
    }
}
