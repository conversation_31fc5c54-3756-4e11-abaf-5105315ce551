using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SubscriptionsController : ControllerBase
    {
        private readonly IRepository<Subscription> _subscriptionRepository;
        private readonly IRepository<UserSubscription> _userSubscriptionRepository;
        private readonly IUserRepository _userRepository;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SubscriptionsController> _logger;

        public SubscriptionsController(
            IRepository<Subscription> subscriptionRepository,
            IRepository<UserSubscription> userSubscriptionRepository,
            IUserRepository userRepository,
            ApplicationDbContext context,
            ILogger<SubscriptionsController> logger)
        {
            _subscriptionRepository = subscriptionRepository;
            _userSubscriptionRepository = userSubscriptionRepository;
            _userRepository = userRepository;
            _context = context;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<SubscriptionDto>>> GetAllSubscriptions()
        {
            try
            {
                var subscriptions = await _subscriptionRepository.GetAllAsync();
                
                // Get current user's subscription if authenticated
                int? currentSubscriptionId = null;
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
                    currentSubscriptionId = user?.CurrentSubscription?.SubscriptionId;
                }

                var subscriptionDtos = subscriptions.Select(s => new SubscriptionDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    Price = s.Price,
                    ChatsPerDay = s.ChatsPerDay,
                    MaxInterests = s.MaxInterests,
                    MaxPictures = s.MaxPictures,
                    PictureRevealCondition = s.PictureRevealCondition,
                    IsCurrent = currentSubscriptionId.HasValue && s.Id == currentSubscriptionId.Value
                }).ToList();

                return Ok(subscriptionDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscriptions");
                return StatusCode(500, "An error occurred while retrieving subscriptions");
            }
        }

        [HttpGet("current")]
        [Authorize]
        public async Task<ActionResult<SubscriptionDto>> GetCurrentSubscription()
        {
            try
            {
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return Unauthorized();

                var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
                if (user == null)
                    return NotFound("User not found");

                var currentSubscription = user.CurrentSubscription;
                if (currentSubscription == null || currentSubscription.Subscription == null)
                    return NotFound("No active subscription found");

                var subscription = currentSubscription.Subscription;
                var subscriptionDto = new SubscriptionDto
                {
                    Id = subscription.Id,
                    Name = subscription.Name,
                    Price = subscription.Price,
                    ChatsPerDay = subscription.ChatsPerDay,
                    MaxInterests = subscription.MaxInterests,
                    MaxPictures = subscription.MaxPictures,
                    PictureRevealCondition = subscription.PictureRevealCondition,
                    IsCurrent = true
                };

                return Ok(subscriptionDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current subscription");
                return StatusCode(500, "An error occurred while retrieving current subscription");
            }
        }

        [HttpPost("subscribe")]
        [Authorize]
        public async Task<ActionResult<UserSubscriptionDto>> Subscribe(SubscriptionPurchaseDto purchaseDto)
        {
            try
            {
                var userIdClaim = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                    return Unauthorized();

                var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
                if (user == null)
                    return NotFound("User not found");

                var subscription = await _subscriptionRepository.GetByIdAsync(purchaseDto.SubscriptionId);
                if (subscription == null)
                    return NotFound("Subscription not found");

                // Check if user already has this subscription
                var existingSubscription = user.Subscriptions
                    .FirstOrDefault(us => us.SubscriptionId == purchaseDto.SubscriptionId && us.IsActive);

                if (existingSubscription != null)
                {
                    // Extend existing subscription
                    existingSubscription.ExpiresAt = existingSubscription.ExpiresAt.AddMonths(1);
                    await _context.SaveChangesAsync();

                    return Ok(new UserSubscriptionDto
                    {
                        Id = existingSubscription.Id,
                        UserId = existingSubscription.UserId,
                        SubscriptionId = existingSubscription.SubscriptionId,
                        SubscriptionName = subscription.Name,
                        StartDate = existingSubscription.StartDate,
                        ExpiresAt = existingSubscription.ExpiresAt,
                        IsActive = existingSubscription.IsActive,
                        AmountPaid = subscription.Price
                    });
                }

                // Create new subscription
                var userSubscription = new UserSubscription
                {
                    UserId = userId,
                    SubscriptionId = subscription.Id,
                    StartDate = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMonths(1),
                    TransactionId = $"tx_{DateTime.UtcNow.Ticks}",
                    AmountPaid = subscription.Price
                };

                _context.UserSubscriptions.Add(userSubscription);
                await _context.SaveChangesAsync();

                return Ok(new UserSubscriptionDto
                {
                    Id = userSubscription.Id,
                    UserId = userSubscription.UserId,
                    SubscriptionId = userSubscription.SubscriptionId,
                    SubscriptionName = subscription.Name,
                    StartDate = userSubscription.StartDate,
                    ExpiresAt = userSubscription.ExpiresAt,
                    IsActive = userSubscription.IsActive,
                    AmountPaid = userSubscription.AmountPaid
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error subscribing to plan");
                return StatusCode(500, "An error occurred while processing subscription");
            }
        }
    }
}
