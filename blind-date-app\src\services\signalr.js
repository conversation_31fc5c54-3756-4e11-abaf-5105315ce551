import * as signalR from '@microsoft/signalr'

class SignalRService {
  constructor() {
    this.connection = null
    this.connectionPromise = null
    this.messageCallbacks = []
    this.notificationCallbacks = []
    this.messagesReadCallbacks = []
    this.connectionStateCallbacks = []
  }

  async start() {
    // If already connected, return the connection
    if (this.connection && this.connection.state === signalR.HubConnectionState.Connected) {
      return this.connection
    }

    // If connecting, return the promise
    if (this.connectionPromise) {
      return this.connectionPromise
    }

    this.connectionPromise = new Promise(async (resolve, reject) => {
      try {
        // Get the API URL
        const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5192'

        // Create the connection
        this.connection = new signalR.HubConnectionBuilder()
          .withUrl(`${apiUrl}/hubs/chat`, {
            accessTokenFactory: () => {
              try {
                const userJson = localStorage.getItem('user')
                if (userJson) {
                  const user = JSON.parse(userJson)
                  if (user && user.token) {
                    return user.token
                  }
                }
              } catch (error) {
                console.error('Error getting token for SignalR:', error)
              }
              return null
            }
          })
          .withAutomaticReconnect([0, 2000, 5000, 10000, 30000]) // Retry intervals in ms
          .configureLogging(signalR.LogLevel.Information)
          .build()

        // Set up event handlers
        this.connection.on('ReceiveMessage', (message) => {
          this.messageCallbacks.forEach(callback => callback(message))
        })

        this.connection.on('ReceiveNotification', (notification) => {
          this.notificationCallbacks.forEach(callback => callback(notification))
        })

        this.connection.on('MessagesRead', (readInfo) => {
          this.messagesReadCallbacks.forEach(callback => callback(readInfo))
        })

        // Connection state change events
        this.connection.onreconnecting(() => {
          this.connectionStateCallbacks.forEach(callback => callback('reconnecting'))
        })

        this.connection.onreconnected(() => {
          this.connectionStateCallbacks.forEach(callback => callback('connected'))
        })

        this.connection.onclose(() => {
          this.connectionStateCallbacks.forEach(callback => callback('disconnected'))
        })

        // Start the connection
        await this.connection.start()

        // Resolve the promise with the connection
        resolve(this.connection)
      } catch (error) {
        console.error('Error starting SignalR connection:', error)
        this.connection = null
        this.connectionPromise = null
        reject(error)
      }
    })

    return this.connectionPromise
  }

  async stop() {
    if (this.connection) {
      try {
        await this.connection.stop()
      } catch (error) {
        console.error('Error stopping SignalR connection:', error)
      }
      this.connection = null
      this.connectionPromise = null
    }
  }

  async joinConversation(conversationId) {
    try {
      // Check if we have a valid token before trying to join
      try {
        const userJson = localStorage.getItem('user')
        if (!userJson) {
          return false
        }

        const user = JSON.parse(userJson)
        if (!user || !user.token) {
          return false
        }
      } catch (error) {
        console.error(`Error checking token for conversation ${conversationId}:`, error)
        return false
      }

      const connection = await this.start()

      if (connection.state !== signalR.HubConnectionState.Connected) {
        return false
      }

      await connection.invoke('JoinConversation', conversationId)
      return true
    } catch (error) {
      console.error(`Error joining conversation ${conversationId}:`, error)
      return false
    }
  }

  async leaveConversation(conversationId) {
    try {
      if (this.connection && this.connection.state === signalR.HubConnectionState.Connected) {
        await this.connection.invoke('LeaveConversation', conversationId)
      }
    } catch (error) {
      console.error(`Error leaving conversation ${conversationId}:`, error)
    }
  }

  onMessage(callback) {
    this.messageCallbacks.push(callback)
    return () => {
      this.messageCallbacks = this.messageCallbacks.filter(cb => cb !== callback)
    }
  }

  onNotification(callback) {
    this.notificationCallbacks.push(callback)
    return () => {
      this.notificationCallbacks = this.notificationCallbacks.filter(cb => cb !== callback)
    }
  }

  onMessagesRead(callback) {
    this.messagesReadCallbacks.push(callback)
    return () => {
      this.messagesReadCallbacks = this.messagesReadCallbacks.filter(cb => cb !== callback)
    }
  }

  onConnectionStateChange(callback) {
    this.connectionStateCallbacks.push(callback)
    return () => {
      this.connectionStateCallbacks = this.connectionStateCallbacks.filter(cb => cb !== callback)
    }
  }

  async reconnectWithNewToken() {
    console.log('Reconnecting SignalR with new token')
    await this.stop()
    this.connectionPromise = null
    return this.start()
  }
}

// Create a singleton instance
const signalRService = new SignalRService()

export default signalRService
