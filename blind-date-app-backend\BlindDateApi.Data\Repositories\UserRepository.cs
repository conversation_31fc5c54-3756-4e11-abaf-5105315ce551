using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace BlindDateApi.Data.Repositories
{
    public class UserRepository : Repository<User>, IUserRepository
    {
        public UserRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<User> GetUserByEmailAsync(string email)
        {
            return await _context.Users
                .SingleOrDefaultAsync(u => u.Email == email);
        }

        public async Task<User> GetUserWithProfileAsync(int userId)
        {
            return await _context.Users
                .Include(u => u.Profile)
                .SingleOrDefaultAsync(u => u.Id == userId);
        }

        public async Task<User> GetUserWithSubscriptionsAsync(int userId)
        {
            return await _context.Users
                .Include(u => u.Subscriptions)
                    .ThenInclude(us => us.Subscription)
                .SingleOrDefaultAsync(u => u.Id == userId);
        }
    }
}
