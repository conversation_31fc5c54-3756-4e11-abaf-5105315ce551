/**
 * Geolocation service for handling location-related functionality
 */

/**
 * Get the user's current location using the browser's Geolocation API
 * @returns {Promise<{latitude: number, longitude: number, accuracy: number}>}
 */
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by your browser'));
      return;
    }
    
    navigator.geolocation.getCurrentPosition(
      // Success callback
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy
        });
      },
      // Error callback
      (error) => {
        switch(error.code) {
          case error.PERMISSION_DENIED:
            reject(new Error('Location access was denied. Please enable location services in your browser settings.'));
            break;
          case error.POSITION_UNAVAILABLE:
            reject(new Error('Location information is unavailable. Please try again later.'));
            break;
          case error.TIMEOUT:
            reject(new Error('Location request timed out. Please try again.'));
            break;
          default:
            reject(new Error('An unknown error occurred while getting your location.'));
        }
      },
      // Options
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  });
};

/**
 * Get a location name from coordinates using reverse geocoding
 * @param {number} latitude 
 * @param {number} longitude 
 * @returns {Promise<string>}
 */
export const reverseGeocode = async (latitude, longitude) => {
  try {
    // Using OpenStreetMap Nominatim API for reverse geocoding
    const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10&addressdetails=1`, {
      headers: {
        'Accept-Language': 'en',
        'User-Agent': 'BlindDateApp/1.0'
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to get location name');
    }
    
    const data = await response.json();
    
    // Extract city and country from the response
    const city = data.address.city || 
                data.address.town || 
                data.address.village || 
                data.address.hamlet || 
                data.address.suburb ||
                data.address.county ||
                '';
                
    const country = data.address.country || '';
    
    if (city && country) {
      return `${city}, ${country}`;
    } else if (city) {
      return city;
    } else if (country) {
      return country;
    } else {
      return `Near ${latitude.toFixed(2)}, ${longitude.toFixed(2)}`;
    }
  } catch (error) {
    console.error('Error reverse geocoding:', error);
    // Fallback to coordinates if reverse geocoding fails
    return `Near ${latitude.toFixed(2)}, ${longitude.toFixed(2)}`;
  }
};

/**
 * Calculate distance between two points using the Haversine formula
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} - Distance in kilometers
 */
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  return Math.round(distance);
};

/**
 * Convert degrees to radians
 * @param {number} deg - Degrees
 * @returns {number} - Radians
 */
const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

/**
 * Format distance for display
 * @param {number} distance - Distance in kilometers
 * @returns {string} - Formatted distance string
 */
export const formatDistance = (distance) => {
  if (distance < 1) {
    return 'Less than 1 km';
  } else if (distance < 10) {
    return `${distance} km`;
  } else {
    // Round to nearest 5 km for distances over 10 km
    const roundedDistance = Math.round(distance / 5) * 5;
    return `About ${roundedDistance} km`;
  }
};

/**
 * Get a fallback location based on IP address
 * @returns {Promise<{latitude: number, longitude: number, accuracy: number, source: string}>}
 */
export const getFallbackLocation = async () => {
  try {
    // Using ipapi.co for IP-based geolocation
    const response = await fetch('https://ipapi.co/json/');
    
    if (!response.ok) {
      throw new Error('Failed to get IP-based location');
    }
    
    const data = await response.json();
    
    return {
      latitude: data.latitude,
      longitude: data.longitude,
      accuracy: 5000, // Low accuracy for IP-based location (5km)
      source: 'ip'
    };
  } catch (error) {
    console.error('Failed to get IP-based location:', error);
    // Return a default location (center of the map)
    return {
      latitude: 0,
      longitude: 0,
      accuracy: 10000, // Very low accuracy
      source: 'default'
    };
  }
};
