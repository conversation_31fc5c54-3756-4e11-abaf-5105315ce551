<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const firstName = ref('')
const lastName = ref('')
const birthDate = ref('')
// Gender and preference will be set during profile setup
const errorMessage = ref('')
const isLoading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const showDatePicker = ref(false)
const birthDateError = ref('')

// Calculate the maximum birth date (18 years ago from today)
const maxBirthDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 18)
  return date.toISOString().split('T')[0]
})

// Format the birth date for display
const formattedBirthDate = computed(() => {
  if (!birthDate.value) return ''
  return new Date(birthDate.value).toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  })
})

// Calculate age from birth date
const calculateAge = (birthDateStr) => {
  if (!birthDateStr) return ''
  const today = new Date()
  const birthDateObj = new Date(birthDateStr)
  let age = today.getFullYear() - birthDateObj.getFullYear()
  const monthDiff = today.getMonth() - birthDateObj.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDateObj.getDate())) {
    age--
  }

  return age
}

const register = async () => {
  console.log('Register function called')
  // Reset error messages
  errorMessage.value = ''
  birthDateError.value = ''

  // Validate all required fields
  if (!email.value || !password.value || !confirmPassword.value || !firstName.value || !lastName.value || !birthDate.value) {
    errorMessage.value = 'Please fill in all required fields'
    return
  }

  // Validate passwords match
  if (password.value !== confirmPassword.value) {
    errorMessage.value = 'Passwords do not match'
    return
  }

  // Validate birth date (must be at least 18 years old)
  const age = calculateAge(birthDate.value)
  if (age < 18) {
    birthDateError.value = 'You must be at least 18 years old to register'
    errorMessage.value = 'You must be at least 18 years old to register'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''

    // Format birthdate as ISO string
    const birthDateObj = new Date(birthDate.value)

    const registerData = {
      email: email.value,
      password: password.value,
      firstName: firstName.value,
      lastName: lastName.value,
      birthDate: birthDateObj.toISOString()
      // Gender and preference will be set during profile setup
    }
    await authStore.register(registerData)

    // Just in case the redirection in the auth store fails
    if (!authStore.isAuthenticated) {
      errorMessage.value = 'Registration successful but login failed. Please try logging in.'
    }
  } catch (error) {
    console.error('Registration error:', error)
    errorMessage.value = error.message || 'Failed to register'
  } finally {
    isLoading.value = false
  }
}

const navigateToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <v-container fluid class="fill-height">
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="pa-6" elevation="8">
          <v-card-title class="text-center text-h4 font-weight-bold">Create Account</v-card-title>
          <v-card-subtitle class="text-center">Sign up to find your perfect match</v-card-subtitle>

          <v-form @submit.prevent="register" class="mt-4">
            <v-row class="mb-4">
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="firstName"
                  label="First Name"
                  required
                  variant="outlined"
                  :disabled="isLoading"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="lastName"
                  label="Last Name"
                  required
                  variant="outlined"
                  :disabled="isLoading"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-text-field
              v-model="email"
              label="Email"
              type="email"
              required
              variant="outlined"
              :disabled="isLoading"
              class="mb-4"
            ></v-text-field>

            <v-text-field
              v-model="password"
              label="Password"
              :type="showPassword ? 'text' : 'password'"
              required
              variant="outlined"
              :disabled="isLoading"
              :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
              @click:append-inner="showPassword = !showPassword"
              class="mb-4"
            ></v-text-field>

            <v-text-field
              v-model="confirmPassword"
              label="Confirm Password"
              :type="showConfirmPassword ? 'text' : 'password'"
              required
              variant="outlined"
              :disabled="isLoading"
              :append-inner-icon="showConfirmPassword ? 'mdi-eye-off' : 'mdi-eye'"
              @click:append-inner="showConfirmPassword = !showConfirmPassword"
              class="mb-4"
            ></v-text-field>

            <v-menu
              v-model="showDatePicker"
              :close-on-content-click="false"
              transition="scale-transition"
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  v-model="formattedBirthDate"
                  label="Birth Date"
                  variant="outlined"
                  readonly
                  required
                  :disabled="isLoading"
                  v-bind="props"
                  placeholder="SELECT DATE"
                  class="mb-4"
                  :hint="birthDate ? `Age: ${calculateAge(birthDate)}` : ''"
                  persistent-hint
                  :error-messages="birthDateError"
                  prepend-inner-icon="mdi-calendar"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="birthDate"
                :max="maxBirthDate"
                @update:model-value="showDatePicker = false"
              >
                <template v-slot:header="{ date }">
                  <div class="text-h5 pa-2">
                    {{ date ? new Date(date).toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' }) : 'Select date' }}
                  </div>
                </template>
              </v-date-picker>
            </v-menu>



            <v-alert
              v-if="errorMessage"
              type="error"
              variant="tonal"
              class="mb-4"
              border="start"
              closable
              @click:close="errorMessage = ''"
            >
              <div v-if="errorMessage.includes('\n')">
                <strong>Please fix the following errors:</strong>
                <ul class="mt-2 mb-0">
                  <li v-for="(line, index) in errorMessage.split('\n')" :key="index">
                    {{ line }}
                  </li>
                </ul>
              </div>
              <div v-else>
                <strong>Error:</strong> {{ errorMessage }}
              </div>
            </v-alert>

            <v-btn
              type="submit"
              block
              color="primary"
              size="large"
              :loading="isLoading"
              class="mt-2"
            >
              Sign up
            </v-btn>
          </v-form>

          <v-divider class="my-4"></v-divider>

          <v-card-text class="text-center d-flex align-center justify-center">
            <span class="text-body-1 mr-2">Already have an account?</span>
            <v-btn
              variant="text"
              color="primary"
              @click="navigateToLogin"
              class="text-body-1 font-weight-medium pa-1"
              style="margin-top: -1px;"
            >
              SIGN IN
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
