using System;
using System.Linq;
using System.Threading.Tasks;
using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Data.Seed
{
    public static class DbInitializer
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

            try
            {
                var context = services.GetRequiredService<ApplicationDbContext>();
                
                // Apply migrations if they haven't been applied
                await context.Database.MigrateAsync();
                
                // Seed subscriptions if they don't exist
                if (!await context.Subscriptions.AnyAsync())
                {
                    await SeedSubscriptions(context);
                }
                
                // Seed interests if they don't exist
                if (!await context.Interests.AnyAsync())
                {
                    await SeedInterests(context);
                }
                
                await context.SaveChangesAsync();
                logger.LogInformation("Database initialized successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while initializing the database");
                throw;
            }
        }
        
        private static async Task SeedSubscriptions(ApplicationDbContext context)
        {
            var subscriptions = new[]
            {
                new Subscription
                {
                    Name = "Free",
                    Price = 0,
                    ChatsPerDay = 10,
                    MaxInterests = 2,
                    MaxPictures = 2,
                    PictureRevealCondition = "Match"
                },
                new Subscription
                {
                    Name = "Premium",
                    Price = 4.99m,
                    ChatsPerDay = 30,
                    MaxInterests = 4,
                    MaxPictures = 4,
                    PictureRevealCondition = "10 Messages"
                },
                new Subscription
                {
                    Name = "VIP",
                    Price = 9.99m,
                    ChatsPerDay = -1, // Unlimited
                    MaxInterests = 6,
                    MaxPictures = 6,
                    PictureRevealCondition = "Immediate"
                }
            };
            
            await context.Subscriptions.AddRangeAsync(subscriptions);
        }
        
        private static async Task SeedInterests(ApplicationDbContext context)
        {
            var interests = new[]
            {
                new Interest { Name = "Music", Description = "Music and concerts" },
                new Interest { Name = "Movies", Description = "Films and cinema" },
                new Interest { Name = "Books", Description = "Reading and literature" },
                new Interest { Name = "Sports", Description = "Sports and athletics" },
                new Interest { Name = "Travel", Description = "Traveling and exploring" },
                new Interest { Name = "Food", Description = "Cooking and dining" },
                new Interest { Name = "Art", Description = "Visual arts and museums" },
                new Interest { Name = "Photography", Description = "Taking and viewing photos" },
                new Interest { Name = "Technology", Description = "Tech and gadgets" },
                new Interest { Name = "Gaming", Description = "Video and board games" },
                new Interest { Name = "Fitness", Description = "Exercise and wellness" },
                new Interest { Name = "Fashion", Description = "Clothing and style" },
                new Interest { Name = "Nature", Description = "Outdoors and wildlife" },
                new Interest { Name = "Pets", Description = "Animals and pet care" },
                new Interest { Name = "Cooking", Description = "Food preparation" },
                new Interest { Name = "Dancing", Description = "Dance and movement" }
            };
            
            await context.Interests.AddRangeAsync(interests);
        }
    }
}
