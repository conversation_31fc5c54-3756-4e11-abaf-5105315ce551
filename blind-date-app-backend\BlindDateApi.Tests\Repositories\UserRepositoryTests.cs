using System;
using System.Threading.Tasks;
using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Xunit;

namespace BlindDateApi.Tests.Repositories
{
    public class UserRepositoryTests
    {
        private readonly DbContextOptions<ApplicationDbContext> _options;

        public UserRepositoryTests()
        {
            // Use in-memory database for testing
            _options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .ConfigureWarnings(x => x.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;
        }

        [Fact]
        public async Task GetUserByEmailAsync_ExistingEmail_ReturnsUser()
        {
            // Arrange
            var testEmail = "<EMAIL>";

            // Seed the database
            using (var context = new ApplicationDbContext(_options))
            {
                context.Users.Add(new User
                {
                    Email = testEmail,
                    PasswordHash = "hashedpassword",
                    CreatedAt = DateTime.UtcNow
                });
                await context.SaveChangesAsync();
            }

            // Act
            using (var context = new ApplicationDbContext(_options))
            {
                var repository = new UserRepository(context);
                var user = await repository.GetUserByEmailAsync(testEmail);

                // Assert
                Assert.NotNull(user);
                Assert.Equal(testEmail, user.Email);
            }
        }

        [Fact]
        public async Task GetUserByEmailAsync_NonExistingEmail_ReturnsNull()
        {
            // Arrange
            var testEmail = "<EMAIL>";
            var nonExistingEmail = "<EMAIL>";

            // Seed the database
            using (var context = new ApplicationDbContext(_options))
            {
                context.Users.Add(new User
                {
                    Email = testEmail,
                    PasswordHash = "hashedpassword",
                    CreatedAt = DateTime.UtcNow
                });
                await context.SaveChangesAsync();
            }

            // Act
            using (var context = new ApplicationDbContext(_options))
            {
                var repository = new UserRepository(context);
                var user = await repository.GetUserByEmailAsync(nonExistingEmail);

                // Assert
                Assert.Null(user);
            }
        }

        [Fact]
        public async Task GetUserWithProfileAsync_UserWithProfile_ReturnsUserWithProfile()
        {
            // Arrange
            var userId = 1;

            // Seed the database
            using (var context = new ApplicationDbContext(_options))
            {
                var user = new User
                {
                    Id = userId,
                    Email = "<EMAIL>",
                    PasswordHash = "hashedpassword",
                    CreatedAt = DateTime.UtcNow
                };

                context.Users.Add(user);
                await context.SaveChangesAsync();

                context.Profiles.Add(new Profile
                {
                    UserId = userId,
                    FirstName = "John",
                    LastName = "Doe",
                    BirthDate = new DateTime(1990, 1, 1),
                    Gender = 1,
                    Preference = 2
                });

                await context.SaveChangesAsync();
            }

            // Act
            using (var context = new ApplicationDbContext(_options))
            {
                var repository = new UserRepository(context);
                var user = await repository.GetUserWithProfileAsync(userId);

                // Assert
                Assert.NotNull(user);
                Assert.NotNull(user.Profile);
                Assert.Equal("John", user.Profile.FirstName);
                Assert.Equal("Doe", user.Profile.LastName);
            }
        }

        [Fact]
        public async Task GetUserWithSubscriptionsAsync_UserWithSubscriptions_ReturnsUserWithSubscriptions()
        {
            // Arrange
            var userId = 1;

            // Seed the database
            using (var context = new ApplicationDbContext(_options))
            {
                var user = new User
                {
                    Id = userId,
                    Email = "<EMAIL>",
                    PasswordHash = "hashedpassword",
                    CreatedAt = DateTime.UtcNow
                };

                context.Users.Add(user);
                await context.SaveChangesAsync();

                var subscription = new Subscription
                {
                    Id = 1,
                    Name = "Free",
                    Price = 0,
                    ChatsPerDay = 10,
                    MaxInterests = 2,
                    MaxPictures = 2,
                    PictureRevealCondition = "Match"
                };

                context.Subscriptions.Add(subscription);
                await context.SaveChangesAsync();

                context.UserSubscriptions.Add(new UserSubscription
                {
                    UserId = userId,
                    SubscriptionId = subscription.Id,
                    StartDate = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddYears(1)
                });

                await context.SaveChangesAsync();
            }

            // Act
            using (var context = new ApplicationDbContext(_options))
            {
                var repository = new UserRepository(context);
                var user = await repository.GetUserWithSubscriptionsAsync(userId);

                // Assert
                Assert.NotNull(user);
                Assert.NotEmpty(user.Subscriptions);
                Assert.Equal("Free", user.Subscriptions.First().Subscription.Name);
            }
        }
    }
}
