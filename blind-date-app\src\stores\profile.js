import { defineStore } from 'pinia'
import { profileService } from '../services/api'

export const useProfileStore = defineStore('profile', {
  state: () => ({
    profile: null,
    isLoading: false,
    error: null,
    interests: [],
    interestsData: [], // Full interest objects from API
    searchPreferences: null
  }),

  actions: {
    async fetchProfile() {
      this.isLoading = true
      this.error = null

      try {
        // Make API call to get profile
        const response = await profileService.getProfile()

        if (response && response.data) {
          // Map API response to our profile format
          const profile = {
            id: response.data.id,
            firstName: response.data.firstName,
            lastName: response.data.lastName,
            birthDate: response.data.birthDate,
            bio: response.data.bio || '',
            pictures: response.data.pictures || [],
            interests: response.data.interests || [],
            location: response.data.location || '',
            latitude: response.data.latitude || null,
            longitude: response.data.longitude || null,
            locationAccuracy: response.data.locationAccuracy || null,
            gender: response.data.gender,
            preference: response.data.preference,
            minAgePreference: response.data.minAgePreference,
            maxAgePreference: response.data.maxAgePreference
          }

          this.profile = profile
          return profile
        } else {
          throw new Error('Invalid API response format')
        }
      } catch (error) {
        this.error = error.message || 'Failed to fetch profile'

        // Fallback to local storage if API fails
        const localProfile = JSON.parse(localStorage.getItem('profile'))
        if (localProfile) {
          this.profile = localProfile
          return localProfile
        }

        throw error
      } finally {
        this.isLoading = false
      }
    },

    async updateProfile(profileData) {
      this.isLoading = true
      this.error = null

      try {
        // Prepare data for API
        const apiProfileData = {
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          bio: profileData.bio,
          location: profileData.location,
          latitude: profileData.latitude,
          longitude: profileData.longitude,
          locationAccuracy: profileData.locationAccuracy,
          gender: profileData.gender,
          preference: profileData.preference,
          minAgePreference: profileData.minAgePreference,
          maxAgePreference: profileData.maxAgePreference,
          interests: profileData.interests,
          pictures: profileData.pictures
        }

        // Remove undefined properties
        Object.keys(apiProfileData).forEach(key => {
          if (apiProfileData[key] === undefined) {
            delete apiProfileData[key]
          }
        })

        // Make API call to update profile with error handling
        try {
          await profileService.updateProfile(apiProfileData)
        } catch (apiError) {
          // If we get a 401 error, just continue with local storage
          if (apiError.response && apiError.response.status === 401) {
            // Authentication error, continuing with local storage only
          } else {
            // For other errors, we'll still continue
          }
        }

        // Update local state regardless of API success
        const updatedProfile = { ...this.profile, ...profileData }
        this.profile = updatedProfile

        // Also update local storage as fallback
        localStorage.setItem('profile', JSON.stringify(updatedProfile))

        return updatedProfile
      } catch (error) {
        this.error = error.message || 'Failed to update profile'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async fetchSearchPreferences() {
      this.isLoading = true
      this.error = null

      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))

        // Mock response
        const preferences = JSON.parse(localStorage.getItem('searchPreferences')) || {
          ageRange: [18, 40],
          distance: 50,
          interests: ['Music', 'Travel'],
          gender: 'women'
        }

        this.searchPreferences = preferences
        return preferences
      } catch (error) {
        this.error = error.message || 'Failed to fetch search preferences'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Method to set search preferences locally without making an API call
    setSearchPreferences(preferences) {
      // Update local state
      const updatedPreferences = { ...this.searchPreferences, ...preferences }
      this.searchPreferences = updatedPreferences

      // Also update local storage
      localStorage.setItem('searchPreferences', JSON.stringify(updatedPreferences))

      return updatedPreferences
    },

    async updateSearchPreferences(preferences) {
      this.isLoading = true
      this.error = null

      try {
        // Make API call to update search preferences with error handling
        try {
          await profileService.updateSearchPreferences(preferences)
        } catch (apiError) {
          // If we get a 401 error, just continue with local storage
          if (apiError.response && apiError.response.status === 401) {
            // Authentication error, continuing with local storage only
          }
          // For other errors, we'll still continue
        }

        // Update local state regardless of API success
        const updatedPreferences = { ...this.searchPreferences, ...preferences }
        this.searchPreferences = updatedPreferences

        // Also update local storage as fallback
        localStorage.setItem('searchPreferences', JSON.stringify(updatedPreferences))

        return updatedPreferences
      } catch (error) {
        this.error = error.message || 'Failed to update search preferences'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async fetchInterests() {
      this.isLoading = true
      this.error = null

      try {
        // Make a direct API call to fetch interests
        try {
          const response = await profileService.getInterests()

          if (response && response.data && Array.isArray(response.data)) {
            // Store the full interest objects
            this.interestsData = response.data
            // Map the API response to get just the interest names for backward compatibility
            this.interests = response.data.map(interest => interest.name)
            return this.interests
          } else {
            throw new Error('Invalid API response format')
          }
        } catch (apiError) {
          // Check if this is an authentication error
          if (apiError.response && apiError.response.status === 401) {
            // Authentication error, continue with fallback
          }

          // Throw the error to be caught by the outer catch block
          throw apiError
        }
      } catch (error) {
        this.error = error.message || 'Failed to fetch interests'

        // Fallback to default interests for any error
        const fallbackInterests = [
          { id: 1, name: 'Music', description: 'Music and concerts' },
          { id: 2, name: 'Movies', description: 'Films and cinema' },
          { id: 3, name: 'Books', description: 'Reading and literature' },
          { id: 4, name: 'Sports', description: 'Sports and athletics' },
          { id: 5, name: 'Travel', description: 'Traveling and exploring' },
          { id: 6, name: 'Food', description: 'Cooking and dining' },
          { id: 7, name: 'Art', description: 'Visual arts and museums' },
          { id: 8, name: 'Photography', description: 'Taking and viewing photos' },
          { id: 9, name: 'Technology', description: 'Tech and gadgets' },
          { id: 10, name: 'Gaming', description: 'Video and board games' },
          { id: 11, name: 'Fitness', description: 'Exercise and wellness' },
          { id: 12, name: 'Fashion', description: 'Clothing and style' },
          { id: 13, name: 'Nature', description: 'Outdoors and wildlife' },
          { id: 14, name: 'Pets', description: 'Animals and pet care' },
          { id: 15, name: 'Cooking', description: 'Food preparation' },
          { id: 16, name: 'Dancing', description: 'Dance and movement' }
        ]

        // Store the full interest objects
        this.interestsData = fallbackInterests
        // Map to get just the interest names for backward compatibility
        this.interests = fallbackInterests.map(interest => interest.name)
        return this.interests
      } finally {
        this.isLoading = false
      }
    }
  }
})
