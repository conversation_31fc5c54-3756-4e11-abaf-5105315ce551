<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useMatchesStore } from '../stores/matches'

const router = useRouter()
const authStore = useAuthStore()
const matchesStore = useMatchesStore()

const isSearching = ref(false)
const searchProgress = ref(0)
const searchInterval = ref(null)
const statusInterval = ref(null)
const errorMessage = ref('')
const mobileMenu = ref(false)

// Computed properties for searching status
const searchingCount = computed(() => matchesStore.searchingStatus.searchingCount)
const canSearch = computed(() => matchesStore.searchingStatus.canSearch)

onMounted(async () => {
  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // Get initial searching status
  await matchesStore.getSearchingStatus()

  // Set up interval to refresh searching status every 5 seconds
  statusInterval.value = setInterval(async () => {
    await matchesStore.getSearchingStatus()
  }, 5000)
})

onBeforeUnmount(() => {
  // Clean up intervals if component is unmounted
  if (searchInterval.value) {
    clearInterval(searchInterval.value)
  }

  if (statusInterval.value) {
    clearInterval(statusInterval.value)
  }

  // Cancel any ongoing search
  if (isSearching.value) {
    matchesStore.cancelSearch()
  }
})

const startSearch = async () => {
  try {
    isSearching.value = true
    searchProgress.value = 0
    errorMessage.value = ''

    // Start progress animation
    const duration = 5000 // 5 seconds for demo (would be 2-3 minutes in production)
    const interval = 50 // Update every 50ms
    const steps = duration / interval
    let currentStep = 0

    searchInterval.value = setInterval(() => {
      currentStep++
      searchProgress.value = (currentStep / steps) * 100

      if (currentStep >= steps) {
        clearInterval(searchInterval.value)
      }
    }, interval)

    // Start actual search
    const match = await matchesStore.startSearch()

    // Clear interval if it's still running
    if (searchInterval.value) {
      clearInterval(searchInterval.value)
      searchProgress.value = 100
    }

    // Navigate to chat if match found, otherwise show no match message
    if (match) {
      // Pass source=matching to indicate this is a new match from the matching process
      const routeConfig = {
        path: `/chat/${match.id}`,
        query: { source: 'matching' }
      }

      // Navigate to the chat with the source parameter
      router.push(routeConfig)
    } else {
      isSearching.value = false
      errorMessage.value = 'No online users match your preferences right now. Try again later!'
    }
  } catch (error) {
    errorMessage.value = error.message || 'Search failed'
    isSearching.value = false

    if (searchInterval.value) {
      clearInterval(searchInterval.value)
    }
  }
}

const cancelSearch = () => {
  matchesStore.cancelSearch()
  isSearching.value = false

  if (searchInterval.value) {
    clearInterval(searchInterval.value)
  }
}

const navigateToMatches = () => {
  router.push('/matches')
}

const navigateToProfile = () => {
  router.push('/profile')
}

const navigateToPayments = () => {
  router.push('/payments')
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}
</script>

<template>
  <v-layout>
    <!-- App Bar -->
    <v-app-bar color="white" elevation="1">
      <v-app-bar-title class="text-primary font-weight-bold">Blind Date</v-app-bar-title>

      <v-spacer></v-spacer>

      <!-- Desktop Navigation -->
      <div class="d-none d-sm-flex">
        <v-btn variant="text" @click="navigateToMatches">
          <v-icon start>mdi-account-multiple</v-icon>
          Matches
        </v-btn>

        <v-btn variant="text" @click="navigateToProfile">
          <v-icon start>mdi-account</v-icon>
          Profile
        </v-btn>

        <v-btn variant="text" @click="navigateToPayments">
          <v-icon start>mdi-crown</v-icon>
          Upgrade
        </v-btn>

        <v-btn variant="text" @click="logout">
          <v-icon start>mdi-logout</v-icon>
          Logout
        </v-btn>
      </div>

      <!-- Mobile Navigation -->
      <v-menu v-model="mobileMenu">
        <template v-slot:activator="{ props }">
          <v-btn
            icon
            v-bind="props"
            class="d-sm-none"
          >
            <v-icon>mdi-menu</v-icon>
          </v-btn>
        </template>
        <v-list>
          <v-list-item @click="navigateToMatches">
            <template v-slot:prepend>
              <v-icon>mdi-account-multiple</v-icon>
            </template>
            <v-list-item-title>Matches</v-list-item-title>
          </v-list-item>

          <v-list-item @click="navigateToProfile">
            <template v-slot:prepend>
              <v-icon>mdi-account</v-icon>
            </template>
            <v-list-item-title>Profile</v-list-item-title>
          </v-list-item>

          <v-list-item @click="navigateToPayments">
            <template v-slot:prepend>
              <v-icon>mdi-crown</v-icon>
            </template>
            <v-list-item-title>Upgrade</v-list-item-title>
          </v-list-item>

          <v-list-item @click="logout">
            <template v-slot:prepend>
              <v-icon>mdi-logout</v-icon>
            </template>
            <v-list-item-title>Logout</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <v-container>
        <v-row justify="center">
          <v-col cols="12" sm="10" md="8" lg="6">
            <v-card class="pa-6" elevation="4">
              <v-card-title class="text-center text-h4 font-weight-bold">Find Your Match</v-card-title>
              <v-card-subtitle class="text-center">
                We'll search for someone who is online and shares your interests
              </v-card-subtitle>

              <!-- Searching status -->
              <v-card-text v-if="!isSearching" class="text-center">
                <v-chip
                  :color="searchingCount > 0 ? 'success' : 'warning'"
                  class="mb-2"
                >
                  <v-icon start>{{ searchingCount > 0 ? 'mdi-account-multiple' : 'mdi-account-off' }}</v-icon>
                  {{ searchingCount > 0 ? `${searchingCount} users currently searching` : 'No users currently searching' }}
                </v-chip>

                <v-chip
                  v-if="!canSearch"
                  color="error"
                  class="ml-2 mb-2"
                >
                  <v-icon start>mdi-alert-circle</v-icon>
                  Daily limit reached
                </v-chip>
              </v-card-text>

              <v-alert
                v-if="errorMessage"
                type="error"
                variant="tonal"
                class="mt-4"
              >
                {{ errorMessage }}
              </v-alert>

              <v-card-text v-if="isSearching" class="text-center py-6">
                <p class="text-h6 mb-2">Searching for users who match your preferences...</p>
                <p class="text-body-2 mb-2">
                  {{ searchingCount > 0
                    ? `${searchingCount} other ${searchingCount === 1 ? 'user is' : 'users are'} currently searching`
                    : 'Waiting for other users to start searching' }}
                </p>
                <p class="text-caption mb-6">This may take a few minutes</p>

                <div class="d-flex flex-column align-center">
                  <v-progress-circular
                    indeterminate
                    color="primary"
                    size="70"
                    width="7"
                    class="mb-6"
                  ></v-progress-circular>

                  <v-btn
                    variant="outlined"
                    color="error"
                    @click="cancelSearch"
                  >
                    CANCEL SEARCH
                  </v-btn>
                </div>
              </v-card-text>

              <v-card-text v-else class="text-center py-6">
                <v-btn
                  block
                  color="primary"
                  size="large"
                  @click="startSearch"
                  :disabled="!canSearch"
                >
                  <v-icon start>mdi-magnify</v-icon>
                  START SEARCHING
                </v-btn>

                <div v-if="!canSearch" class="text-caption text-error mt-2">
                  You have reached your daily match limit. Please try again tomorrow.
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  </v-layout>
</template>
