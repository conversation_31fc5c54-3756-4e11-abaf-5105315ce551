<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateToLogin = () => {
  router.push('/login')
}

const navigateToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <v-container fluid class="fill-height welcome-container">
    <v-row align="center" justify="center" class="welcome-row">
      <v-col cols="12" sm="8" md="6" lg="4" xl="3">
        <v-card class="welcome-card" elevation="8">
          <v-card-title class="welcome-title">
            Blind Date
          </v-card-title>

          <v-card-subtitle class="welcome-subtitle">
            Find your perfect match based on personality, not just looks
          </v-card-subtitle>

          <v-card-text class="welcome-content">
            <div class="button-container">
              <v-btn
                block
                color="primary"
                size="large"
                class="welcome-btn login-btn"
                @click="navigateToLogin"
              >
                LOGIN
              </v-btn>

              <v-btn
                block
                variant="outlined"
                color="primary"
                size="large"
                class="welcome-btn register-btn"
                @click="navigateToRegister"
              >
                CREATE ACCOUNT
              </v-btn>
            </div>
          </v-card-text>

          <v-card-text class="welcome-footer">
            By signing up, you agree to our Terms of Service and Privacy Policy
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
.welcome-container {
  padding: 16px;
}

.welcome-row {
  min-height: 100vh;
}

.welcome-card {
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.welcome-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: bold;
  color: #E91E63;
  margin-bottom: 8px;
  line-height: 1.2;
}

.welcome-subtitle {
  text-align: center;
  font-size: 1.125rem;
  color: #666;
  margin-bottom: 24px;
  line-height: 1.4;
  padding: 0 8px;
}

.welcome-content {
  padding: 0;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 32px;
}

.welcome-btn {
  height: 56px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 12px;
  text-transform: uppercase;
}

.login-btn {
  box-shadow: 0 4px 16px rgba(233, 30, 99, 0.3);
}

.register-btn {
  border-width: 2px;
}

.welcome-footer {
  text-align: center;
  font-size: 0.875rem;
  color: #888;
  margin-top: 24px;
  padding: 0 16px;
  line-height: 1.4;
}

/* Mobile Responsive Styles */
@media (max-width: 600px) {
  .welcome-container {
    padding: 12px;
  }

  .welcome-card {
    padding: 24px 20px;
    margin: 8px;
  }

  .welcome-title {
    font-size: 2rem;
    margin-bottom: 6px;
  }

  .welcome-subtitle {
    font-size: 1rem;
    margin-bottom: 20px;
    padding: 0 4px;
  }

  .button-container {
    gap: 14px;
    margin-top: 24px;
  }

  .welcome-btn {
    height: 52px;
    font-size: 0.95rem;
  }

  .welcome-footer {
    font-size: 0.8rem;
    margin-top: 20px;
    padding: 0 8px;
  }
}

/* Extra small screens */
@media (max-width: 400px) {
  .welcome-card {
    padding: 20px 16px;
    margin: 4px;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .welcome-subtitle {
    font-size: 0.95rem;
  }

  .welcome-btn {
    height: 48px;
    font-size: 0.9rem;
  }
}

/* Tablet styles */
@media (min-width: 601px) and (max-width: 960px) {
  .welcome-card {
    padding: 40px 32px;
  }

  .welcome-title {
    font-size: 2.75rem;
  }

  .welcome-subtitle {
    font-size: 1.2rem;
  }
}

/* Large screens */
@media (min-width: 1264px) {
  .welcome-card {
    padding: 48px 40px;
  }

  .welcome-title {
    font-size: 3rem;
  }

  .welcome-subtitle {
    font-size: 1.25rem;
  }

  .button-container {
    margin-top: 40px;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 960px) and (orientation: landscape) {
  .welcome-row {
    min-height: auto;
    padding: 20px 0;
  }

  .welcome-card {
    padding: 20px;
  }

  .welcome-title {
    font-size: 1.75rem;
    margin-bottom: 4px;
  }

  .welcome-subtitle {
    font-size: 0.95rem;
    margin-bottom: 16px;
  }

  .button-container {
    margin-top: 20px;
    gap: 12px;
  }

  .welcome-btn {
    height: 44px;
  }

  .welcome-footer {
    margin-top: 16px;
    font-size: 0.8rem;
  }
}
</style>
