<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateToLogin = () => {
  router.push('/login')
}

const navigateToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <v-container fluid class="fill-height welcome-container">
    <v-row align="center" justify="center" class="welcome-row">
      <v-col cols="12" sm="10" md="8" lg="6" xl="4">
        <v-card class="welcome-card" elevation="8">
          <v-card-title class="welcome-title">
            Blind Date
          </v-card-title>

          <v-card-subtitle class="welcome-subtitle">
            Find your perfect match based on personality, not just looks
          </v-card-subtitle>

          <v-card-text class="welcome-content">
            <div class="button-container">
              <v-btn
                block
                color="primary"
                size="large"
                class="welcome-btn login-btn"
                @click="navigateToLogin"
              >
                LOGIN
              </v-btn>

              <v-btn
                block
                variant="outlined"
                color="primary"
                size="large"
                class="welcome-btn register-btn"
                @click="navigateToRegister"
              >
                CREATE ACCOUNT
              </v-btn>
            </div>
          </v-card-text>

          <v-card-text class="welcome-footer">
            By signing up, you agree to our Terms of Service and Privacy Policy
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
.welcome-container {
  padding: 8px;
  min-height: 100vh;
}

.welcome-row {
  min-height: 100vh;
  margin: 0;
}

.welcome-card {
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 100%;
}

.welcome-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: bold;
  color: #E91E63;
  margin-bottom: 6px;
  line-height: 1.1;
  padding: 0;
}

.welcome-subtitle {
  text-align: center;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.3;
  padding: 0;
  word-wrap: break-word;
  hyphens: auto;
}

.welcome-content {
  padding: 0;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 20px;
}

.welcome-btn {
  height: 48px;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 0.3px;
  border-radius: 8px;
  text-transform: uppercase;
  min-height: 48px;
}

.login-btn {
  box-shadow: 0 2px 8px rgba(233, 30, 99, 0.3);
}

.register-btn {
  border-width: 2px;
}

.welcome-footer {
  text-align: center;
  font-size: 0.75rem;
  color: #888;
  margin-top: 16px;
  padding: 0;
  line-height: 1.3;
  word-wrap: break-word;
}

/* Extra small mobile screens */
@media (max-width: 480px) {
  .welcome-container {
    padding: 4px;
  }

  .welcome-card {
    padding: 16px 12px;
    margin: 2px;
    border-radius: 8px;
  }

  .welcome-title {
    font-size: 1.5rem;
    margin-bottom: 4px;
  }

  .welcome-subtitle {
    font-size: 0.8rem;
    margin-bottom: 16px;
    line-height: 1.2;
  }

  .button-container {
    gap: 10px;
    margin-top: 16px;
  }

  .welcome-btn {
    height: 44px;
    font-size: 0.85rem;
    border-radius: 6px;
  }

  .welcome-footer {
    font-size: 0.7rem;
    margin-top: 12px;
    line-height: 1.2;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .welcome-container {
    padding: 2px;
  }

  .welcome-card {
    padding: 12px 8px;
    margin: 1px;
  }

  .welcome-title {
    font-size: 1.3rem;
  }

  .welcome-subtitle {
    font-size: 0.75rem;
    margin-bottom: 12px;
  }

  .welcome-btn {
    height: 42px;
    font-size: 0.8rem;
  }

  .welcome-footer {
    font-size: 0.65rem;
    margin-top: 10px;
  }
}

/* Tablet and larger screens */
@media (min-width: 481px) {
  .welcome-container {
    padding: 16px;
  }

  .welcome-card {
    padding: 32px 24px;
    border-radius: 16px;
  }

  .welcome-title {
    font-size: 2.2rem;
    margin-bottom: 8px;
  }

  .welcome-subtitle {
    font-size: 1rem;
    margin-bottom: 24px;
  }

  .button-container {
    gap: 16px;
    margin-top: 28px;
  }

  .welcome-btn {
    height: 52px;
    font-size: 0.95rem;
    border-radius: 10px;
  }

  .welcome-footer {
    font-size: 0.8rem;
    margin-top: 20px;
  }
}

/* Desktop screens */
@media (min-width: 769px) {
  .welcome-card {
    padding: 40px 32px;
  }

  .welcome-title {
    font-size: 2.5rem;
  }

  .welcome-subtitle {
    font-size: 1.1rem;
  }

  .welcome-btn {
    height: 56px;
    font-size: 1rem;
  }

  .welcome-footer {
    font-size: 0.85rem;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) and (max-height: 500px) {
  .welcome-container {
    padding: 4px;
  }

  .welcome-row {
    min-height: auto;
    padding: 8px 0;
  }

  .welcome-card {
    padding: 12px 16px;
  }

  .welcome-title {
    font-size: 1.4rem;
    margin-bottom: 2px;
  }

  .welcome-subtitle {
    font-size: 0.75rem;
    margin-bottom: 8px;
  }

  .button-container {
    margin-top: 12px;
    gap: 8px;
  }

  .welcome-btn {
    height: 40px;
    font-size: 0.8rem;
  }

  .welcome-footer {
    margin-top: 8px;
    font-size: 0.65rem;
  }
}
</style>
