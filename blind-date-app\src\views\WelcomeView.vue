<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateToLogin = () => {
  router.push('/login')
}

const navigateToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <v-container fluid class="fill-height">
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="pa-6" elevation="8">
          <v-card-title class="text-center text-h3 font-weight-bold primary--text">
            Blind Date
          </v-card-title>

          <v-card-subtitle class="text-center text-subtitle-1 mt-2">
            Find your perfect match based on personality, not just looks
          </v-card-subtitle>

          <v-card-text>
            <v-row class="mt-6">
              <v-col cols="12">
                <v-btn
                  block
                  color="primary"
                  size="large"
                  @click="navigateToLogin"
                >
                  Login
                </v-btn>
              </v-col>

              <v-col cols="12">
                <v-btn
                  block
                  variant="outlined"
                  color="primary"
                  size="large"
                  @click="navigateToRegister"
                >
                  Create Account
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-text class="text-center text-caption">
            By signing up, you agree to our Terms of Service and Privacy Policy
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>
