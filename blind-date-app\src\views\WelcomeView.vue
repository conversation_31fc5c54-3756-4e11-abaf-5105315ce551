<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateToLogin = () => {
  router.push('/login')
}

const navigateToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <v-container fluid class="fill-height">
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="pa-6" elevation="8">
          <v-card-title class="text-center text-h3 font-weight-bold primary--text">
            Blind Date
          </v-card-title>

          <v-card-subtitle class="text-center text-subtitle-1 mt-2">
            Find your perfect match based on personality, not just looks
          </v-card-subtitle>

          <v-card-text>
            <v-row class="mt-6">
              <v-col cols="12">
                <v-btn
                  block
                  color="primary"
                  size="large"
                  @click="navigateToLogin"
                >
                  Login
                </v-btn>
              </v-col>

              <v-col cols="12">
                <v-btn
                  block
                  variant="outlined"
                  color="primary"
                  size="large"
                  @click="navigateToRegister"
                >
                  Create Account
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-text class="text-center text-caption">
            By signing up, you agree to our Terms of Service and Privacy Policy
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
/* Mobile responsive styles - only add what's needed */
@media (max-width: 600px) {
  .v-container {
    padding: 12px !important;
  }

  .v-card {
    padding: 20px !important;
  }

  .v-card-title {
    font-size: 1.75rem !important;
    line-height: 1.2 !important;
  }

  .v-card-subtitle {
    font-size: 0.9rem !important;
    line-height: 1.3 !important;
    word-wrap: break-word;
  }

  .text-caption {
    font-size: 0.75rem !important;
    line-height: 1.3 !important;
    word-wrap: break-word;
  }
}

/* Extra small screens */
@media (max-width: 400px) {
  .v-container {
    padding: 8px !important;
  }

  .v-card {
    padding: 16px !important;
  }

  .v-card-title {
    font-size: 1.5rem !important;
  }

  .v-card-subtitle {
    font-size: 0.85rem !important;
  }

  .text-caption {
    font-size: 0.7rem !important;
  }
}
</style>
