using System;
using System.Security.Cryptography;
using BlindDateApi.Services;
using Xunit;

namespace BlindDateApi.Tests.Services
{
    public class EncryptionServiceTests
    {
        private readonly IEncryptionService _encryptionService;

        public EncryptionServiceTests()
        {
            _encryptionService = new EncryptionService();
        }

        [Fact]
        public void GenerateKeyPair_ReturnsValidKeyPair()
        {
            // Act
            var (publicKey, privateKey) = _encryptionService.GenerateKeyPair();

            // Assert
            Assert.NotNull(publicKey);
            Assert.NotNull(privateKey);
            Assert.NotEmpty(publicKey);
            Assert.NotEmpty(privateKey);

            // Verify keys are in Base64 format
            Assert.True(IsBase64String(publicKey));
            Assert.True(IsBase64String(privateKey));
        }

        [Fact]
        public void EncryptMessage_WithValidPublicKey_ReturnsEncryptedMessage()
        {
            // Arrange
            var (publicKey, _) = _encryptionService.GenerateKeyPair();
            var message = "This is a secret message";

            // Act
            var encryptedMessage = _encryptionService.EncryptMessage(message, publicKey);

            // Assert
            Assert.NotNull(encryptedMessage);
            Assert.NotEmpty(encryptedMessage);
            Assert.NotEqual(message, encryptedMessage);
            Assert.True(IsBase64String(encryptedMessage));
        }

        [Fact]
        public void DecryptMessage_WithValidPrivateKey_ReturnsOriginalMessage()
        {
            // Arrange
            var (publicKey, privateKey) = _encryptionService.GenerateKeyPair();
            var originalMessage = "This is a secret message";
            var encryptedMessage = _encryptionService.EncryptMessage(originalMessage, publicKey);

            // Act
            var decryptedMessage = _encryptionService.DecryptMessage(encryptedMessage, privateKey);

            // Assert
            Assert.NotNull(decryptedMessage);
            Assert.Equal(originalMessage, decryptedMessage);
        }

        [Fact]
        public void EncryptAndDecrypt_MediumMessage_WorksCorrectly()
        {
            // Arrange
            var (publicKey, privateKey) = _encryptionService.GenerateKeyPair();
            var originalMessage = "This message contains special characters like !@#$%^&*()_+{}|:<>?~`-=[]\\;',./";

            // Act
            var encryptedMessage = _encryptionService.EncryptMessage(originalMessage, publicKey);
            var decryptedMessage = _encryptionService.DecryptMessage(encryptedMessage, privateKey);

            // Assert
            Assert.Equal(originalMessage, decryptedMessage);
        }

        [Fact]
        public void DecryptMessage_WithWrongPrivateKey_ThrowsException()
        {
            // Arrange
            var (publicKey1, _) = _encryptionService.GenerateKeyPair();
            var (_, privateKey2) = _encryptionService.GenerateKeyPair();
            var originalMessage = "This is a secret message";
            var encryptedMessage = _encryptionService.EncryptMessage(originalMessage, publicKey1);

            // Act & Assert
            Assert.Throws<CryptographicException>(() =>
                _encryptionService.DecryptMessage(encryptedMessage, privateKey2));
        }

        [Fact]
        public void EncryptMessage_WithInvalidPublicKey_ThrowsException()
        {
            // Arrange
            var invalidPublicKey = "InvalidKey";
            var message = "This is a secret message";

            // Act & Assert
            Assert.Throws<FormatException>(() =>
                _encryptionService.EncryptMessage(message, invalidPublicKey));
        }

        [Fact]
        public void DecryptMessage_WithInvalidPrivateKey_ThrowsException()
        {
            // Arrange
            var (publicKey, _) = _encryptionService.GenerateKeyPair();
            var invalidPrivateKey = "InvalidKey";
            var message = "This is a secret message";
            var encryptedMessage = _encryptionService.EncryptMessage(message, publicKey);

            // Act & Assert
            Assert.Throws<FormatException>(() =>
                _encryptionService.DecryptMessage(encryptedMessage, invalidPrivateKey));
        }

        [Fact]
        public void DecryptMessage_WithInvalidEncryptedMessage_ThrowsException()
        {
            // Arrange
            var (_, privateKey) = _encryptionService.GenerateKeyPair();
            var invalidEncryptedMessage = "InvalidEncryptedMessage";

            // Act & Assert
            Assert.Throws<FormatException>(() =>
                _encryptionService.DecryptMessage(invalidEncryptedMessage, privateKey));
        }

        [Fact]
        public void MultipleKeyPairs_AreUnique()
        {
            // Act
            var (publicKey1, privateKey1) = _encryptionService.GenerateKeyPair();
            var (publicKey2, privateKey2) = _encryptionService.GenerateKeyPair();

            // Assert
            Assert.NotEqual(publicKey1, publicKey2);
            Assert.NotEqual(privateKey1, privateKey2);
        }

        [Fact]
        public void CrossKeyPairEncryption_Works()
        {
            // Arrange
            var (alicePublicKey, alicePrivateKey) = _encryptionService.GenerateKeyPair();
            var (bobPublicKey, bobPrivateKey) = _encryptionService.GenerateKeyPair();

            var messageFromAliceToBob = "Hi Bob, this is Alice!";
            var messageFromBobToAlice = "Hi Alice, this is Bob!";

            // Act - Alice encrypts a message for Bob using Bob's public key
            var encryptedMessageForBob = _encryptionService.EncryptMessage(messageFromAliceToBob, bobPublicKey);
            // Bob decrypts the message using his private key
            var decryptedMessageByBob = _encryptionService.DecryptMessage(encryptedMessageForBob, bobPrivateKey);

            // Bob encrypts a message for Alice using Alice's public key
            var encryptedMessageForAlice = _encryptionService.EncryptMessage(messageFromBobToAlice, alicePublicKey);
            // Alice decrypts the message using her private key
            var decryptedMessageByAlice = _encryptionService.DecryptMessage(encryptedMessageForAlice, alicePrivateKey);

            // Assert
            Assert.Equal(messageFromAliceToBob, decryptedMessageByBob);
            Assert.Equal(messageFromBobToAlice, decryptedMessageByAlice);
        }

        // Helper method to check if a string is valid Base64
        private bool IsBase64String(string base64)
        {
            if (string.IsNullOrEmpty(base64) || base64.Length % 4 != 0)
                return false;

            try
            {
                Convert.FromBase64String(base64);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
