using System.Collections.Generic;
using System.Linq;
using BlindDateApi.Data.Models;
using Xunit;

namespace BlindDateApi.Tests.Models
{
    public class InterestTests
    {
        [Fact]
        public void Interest_CreatesWithEmptyProfileInterests()
        {
            // Arrange & Act
            var interest = new Interest();

            // Assert
            Assert.NotNull(interest.ProfileInterests);
            Assert.Empty(interest.ProfileInterests);
        }

        [Fact]
        public void Interest_PropertiesSetCorrectly()
        {
            // Arrange
            var interest = new Interest
            {
                Id = 1,
                Name = "Music",
                Description = "Music and concerts"
            };

            // Act & Assert
            Assert.Equal(1, interest.Id);
            Assert.Equal("Music", interest.Name);
            Assert.Equal("Music and concerts", interest.Description);
        }

        [Fact]
        public void ProfileInterest_LinksBothEntities()
        {
            // Arrange
            var profile = new Profile
            {
                Id = 1,
                FirstName = "John",
                LastName = "Doe"
            };

            var interest = new Interest
            {
                Id = 1,
                Name = "Music"
            };

            var profileInterest = new ProfileInterest
            {
                Id = 1,
                ProfileId = profile.Id,
                InterestId = interest.Id,
                Profile = profile,
                Interest = interest
            };

            // Act
            profile.ProfileInterests.Add(profileInterest);
            interest.ProfileInterests.Add(profileInterest);

            // Assert
            Assert.Single(profile.ProfileInterests);
            Assert.Single(interest.ProfileInterests);
            Assert.Same(profileInterest, profile.ProfileInterests.First());
            Assert.Same(profileInterest, interest.ProfileInterests.First());
            Assert.Same(profile, profileInterest.Profile);
            Assert.Same(interest, profileInterest.Interest);
        }
    }
}
