using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class ChatService : IChatService
    {
        private readonly IRepository<Conversation> _conversationRepository;
        private readonly IRepository<ConversationParticipant> _participantRepository;
        private readonly IRepository<Message> _messageRepository;
        private readonly IUserRepository _userRepository;
        private readonly IRepository<Profile> _profileRepository;
        private readonly ICacheService _cacheService;
        private readonly IChatNotificationService _notificationService;
        private readonly ILogger<ChatService> _logger;

        private const string RemainingChatsKey = "RemainingChats_{0}_{1}"; // UserId_Date

        public ChatService(
            IRepository<Conversation> conversationRepository,
            IRepository<ConversationParticipant> participantRepository,
            IRepository<Message> messageRepository,
            IUserRepository userRepository,
            IRepository<Profile> profileRepository,
            ICacheService cacheService,
            IChatNotificationService notificationService,
            ILogger<ChatService> logger)
        {
            _conversationRepository = conversationRepository;
            _participantRepository = participantRepository;
            _messageRepository = messageRepository;
            _userRepository = userRepository;
            _profileRepository = profileRepository;
            _cacheService = cacheService;
            _notificationService = notificationService;
            _logger = logger;
        }

        public async Task<ConversationDto> CreateConversationAsync(int userId, CreateConversationDto dto)
        {
            // Check if user has reached chat limit
            if (await HasReachedChatLimitAsync(userId))
            {
                throw new InvalidOperationException("You have reached your daily chat limit.");
            }

            // Validate participants
            if (!dto.ParticipantIds.Contains(userId))
            {
                dto.ParticipantIds.Add(userId); // Ensure the creator is a participant
            }

            if (dto.ParticipantIds.Count < 2)
            {
                throw new ArgumentException("A conversation must have at least 2 participants.");
            }

            // Create the conversation
            var conversation = new Conversation
            {
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(2), // 2-minute timer for new conversations
                PublicKey = dto.PublicKey
            };

            await _conversationRepository.AddAsync(conversation);
            await _conversationRepository.SaveChangesAsync();

            // Add participants
            foreach (var participantId in dto.ParticipantIds)
            {
                var encryptedKey = dto.EncryptedKeys.ContainsKey(participantId)
                    ? dto.EncryptedKeys[participantId]
                    : string.Empty;

                var participant = new ConversationParticipant
                {
                    ConversationId = conversation.Id,
                    UserId = participantId,
                    EncryptedConversationKey = encryptedKey
                };

                await _participantRepository.AddAsync(participant);
            }

            await _participantRepository.SaveChangesAsync();

            // Decrement remaining chats for the user
            await DecrementRemainingChatsAsync(userId);

            // Get the conversation DTO
            var conversationDto = await GetConversationAsync(userId, conversation.Id);

            // Notify participants about the new conversation
            await _notificationService.NotifyConversationCreated(userId, conversationDto);

            // Return the created conversation
            return conversationDto;
        }

        public async Task<List<ConversationDto>> GetConversationsAsync(int userId)
        {
            // Get all conversations where the user is a participant
            var participations = await _participantRepository.GetAllAsync(
                p => p.UserId == userId,
                include => include
                    .Include(p => p.Conversation)
                    .ThenInclude(c => c.Messages)
                    .Include(p => p.Conversation)
                    .ThenInclude(c => c.Participants)
                    .ThenInclude(p => p.User)
                    .ThenInclude(u => u.Profile)
            );

            var conversations = new List<ConversationDto>();

            foreach (var participation in participations)
            {
                var conversation = participation.Conversation;
                if (conversation == null) continue;

                var dto = MapToConversationDto(conversation, userId);
                conversations.Add(dto);
            }

            // Sort by last message time or creation time if no messages
            return conversations
                .OrderByDescending(c => c.LastMessage?.SentAt ?? c.CreatedAt)
                .ToList();
        }

        public async Task<ConversationDto> GetConversationAsync(int userId, int conversationId)
        {
            // Check if user is a participant
            var participant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == conversationId && p.UserId == userId,
                include => include
                    .Include(p => p.Conversation)
                    .ThenInclude(c => c.Messages)
                    .Include(p => p.Conversation)
                    .ThenInclude(c => c.Participants)
                    .ThenInclude(p => p.User)
                    .ThenInclude(u => u.Profile)
            );

            if (participant == null || participant.Conversation == null)
            {
                throw new InvalidOperationException("Conversation not found or you are not a participant.");
            }

            return MapToConversationDto(participant.Conversation, userId);
        }

        public async Task<List<ChatMessageDto>> GetMessagesAsync(int userId, int conversationId, int? lastMessageId = null, int count = 20)
        {
            // Check if user is a participant
            var isParticipant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == conversationId && p.UserId == userId
            ) != null;

            if (!isParticipant)
            {
                throw new InvalidOperationException("You are not a participant in this conversation.");
            }

            // Get messages
            var query = _messageRepository.GetAllAsync(
                m => m.ConversationId == conversationId,
                include => include
                    .Include(m => m.Sender)
            );

            var messages = (await query).AsQueryable();

            // Apply pagination
            if (lastMessageId.HasValue)
            {
                var lastMessage = await _messageRepository.GetByIdAsync(lastMessageId.Value);
                if (lastMessage != null)
                {
                    messages = messages.Where(m => m.SentAt < lastMessage.SentAt);
                }
            }

            // Get the messages and map to DTOs
            var messageDtos = messages
                .OrderByDescending(m => m.SentAt)
                .Take(count)
                .Select(m => MapToMessageDto(m))
                .ToList();

            return messageDtos;
        }

        public async Task<ChatMessageDto> SendMessageAsync(int userId, SendChatMessageDto dto)
        {
            // Check if user is a participant
            var participant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == dto.ConversationId && p.UserId == userId,
                include => include.Include(p => p.Conversation)
            );

            if (participant == null || participant.Conversation == null)
            {
                throw new InvalidOperationException("Conversation not found or you are not a participant.");
            }

            // Check if conversation is expired and not matched
            if (participant.Conversation.IsExpired && participant.Conversation.MatchId == null)
            {
                throw new InvalidOperationException("This conversation has expired.");
            }

            // Create the message
            var message = new Message
            {
                ConversationId = dto.ConversationId,
                SenderId = userId,
                Content = dto.Content,
                EncryptedContent = dto.EncryptedContent,
                EncryptedKey = dto.EncryptedKey,
                IV = dto.IV,
                SentAt = DateTime.UtcNow,
                Type = dto.Type
            };

            await _messageRepository.AddAsync(message);
            await _messageRepository.SaveChangesAsync();

            // Update participant message count
            participant.MessageCount++;
            _participantRepository.Update(participant);
            await _participantRepository.SaveChangesAsync();

            // Get the sender's name for the DTO
            var sender = await _userRepository.GetUserWithProfileAsync(userId);
            var senderName = sender?.Profile?.FirstName ?? "Unknown";

            // Get all participants for the conversation
            var participants = await _participantRepository.GetAllAsync(
                p => p.ConversationId == dto.ConversationId
            );
            var participantIds = participants.Select(p => p.UserId).ToList();

            // Create the DTO
            var messageDto = new ChatMessageDto
            {
                Id = message.Id,
                ConversationId = message.ConversationId ?? 0,
                SenderId = message.SenderId,
                SenderName = senderName,
                SentAt = message.SentAt,
                EncryptedContent = message.EncryptedContent,
                EncryptedKey = message.EncryptedKey,
                IV = message.IV,
                IsRead = false,
                Type = message.Type,
                ParticipantIds = participantIds
            };

            // Notify participants about the new message
            await _notificationService.NotifyMessageSent(dto.ConversationId, messageDto);

            return messageDto;
        }

        public async Task<bool> MarkAsReadAsync(int userId, MarkAsReadDto dto)
        {
            // Check if user is a participant
            var participant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == dto.ConversationId && p.UserId == userId
            );

            if (participant == null)
            {
                throw new InvalidOperationException("Conversation not found or you are not a participant.");
            }

            // Update last read time
            participant.LastReadAt = DateTime.UtcNow;
            _participantRepository.Update(participant);
            await _participantRepository.SaveChangesAsync();

            // Mark messages as read
            var messages = await _messageRepository.GetAllAsync(
                m => m.ConversationId == dto.ConversationId &&
                     m.SenderId != userId &&
                     !m.IsRead &&
                     (dto.LastMessageId == null || m.Id <= dto.LastMessageId)
            );

            foreach (var message in messages)
            {
                message.IsRead = true;
                message.ReadAt = DateTime.UtcNow;
                _messageRepository.Update(message);
            }

            await _messageRepository.SaveChangesAsync();

            // Notify participants that messages have been read
            if (dto.LastMessageId.HasValue)
            {
                await _notificationService.NotifyMessageRead(dto.ConversationId, userId, dto.LastMessageId.Value);
            }

            return true;
        }

        public async Task<bool> RequestRevealAsync(int userId, RevealRequestDto dto)
        {
            // Check if user is a participant
            var participant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == dto.ConversationId && p.UserId == userId,
                include => include.Include(p => p.Conversation)
            );

            if (participant == null || participant.Conversation == null)
            {
                throw new InvalidOperationException("Conversation not found or you are not a participant.");
            }

            // Check if conversation is expired and not matched
            if (participant.Conversation.IsExpired && participant.Conversation.MatchId == null)
            {
                throw new InvalidOperationException("This conversation has expired.");
            }

            // Check if the requested user is a participant
            var requestedParticipant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == dto.ConversationId && p.UserId == dto.RequestedUserId
            );

            if (requestedParticipant == null)
            {
                throw new InvalidOperationException("The requested user is not a participant in this conversation.");
            }

            // Create a reveal request message
            var message = new Message
            {
                ConversationId = dto.ConversationId,
                SenderId = userId,
                EncryptedContent = $"{{\"requestedUserId\":{dto.RequestedUserId}}}",
                SentAt = DateTime.UtcNow,
                Type = MessageType.RevealRequest
            };

            await _messageRepository.AddAsync(message);
            await _messageRepository.SaveChangesAsync();

            return true;
        }

        public async Task<bool> RespondToRevealAsync(int userId, RevealResponseDto dto)
        {
            // Check if user is a participant
            var participant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == dto.ConversationId && p.UserId == userId,
                include => include.Include(p => p.Conversation)
            );

            if (participant == null || participant.Conversation == null)
            {
                throw new InvalidOperationException("Conversation not found or you are not a participant.");
            }

            // Check if conversation is expired and not matched
            if (participant.Conversation.IsExpired && participant.Conversation.MatchId == null)
            {
                throw new InvalidOperationException("This conversation has expired.");
            }

            // Find the latest reveal request
            var revealRequest = (await _messageRepository.GetAllAsync(
                m => m.ConversationId == dto.ConversationId &&
                     m.Type == MessageType.RevealRequest &&
                     m.EncryptedContent.Contains($"\"requestedUserId\":{userId}")
            )).OrderByDescending(m => m.SentAt).FirstOrDefault();

            if (revealRequest == null)
            {
                throw new InvalidOperationException("No reveal request found.");
            }

            // Create a response message
            var message = new Message
            {
                ConversationId = dto.ConversationId,
                SenderId = userId,
                EncryptedContent = $"{{\"accepted\":{dto.Accept.ToString().ToLower()}}}",
                SentAt = DateTime.UtcNow,
                Type = dto.Accept ? MessageType.RevealAccepted : MessageType.RevealRejected
            };

            await _messageRepository.AddAsync(message);

            // If accepted, update the participant's reveal status
            if (dto.Accept)
            {
                participant.HasRevealedPicture = true;
                _participantRepository.Update(participant);
            }

            await _messageRepository.SaveChangesAsync();
            await _participantRepository.SaveChangesAsync();

            return true;
        }

        public async Task<bool> DeleteConversationAsync(int userId, int conversationId)
        {
            // Check if user is a participant
            var participant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == conversationId && p.UserId == userId
            );

            if (participant == null)
            {
                throw new InvalidOperationException("Conversation not found or you are not a participant.");
            }

            // Remove the participant
            await _participantRepository.DeleteAsync(participant.Id);

            // Check if there are any participants left
            var remainingParticipants = await _participantRepository.GetAllAsync(
                p => p.ConversationId == conversationId
            );

            // If no participants left, delete the conversation and its messages
            if (!remainingParticipants.Any())
            {
                var messages = await _messageRepository.GetAllAsync(
                    m => m.ConversationId == conversationId
                );

                foreach (var message in messages)
                {
                    await _messageRepository.DeleteAsync(message.Id);
                }

                await _conversationRepository.DeleteAsync(conversationId);
            }

            return true;
        }

        public async Task<bool> IsConversationExpiredAsync(int conversationId)
        {
            var conversation = await _conversationRepository.GetByIdAsync(conversationId);
            return conversation?.IsExpired ?? false;
        }

        public async Task<int> GetRemainingChatsAsync(int userId)
        {
            // Get the user's subscription
            var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
            if (user == null || user.CurrentSubscription == null)
            {
                return 0;
            }

            var subscription = user.CurrentSubscription.Subscription;
            if (subscription == null)
            {
                return 0;
            }

            // If unlimited chats (-1), return a high number
            if (subscription.ChatsPerDay < 0)
            {
                return 999;
            }

            // Check cache for remaining chats
            var today = DateTime.UtcNow.Date.ToString("yyyyMMdd");
            var cacheKey = string.Format(RemainingChatsKey, userId, today);

            var remainingChats = await _cacheService.GetAsync<int?>(cacheKey);
            if (remainingChats.HasValue)
            {
                return remainingChats.Value;
            }

            // If not in cache, calculate remaining chats
            var startOfDay = DateTime.UtcNow.Date;
            var endOfDay = startOfDay.AddDays(1);

            var conversationsStartedToday = await _participantRepository.GetAllAsync(
                p => p.UserId == userId &&
                     p.Conversation.CreatedAt >= startOfDay &&
                     p.Conversation.CreatedAt < endOfDay
            );

            var chatsUsed = conversationsStartedToday.Count();
            var remaining = Math.Max(0, subscription.ChatsPerDay - chatsUsed);

            // Cache the result
            await _cacheService.SetAsync(cacheKey, remaining, TimeSpan.FromHours(1));

            return remaining;
        }

        public async Task<bool> HasReachedChatLimitAsync(int userId)
        {
            var remaining = await GetRemainingChatsAsync(userId);
            return remaining <= 0;
        }

        public async Task<bool> UpdateMessagesWithMatchIdAsync(int conversationId, int matchId, int userId)
        {
            // Verify the user is part of the conversation
            var participant = await _participantRepository.SingleOrDefaultAsync(
                p => p.ConversationId == conversationId && p.UserId == userId,
                include => include.Include(p => p.Conversation)
            );

            if (participant == null || participant.Conversation == null)
            {
                throw new InvalidOperationException("Conversation not found or you are not a participant.");
            }

            // Update the conversation with the match ID and reset expiry date
            var conversation = participant.Conversation;
            conversation.MatchId = matchId;

            // Reset the expiry date to null since this is now a matched conversation
            // and should not expire
            conversation.ExpiresAt = null;

            _conversationRepository.Update(conversation);
            await _conversationRepository.SaveChangesAsync();

            // Get all messages in the conversation
            var messages = await _messageRepository.GetAllAsync(
                m => m.ConversationId == conversationId
            );

            // Update all messages with the match ID
            foreach (var message in messages)
            {
                message.MatchId = matchId;
                _messageRepository.Update(message);
            }

            await _messageRepository.SaveChangesAsync();
            return true;
        }

        private async Task DecrementRemainingChatsAsync(int userId)
        {
            var today = DateTime.UtcNow.Date.ToString("yyyyMMdd");
            var cacheKey = string.Format(RemainingChatsKey, userId, today);

            var remainingChats = await _cacheService.GetAsync<int?>(cacheKey);
            if (remainingChats.HasValue)
            {
                await _cacheService.SetAsync(cacheKey, Math.Max(0, remainingChats.Value - 1), TimeSpan.FromHours(1));
            }
        }

        private ConversationDto MapToConversationDto(Conversation conversation, int currentUserId)
        {
            var participants = new List<ParticipantDto>();
            var unreadCount = 0;

            foreach (var participant in conversation.Participants)
            {
                if (participant.User?.Profile == null) continue;

                var profile = participant.User.Profile;

                // Calculate age
                var age = profile.Age;

                // Determine if profile picture should be shown
                string? profilePictureUrl = null;

                // For VIP users, always show profile pictures
                var isVipUser = participant.User.CurrentSubscription?.Subscription?.Name == "VIP";

                // For the current user, always show their own picture
                var isCurrentUser = participant.UserId == currentUserId;

                // For users who have revealed their picture
                var hasRevealedPicture = participant.HasRevealedPicture;

                // For premium users who have exchanged enough messages
                var isPremiumUser = participant.User.CurrentSubscription?.Subscription?.Name == "Premium";
                var hasEnoughMessages = participant.MessageCount >= 10; // Premium users reveal after 10 messages

                if (isVipUser || isCurrentUser || hasRevealedPicture || (isPremiumUser && hasEnoughMessages))
                {
                    // Get the first picture from the profile's pictures list
                    profilePictureUrl = profile.PicturesList.FirstOrDefault();
                }

                // Create participant DTO
                var participantDto = new ParticipantDto
                {
                    UserId = participant.UserId,
                    FirstName = profile.FirstName,
                    LastName = isCurrentUser || hasRevealedPicture ? profile.LastName : null,
                    Age = age,
                    ProfilePictureUrl = profilePictureUrl,
                    HasRevealedPicture = participant.HasRevealedPicture,
                    PublicKey = participant.PublicKey,
                    LastReadAt = participant.LastReadAt,
                    MessageCount = participant.MessageCount
                };

                participants.Add(participantDto);
            }

            // Get the last message
            var lastMessage = conversation.Messages
                .OrderByDescending(m => m.SentAt)
                .FirstOrDefault();

            // Count unread messages
            var currentUserLastRead = conversation.Participants
                .FirstOrDefault(p => p.UserId == currentUserId)?.LastReadAt;

            if (currentUserLastRead.HasValue)
            {
                unreadCount = conversation.Messages
                    .Count(m => m.SenderId != currentUserId && m.SentAt > currentUserLastRead.Value);
            }
            else
            {
                unreadCount = conversation.Messages
                    .Count(m => m.SenderId != currentUserId);
            }

            // Create conversation DTO
            var dto = new ConversationDto
            {
                Id = conversation.Id,
                CreatedAt = conversation.CreatedAt,
                ExpiresAt = conversation.ExpiresAt,
                IsExpired = conversation.IsExpired,
                MatchId = conversation.MatchId,
                Participants = participants,
                LastMessage = lastMessage != null ? MapToMessageDto(lastMessage) : null,
                UnreadCount = unreadCount,
                PublicKey = conversation.PublicKey
            };

            return dto;
        }

        private ChatMessageDto MapToMessageDto(Message message)
        {
            return new ChatMessageDto
            {
                Id = message.Id,
                ConversationId = message.ConversationId ?? 0,
                SenderId = message.SenderId,
                SenderName = message.Sender?.Profile?.FirstName ?? "Unknown",
                SentAt = message.SentAt,
                EncryptedContent = message.EncryptedContent,
                Content = message.Content,
                EncryptedKey = message.EncryptedKey,
                IV = message.IV,
                IsRead = message.IsRead,
                ReadAt = message.ReadAt,
                Type = message.Type
            };
        }
    }
}
