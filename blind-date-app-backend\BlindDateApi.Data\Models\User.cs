using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlindDateApi.Data.Models
{
    public class User
    {
        public User()
        {
            InitiatedMatches = new HashSet<Match>();
            ReceivedMatches = new HashSet<Match>();
            SentMessages = new HashSet<Message>();
            Subscriptions = new HashSet<UserSubscription>();
        }

        [Key]
        public int Id { get; set; }

        [Required]
        [EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastLoginAt { get; set; }

        public bool IsActive { get; set; } = true;

        public bool IsOnline { get; set; } = false;

        public DateTime? LastActiveAt { get; set; }

        // Public key for end-to-end encryption
        [MaxLength(2048)]
        public string? PublicKey { get; set; }

        // Encrypted private key for end-to-end encryption
        [MaxLength(4096)]
        public string? EncryptedPrivateKey { get; set; }

        // Salt used for encrypting the private key
        [MaxLength(64)]
        public string? PrivateKeySalt { get; set; }

        // IV used for encrypting the private key
        [MaxLength(64)]
        public string? PrivateKeyIV { get; set; }

        // Encrypted master private key for end-to-end encryption
        [MaxLength(4096)]
        public string? EncryptedMasterPrivateKey { get; set; }

        // Salt used for encrypting the private master key
        [MaxLength(64)]
        public string? PrivateMasterKeySalt { get; set; }

        // IV used for encrypting the private master key
        [MaxLength(64)]
        public string? PrivateMasterKeyIV { get; set; }

        // External login provider (e.g., "google", "facebook")
        [MaxLength(50)]
        public string? ExternalLoginProvider { get; set; }

        // External login provider ID
        [MaxLength(100)]
        public string? ExternalLoginId { get; set; }

        // Password reset token
        [MaxLength(100)]
        public string? PasswordResetToken { get; set; }

        // Password reset token expiration
        public DateTime? PasswordResetTokenExpires { get; set; }

        // Stripe customer ID
        [MaxLength(100)]
        public string? StripeCustomerId { get; set; }

        // Navigation properties
        public virtual Profile? Profile { get; set; }
        public virtual ICollection<Match> InitiatedMatches { get; set; }
        public virtual ICollection<Match> ReceivedMatches { get; set; }
        public virtual ICollection<Message> SentMessages { get; set; }
        public virtual ICollection<UserSubscription> Subscriptions { get; set; }

        // Helper property to get current subscription (not mapped to DB)
        [NotMapped]
        public UserSubscription? CurrentSubscription =>
            Subscriptions?.OrderByDescending(s => s.ExpiresAt)
                .FirstOrDefault(s => s.ExpiresAt > DateTime.UtcNow);
    }
}
