using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BlindDateApi.Data.Models
{
    public class Subscription
    {
        public Subscription()
        {
            Users = new HashSet<UserSubscription>();
        }

        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public decimal Price { get; set; }

        // Number of chats per day (-1 for unlimited)
        public int ChatsPerDay { get; set; }

        // Maximum number of interests
        public int MaxInterests { get; set; }

        // Maximum number of pictures
        public int MaxPictures { get; set; }

        // When pictures are revealed: "Match", "10 Messages", "Immediate"
        [MaxLength(50)]
        public string PictureRevealCondition { get; set; } = string.Empty;

        // Navigation property
        public virtual ICollection<UserSubscription> Users { get; set; }
    }
}
