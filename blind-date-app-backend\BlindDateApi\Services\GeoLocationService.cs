using System;

namespace BlindDateApi.Services
{
    public class GeoLocationService : IGeoLocationService
    {
        private const double EarthRadiusKm = 6371.0;
        
        /// <summary>
        /// Calculate distance between two points using the Haversine formula
        /// </summary>
        /// <param name="lat1">Latitude of first point</param>
        /// <param name="lon1">Longitude of first point</param>
        /// <param name="lat2">Latitude of second point</param>
        /// <param name="lon2">Longitude of second point</param>
        /// <returns>Distance in kilometers</returns>
        public double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
        {
            // Convert degrees to radians
            var dLat = DegreesToRadians(lat2 - lat1);
            var dLon = DegreesToRadians(lon2 - lon1);
            
            // Haversine formula
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(DegreesToRadians(lat1)) * Math.Cos(DegreesToRadians(lat2)) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
                    
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            var distance = EarthRadiusKm * c;
            
            return Math.Round(distance, 1);
        }
        
        /// <summary>
        /// Check if a user is within the specified distance of another user
        /// </summary>
        /// <param name="userLat">User's latitude</param>
        /// <param name="userLon">User's longitude</param>
        /// <param name="otherLat">Other user's latitude</param>
        /// <param name="otherLon">Other user's longitude</param>
        /// <param name="maxDistance">Maximum distance in kilometers</param>
        /// <returns>True if within distance, false otherwise</returns>
        public bool IsWithinDistance(double userLat, double userLon, double otherLat, double otherLon, int maxDistance)
        {
            var distance = CalculateDistance(userLat, userLon, otherLat, otherLon);
            return distance <= maxDistance;
        }
        
        /// <summary>
        /// Convert degrees to radians
        /// </summary>
        /// <param name="degrees">Angle in degrees</param>
        /// <returns>Angle in radians</returns>
        private double DegreesToRadians(double degrees)
        {
            return degrees * Math.PI / 180.0;
        }
    }
}
