﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BlindDateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddEncryptedPrivateKeyToUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "EncryptedPrivateKey",
                table: "Users",
                type: "nvarchar(max)",
                maxLength: 4096,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PrivateKeyIV",
                table: "Users",
                type: "nvarchar(64)",
                maxLength: 64,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PrivateKeySalt",
                table: "Users",
                type: "nvarchar(64)",
                maxLength: 64,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EncryptedPrivateKey",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "PrivateKeyIV",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "PrivateKeySalt",
                table: "Users");
        }
    }
}
