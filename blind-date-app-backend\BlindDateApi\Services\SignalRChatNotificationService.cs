using System;
using System.Threading.Tasks;
using BlindDateApi.DTOs;
using BlindDateApi.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class SignalRChatNotificationService : IChatNotificationService
    {
        private readonly IHubContext<ChatHub> _hubContext;
        private readonly ILogger<SignalRChatNotificationService> _logger;

        public SignalRChatNotificationService(
            IHubContext<ChatHub> hubContext,
            ILogger<SignalRChatNotificationService> logger)
        {
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task NotifyMessageSent(int conversationId, ChatMessageDto message)
        {
            try
            {
                // Send to the conversation group
                await _hubContext.Clients
                    .Group($"Conversation_{conversationId}")
                    .SendAsync("ReceiveMessage", message);

                // Also send to the recipient's user group (for notifications when not in the conversation)
                if (message.SenderId > 0)
                {
                    // Get all participants except the sender
                    var otherParticipants = message.ParticipantIds ?? new List<int>();
                    foreach (var participantId in otherParticipants)
                    {
                        if (participantId != message.SenderId)
                        {
                            await _hubContext.Clients
                                .Group($"User_{participantId}")
                                .SendAsync("ReceiveNotification", new
                                {
                                    Type = "NewMessage",
                                    ConversationId = conversationId,
                                    Message = message
                                });
                        }
                    }
                }

                _logger.LogInformation("Notified clients about new message in conversation {ConversationId}", conversationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error notifying about message in conversation {ConversationId}", conversationId);
            }
        }

        public async Task NotifyConversationCreated(int userId, ConversationDto conversation)
        {
            try
            {
                // Notify each participant about the new conversation
                foreach (var participantId in conversation.ParticipantIds)
                {
                    if (participantId != userId) // Don't notify the creator
                    {
                        await _hubContext.Clients
                            .Group($"User_{participantId}")
                            .SendAsync("ReceiveNotification", new
                            {
                                Type = "NewConversation",
                                Conversation = conversation
                            });
                    }
                }

                _logger.LogInformation("Notified participants about new conversation {ConversationId}", conversation.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error notifying about new conversation {ConversationId}", conversation.Id);
            }
        }

        public async Task NotifyMessageRead(int conversationId, int userId, int lastMessageId)
        {
            try
            {
                // Notify the conversation group that messages have been read
                await _hubContext.Clients
                    .Group($"Conversation_{conversationId}")
                    .SendAsync("MessagesRead", new
                    {
                        ConversationId = conversationId,
                        UserId = userId,
                        LastMessageId = lastMessageId
                    });

                _logger.LogInformation("Notified conversation {ConversationId} that user {UserId} read messages up to {LastMessageId}",
                    conversationId, userId, lastMessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error notifying about read messages in conversation {ConversationId}", conversationId);
            }
        }
    }
}
