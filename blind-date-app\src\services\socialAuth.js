// Social authentication service
// This service handles the OAuth flow for different providers

// Google OAuth configuration
const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || '*********-example.apps.googleusercontent.com'

// Facebook OAuth configuration
const FACEBOOK_APP_ID = import.meta.env.VITE_FACEBOOK_APP_ID || '*********'

/**
 * Initialize Google OAuth API
 * This function loads the Google Identity Services library
 */
export const initGoogleAuth = () => {
  return new Promise((resolve, reject) => {
    // Check if the Google Identity Services script is already loaded
    if (window.google && window.google.accounts) {
      resolve(window.google.accounts)
      return
    }

    // Load the Google Identity Services script
    const script = document.createElement('script')
    script.src = 'https://accounts.google.com/gsi/client'
    script.async = true
    script.defer = true
    script.onload = () => {
      if (window.google && window.google.accounts) {
        // Initialize Google Identity Services
        window.google.accounts.id.initialize({
          client_id: GOOGLE_CLIENT_ID,
          callback: (response) => {
            // This callback is called when the user signs in
            // We don't use it directly here, but it's required for initialization
          }
        })
        resolve(window.google.accounts)
      } else {
        reject(new Error('Failed to initialize Google Identity Services'))
      }
    }
    script.onerror = (error) => {
      reject(new Error('Failed to load Google Identity Services: ' + error))
    }
    document.body.appendChild(script)
  })
}

/**
 * Initialize Facebook OAuth API
 * This function loads the Facebook SDK
 */
export const initFacebookAuth = () => {
  return new Promise((resolve, reject) => {
    // Check if the Facebook SDK is already loaded
    if (window.FB) {
      resolve(window.FB)
      return
    }

    // Load the Facebook SDK
    window.fbAsyncInit = function() {
      window.FB.init({
        appId: FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v18.0' // Updated to latest version
      })
      resolve(window.FB)
    }

    // Load the Facebook SDK script
    const script = document.createElement('script')
    script.src = 'https://connect.facebook.net/en_US/sdk.js'
    script.async = true
    script.defer = true
    script.crossOrigin = 'anonymous' // Added for security
    script.onerror = (error) => {
      reject(new Error('Failed to load Facebook SDK: ' + error))
    }
    document.body.appendChild(script)
  })
}

/**
 * Sign in with Google
 * This function handles the Google OAuth flow using Google Identity Services
 * with a redirect-based approach instead of popups
 */
export const signInWithGoogle = async () => {
  try {
    // Initialize Google Auth
    const googleAccounts = await initGoogleAuth()

    // Store the current URL to return to after authentication
    const currentUrl = window.location.href
    sessionStorage.setItem('googleAuthRedirectUrl', currentUrl)

    // Create the OAuth URL
    const redirectUri = `${window.location.origin}/auth/callback`
    const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth')

    // Add OAuth parameters
    authUrl.searchParams.append('client_id', GOOGLE_CLIENT_ID)
    authUrl.searchParams.append('redirect_uri', redirectUri)
    authUrl.searchParams.append('response_type', 'token')
    authUrl.searchParams.append('scope', 'email profile openid')
    authUrl.searchParams.append('state', 'google')
    authUrl.searchParams.append('prompt', 'select_account')

    // Redirect to Google's OAuth page
    window.location.href = authUrl.toString()

    // This function will not return normally due to the redirect
    // The auth callback component will handle the response
    return new Promise(() => {})
  } catch (error) {
    console.error('Google sign in error:', error)
    throw new Error('Failed to sign in with Google: ' + (error.message || error))
  }
}

/**
 * Sign in with Facebook
 * This function handles the Facebook OAuth flow using a redirect-based approach
 */
export const signInWithFacebook = async () => {
  try {
    // Store the current URL to return to after authentication
    const currentUrl = window.location.href
    sessionStorage.setItem('facebookAuthRedirectUrl', currentUrl)

    // Create the OAuth URL
    const redirectUri = `${window.location.origin}/auth/callback`
    const authUrl = new URL('https://www.facebook.com/v18.0/dialog/oauth')

    // Add OAuth parameters
    authUrl.searchParams.append('client_id', FACEBOOK_APP_ID)
    authUrl.searchParams.append('redirect_uri', redirectUri)
    authUrl.searchParams.append('response_type', 'token')
    authUrl.searchParams.append('scope', 'email,public_profile')
    authUrl.searchParams.append('state', 'facebook')
    authUrl.searchParams.append('auth_type', 'rerequest')

    // Redirect to Facebook's OAuth page
    window.location.href = authUrl.toString()

    // This function will not return normally due to the redirect
    // The auth callback component will handle the response
    return new Promise(() => {})
  } catch (error) {
    console.error('Facebook sign in error:', error)
    throw new Error('Failed to sign in with Facebook: ' + (error.message || error))
  }
}

/**
 * Get user data from social provider
 * This function handles the OAuth flow for different providers
 */
export const getSocialUserData = async (provider) => {
  switch (provider.toLowerCase()) {
    case 'google':
      return await signInWithGoogle()
    case 'facebook':
      return await signInWithFacebook()
    default:
      throw new Error(`Unsupported provider: ${provider}`)
  }
}
