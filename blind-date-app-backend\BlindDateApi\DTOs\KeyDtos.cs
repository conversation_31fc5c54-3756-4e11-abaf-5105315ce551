using System.ComponentModel.DataAnnotations;

namespace BlindDateApi.DTOs
{
    public class PublicKeyDto
    {
        public int UserId { get; set; }
        public string PublicKey { get; set; } = string.Empty;
    }

    public class UpdatePublicKeyDto
    {
        [Required]
        public string PublicKey { get; set; } = string.Empty;
    }

    public class EncryptedPrivateKeyDto
    {
        public string EncryptedPrivateKey { get; set; } = string.Empty;
        public string Salt { get; set; } = string.Empty;
        public string IV { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
    }

    public class UpdateEncryptedPrivateKeyDto
    {
        [Required]
        public string EncryptedPrivateKey { get; set; } = string.Empty;

        [Required]
        public string Salt { get; set; } = string.Empty;

        [Required]
        public string IV { get; set; } = string.Empty;
    }

    public class UserKeysDto
    {
        public string PublicKey { get; set; } = string.Empty;
        public string? EncryptedPrivateKey { get; set; }
        public string? Salt { get; set; }
        public string? IV { get; set; }
    }
}
