{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=BlindDateDb;Trusted_Connection=True;MultipleActiveResultSets=true",
    "Redis": "" // Empty to use in-memory cache in development
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "BlindDateApi.Services": "Debug" // Enable debug logging for cache services
    }
  }
}
