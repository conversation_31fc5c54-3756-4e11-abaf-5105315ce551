using System;
using System.Security.Cryptography;
using System.Text;

namespace BlindDateApi.Services
{
    public interface IEncryptionService
    {
        (string publicKey, string privateKey) GenerateKeyPair();
        string EncryptMessage(string message, string publicKey);
        string DecryptMessage(string encryptedMessage, string privateKey);
    }

    public class EncryptionService : IEncryptionService
    {
        // Generate a new RSA key pair
        public (string publicKey, string privateKey) GenerateKeyPair()
        {
            using (var rsa = RSA.Create())
            {
                // Export the public and private keys
                var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());
                var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
                
                return (publicKey, privateKey);
            }
        }

        // Encrypt a message using the recipient's public key
        public string EncryptMessage(string message, string publicKey)
        {
            using (var rsa = RSA.Create())
            {
                // Import the recipient's public key
                rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
                
                // Encrypt the message
                byte[] messageBytes = Encoding.UTF8.GetBytes(message);
                byte[] encryptedBytes = rsa.Encrypt(messageBytes, RSAEncryptionPadding.OaepSHA256);
                
                // Return the encrypted message as a Base64 string
                return Convert.ToBase64String(encryptedBytes);
            }
        }

        // Decrypt a message using the recipient's private key
        public string DecryptMessage(string encryptedMessage, string privateKey)
        {
            using (var rsa = RSA.Create())
            {
                // Import the recipient's private key
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
                
                // Decrypt the message
                byte[] encryptedBytes = Convert.FromBase64String(encryptedMessage);
                byte[] decryptedBytes = rsa.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
                
                // Return the decrypted message
                return Encoding.UTF8.GetString(decryptedBytes);
            }
        }
    }
}
