import { loadStripe } from '@stripe/stripe-js'
import { apiClient } from './api'

let stripePromise = null

export const getStripe = async () => {
  if (!stripePromise) {
    // Get the publishable key from the backend
    const response = await apiClient.get('/payments/config')
    const publishableKey = response.data.publishableKey
    
    // Initialize Stripe with the publishable key
    stripePromise = loadStripe(publishableKey)
  }
  return stripePromise
}

export const createPaymentIntent = async (subscriptionId) => {
  const response = await apiClient.post('/payments/create-payment-intent', {
    subscriptionId
  })
  return response.data.clientSecret
}

export const confirmPayment = async (paymentIntentId) => {
  const response = await apiClient.post('/payments/confirm-payment', {
    paymentIntentId
  })
  return response.data
}

export const cancelSubscription = async () => {
  const response = await apiClient.post('/payments/cancel-subscription')
  return response.data
}
