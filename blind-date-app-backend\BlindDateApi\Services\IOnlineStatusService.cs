using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BlindDateApi.Services
{
    public interface IOnlineStatusService
    {
        /// <summary>
        /// Sets a user as online
        /// </summary>
        Task SetUserOnlineAsync(int userId);

        /// <summary>
        /// Sets a user as offline
        /// </summary>
        Task SetUserOfflineAsync(int userId);

        /// <summary>
        /// Updates a user's last active timestamp
        /// </summary>
        Task UpdateUserActivityAsync(int userId);

        /// <summary>
        /// Gets all online users
        /// </summary>
        Task<List<int>> GetOnlineUserIdsAsync();

        /// <summary>
        /// Checks if a user is online
        /// </summary>
        Task<bool> IsUserOnlineAsync(int userId);

        /// <summary>
        /// Cleans up inactive users (marks them as offline if they haven't been active for a while)
        /// </summary>
        Task CleanupInactiveUsersAsync(TimeSpan inactivityThreshold);
    }
}
