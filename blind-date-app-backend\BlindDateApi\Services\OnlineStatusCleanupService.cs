using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class OnlineStatusCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<OnlineStatusCleanupService> _logger;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _inactivityThreshold = TimeSpan.FromMinutes(15);

        public OnlineStatusCleanupService(
            IServiceProvider serviceProvider,
            ILogger<OnlineStatusCleanupService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Online Status Cleanup Service is starting");

            while (!stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("Running cleanup of inactive users");

                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var onlineStatusService = scope.ServiceProvider.GetRequiredService<IOnlineStatusService>();
                        await onlineStatusService.CleanupInactiveUsersAsync(_inactivityThreshold);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while cleaning up inactive users");
                }

                _logger.LogInformation("Cleanup complete. Waiting for next interval");
                await Task.Delay(_cleanupInterval, stoppingToken);
            }

            _logger.LogInformation("Online Status Cleanup Service is stopping");
        }
    }
}
