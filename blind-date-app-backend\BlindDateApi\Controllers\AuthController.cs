using System;
using System.Threading.Tasks;
using System.Linq;
using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly IRepository<Subscription> _subscriptionRepository;
        private readonly IRepository<UserSubscription> _userSubscriptionRepository;
        private readonly IJwtService _jwtService;
        private readonly IPasswordValidationService _passwordValidationService;
        private readonly IOnlineStatusService _onlineStatusService;
        private readonly IEncryptionService _encryptionService;
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IUserRepository userRepository,
            IRepository<Subscription> subscriptionRepository,
            IRepository<UserSubscription> userSubscriptionRepository,
            IJwtService jwtService,
            IPasswordValidationService passwordValidationService,
            IOnlineStatusService onlineStatusService,
            IEncryptionService encryptionService,
            IEmailService emailService,
            IConfiguration configuration,
            ILogger<AuthController> logger)
        {
            _userRepository = userRepository;
            _subscriptionRepository = subscriptionRepository;
            _userSubscriptionRepository = userSubscriptionRepository;
            _jwtService = jwtService;
            _passwordValidationService = passwordValidationService;
            _onlineStatusService = onlineStatusService;
            _encryptionService = encryptionService;
            _emailService = emailService;
            _configuration = configuration;
            _logger = logger;
        }

        [HttpPost("register")]
        public async Task<ActionResult<AuthResponseDto>> Register(RegisterDto registerDto)
        {
            try
            {
                // Check if email already exists
                var existingUser = await _userRepository.SingleOrDefaultAsync(u => u.Email == registerDto.Email);
                if (existingUser != null)
                {
                    return BadRequest("Email already in use");
                }

                // Check if user is at least 18 years old
                var minBirthDate = DateTime.Today.AddYears(-18);
                if (registerDto.BirthDate > minBirthDate)
                {
                    return BadRequest("You must be at least 18 years old to register");
                }

                // Validate password strength
                if (!_passwordValidationService.IsValid(registerDto.Password))
                {
                    return BadRequest(_passwordValidationService.GetValidationErrorMessage());
                }

                // Create user
                var user = new User
                {
                    Email = registerDto.Email,
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword(registerDto.Password),
                    CreatedAt = DateTime.UtcNow
                };

                // Create profile
                var profile = new Profile
                {
                    FirstName = registerDto.FirstName,
                    LastName = registerDto.LastName,
                    BirthDate = registerDto.BirthDate,
                    Gender = registerDto.Gender ?? 1, // Default to Male if not provided
                    Preference = registerDto.Preference ?? 2, // Default to Women if not provided
                    Location = registerDto.Location ?? "Unknown",
                    MinAgePreference = 18,
                    MaxAgePreference = 99,
                    MaxDistance = 50 // Default max distance
                };

                // Add user and profile to database
                user.Profile = profile;
                await _userRepository.AddAsync(user);
                await _userRepository.SaveChangesAsync();

                // Assign free subscription
                var freeSubscription = await _subscriptionRepository.SingleOrDefaultAsync(s => s.Name == "Free");
                if (freeSubscription != null)
                {
                    var userSubscription = new UserSubscription
                    {
                        UserId = user.Id,
                        SubscriptionId = freeSubscription.Id,
                        StartDate = DateTime.UtcNow,
                        ExpiresAt = DateTime.UtcNow.AddYears(100), // Free subscription doesn't expire
                        AmountPaid = 0
                    };

                    // Add the user subscription
                    await _userSubscriptionRepository.AddAsync(userSubscription);
                    await _userSubscriptionRepository.SaveChangesAsync();
                }

                // Generate JWT token
                var token = _jwtService.GenerateToken(user);

                // Return user info and token
                return Ok(new AuthResponseDto
                {
                    UserId = user.Id,
                    Email = user.Email,
                    FirstName = profile.FirstName,
                    LastName = profile.LastName,
                    Token = token,
                    TokenExpiration = DateTime.UtcNow.AddDays(7),
                    Subscription = new SubscriptionInfoDto
                    {
                        Id = freeSubscription.Id,
                        Name = freeSubscription.Name,
                        ChatsPerDay = freeSubscription.ChatsPerDay,
                        MaxInterests = freeSubscription.MaxInterests,
                        MaxPictures = freeSubscription.MaxPictures,
                        PictureRevealCondition = freeSubscription.PictureRevealCondition,
                        ExpiresAt = DateTime.UtcNow.AddYears(100)
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration");
                return StatusCode(500, "An error occurred during registration");
            }
        }

        [HttpPost("login")]
        public async Task<ActionResult<AuthResponseDto>> Login(LoginDto loginDto)
        {
            try
            {
                // Find user by email
                var user = await _userRepository.GetUserByEmailAsync(loginDto.Email);
                if (user != null)
                {
                    // Load profile and subscriptions
                    user = await _userRepository.GetUserWithProfileAsync(user.Id);
                    var userWithSubscriptions = await _userRepository.GetUserWithSubscriptionsAsync(user.Id);
                    user.Subscriptions = userWithSubscriptions.Subscriptions;
                }

                // Check if user exists
                if (user == null)
                {
                    return Unauthorized("Invalid email or password");
                }

                // Verify password
                if (!BCrypt.Net.BCrypt.Verify(loginDto.Password, user.PasswordHash))
                {
                    return Unauthorized("Invalid email or password");
                }

                // Update last login and set user as online
                user.LastLoginAt = DateTime.UtcNow;
                user.IsOnline = true;
                user.LastActiveAt = DateTime.UtcNow;
                _userRepository.Update(user);
                await _userRepository.SaveChangesAsync();

                // Update online status in the service
                await _onlineStatusService.SetUserOnlineAsync(user.Id);

                // Get current subscription
                var currentSubscription = user.CurrentSubscription;
                if (currentSubscription == null)
                {
                    // Assign free subscription if user doesn't have one
                    var freeSubscription = await _subscriptionRepository.SingleOrDefaultAsync(s => s.Name == "Free");
                    if (freeSubscription != null)
                    {
                        currentSubscription = new UserSubscription
                        {
                            UserId = user.Id,
                            SubscriptionId = freeSubscription.Id,
                            StartDate = DateTime.UtcNow,
                            ExpiresAt = DateTime.UtcNow.AddYears(100), // Free subscription doesn't expire
                            AmountPaid = 0
                        };

                        // Add the user subscription
                        await _userSubscriptionRepository.AddAsync(currentSubscription);
                        await _userSubscriptionRepository.SaveChangesAsync();

                        // Reload the user with subscriptions
                        user = await _userRepository.GetUserWithSubscriptionsAsync(user.Id);
                        currentSubscription = user.CurrentSubscription;
                    }
                    else
                    {
                        // Handle the case where free subscription is not found
                        _logger.LogError("Free subscription not found");
                        return StatusCode(500, "An error occurred during login");
                    }
                }

                // Generate JWT token
                var token = _jwtService.GenerateToken(user);

                // Generate new keys if the user doesn't have a public key
                string privateKey = string.Empty;
                if (string.IsNullOrEmpty(user.PublicKey))
                {
                    var (publicKey, newPrivateKey) = _encryptionService.GenerateKeyPair();
                    user.PublicKey = publicKey;
                    privateKey = newPrivateKey;

                    _userRepository.Update(user);
                    await _userRepository.SaveChangesAsync();
                }

                // Return user info, token, and encryption keys
                return Ok(new AuthResponseDto
                {
                    UserId = user.Id,
                    Email = user.Email,
                    FirstName = user.Profile.FirstName,
                    LastName = user.Profile.LastName,
                    Token = token,
                    TokenExpiration = DateTime.UtcNow.AddDays(7),
                    PublicKey = user.PublicKey ?? string.Empty,
                    PrivateKey = privateKey, // Only send private key if newly generated
                    Subscription = new SubscriptionInfoDto
                    {
                        Id = currentSubscription.Subscription.Id,
                        Name = currentSubscription.Subscription.Name,
                        ChatsPerDay = currentSubscription.Subscription.ChatsPerDay,
                        MaxInterests = currentSubscription.Subscription.MaxInterests,
                        MaxPictures = currentSubscription.Subscription.MaxPictures,
                        PictureRevealCondition = currentSubscription.Subscription.PictureRevealCondition,
                        ExpiresAt = currentSubscription.ExpiresAt
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login");
                return StatusCode(500, "An error occurred during login");
            }
        }

        [HttpPost("social-login")]
        public async Task<ActionResult<AuthResponseDto>> SocialLogin(SocialLoginDto socialLoginDto)
        {
            try
            {
                // Check if user with this email already exists
                var existingUser = await _userRepository.GetUserByEmailAsync(socialLoginDto.Email);

                if (existingUser == null)
                {
                    // Create a new user for this social login
                    existingUser = new User
                    {
                        Email = socialLoginDto.Email,
                        PasswordHash = socialLoginDto.Password,
                        CreatedAt = DateTime.UtcNow,
                        // Store provider info
                        ExternalLoginProvider = socialLoginDto.Provider,
                        ExternalLoginId = socialLoginDto.ProviderId
                    };

                    // Create profile
                    var profile = new Profile
                    {
                        FirstName = socialLoginDto.FirstName,
                        LastName = socialLoginDto.LastName,
                        // Set default values for required fields
                        BirthDate = DateTime.Today.AddYears(-20), // Default age, will be updated during profile setup
                        Gender = 1, // Default gender
                        Preference = 2, // Default preference
                        Location = "Unknown",
                        MinAgePreference = 18,
                        MaxAgePreference = 99
                    };

                    // Add user and profile to database
                    existingUser.Profile = profile;
                    await _userRepository.AddAsync(existingUser);
                    await _userRepository.SaveChangesAsync();

                    // Assign free subscription
                    var freeSubscription = await _subscriptionRepository.SingleOrDefaultAsync(s => s.Name == "Free");
                    if (freeSubscription != null)
                    {
                        var userSubscription = new UserSubscription
                        {
                            UserId = existingUser.Id,
                            SubscriptionId = freeSubscription.Id,
                            StartDate = DateTime.UtcNow,
                            ExpiresAt = DateTime.UtcNow.AddYears(100), // Free subscription doesn't expire
                            AmountPaid = 0
                        };

                        // Add the user subscription
                        await _userSubscriptionRepository.AddAsync(userSubscription);
                        await _userSubscriptionRepository.SaveChangesAsync();
                    }
                }
                else
                {
                    // Update existing user's social login info if needed
                    if (string.IsNullOrEmpty(existingUser.ExternalLoginProvider))
                    {
                        existingUser.ExternalLoginProvider = socialLoginDto.Provider;
                        existingUser.ExternalLoginId = socialLoginDto.ProviderId;
                        _userRepository.Update(existingUser);
                        await _userRepository.SaveChangesAsync();
                    }

                    // Load profile and subscriptions
                    existingUser = await _userRepository.GetUserWithProfileAsync(existingUser.Id);
                    var userWithSubscriptions = await _userRepository.GetUserWithSubscriptionsAsync(existingUser.Id);
                    existingUser.Subscriptions = userWithSubscriptions.Subscriptions;
                }

                // Update last login and set user as online
                existingUser.LastLoginAt = DateTime.UtcNow;
                existingUser.IsOnline = true;
                existingUser.LastActiveAt = DateTime.UtcNow;
                _userRepository.Update(existingUser);
                await _userRepository.SaveChangesAsync();

                // Update online status in the service
                await _onlineStatusService.SetUserOnlineAsync(existingUser.Id);

                // Get current subscription
                var currentSubscription = existingUser.CurrentSubscription;
                if (currentSubscription == null)
                {
                    // Assign free subscription if user doesn't have one
                    var freeSubscription = await _subscriptionRepository.SingleOrDefaultAsync(s => s.Name == "Free");
                    if (freeSubscription != null)
                    {
                        currentSubscription = new UserSubscription
                        {
                            UserId = existingUser.Id,
                            SubscriptionId = freeSubscription.Id,
                            StartDate = DateTime.UtcNow,
                            ExpiresAt = DateTime.UtcNow.AddYears(100), // Free subscription doesn't expire
                            AmountPaid = 0
                        };

                        // Add the user subscription
                        await _userSubscriptionRepository.AddAsync(currentSubscription);
                        await _userSubscriptionRepository.SaveChangesAsync();

                        // Reload the user with subscriptions
                        existingUser = await _userRepository.GetUserWithSubscriptionsAsync(existingUser.Id);
                        currentSubscription = existingUser.CurrentSubscription;
                    }
                    else
                    {
                        // Handle the case where free subscription is not found
                        _logger.LogError("Free subscription not found");
                        return StatusCode(500, "An error occurred during social login");
                    }
                }

                // Generate JWT token
                var token = _jwtService.GenerateToken(existingUser);

                // Return user info, token, and encryption keys
                return Ok(new AuthResponseDto
                {
                    UserId = existingUser.Id,
                    Email = existingUser.Email,
                    FirstName = existingUser.Profile.FirstName,
                    LastName = existingUser.Profile.LastName,
                    Token = token,
                    TokenExpiration = DateTime.UtcNow.AddDays(7),
                    PublicKey = existingUser.PublicKey ?? string.Empty,
                    Subscription = new SubscriptionInfoDto
                    {
                        Id = currentSubscription.Subscription.Id,
                        Name = currentSubscription.Subscription.Name,
                        ChatsPerDay = currentSubscription.Subscription.ChatsPerDay,
                        MaxInterests = currentSubscription.Subscription.MaxInterests,
                        MaxPictures = currentSubscription.Subscription.MaxPictures,
                        PictureRevealCondition = currentSubscription.Subscription.PictureRevealCondition,
                        ExpiresAt = currentSubscription.ExpiresAt
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during social login");
                return StatusCode(500, "An error occurred during social login");
            }
        }

        [HttpPost("forgot-password")]
        public async Task<ActionResult> ForgotPassword(ForgotPasswordDto model)
        {
            try
            {
                // Find user by email
                var user = await _userRepository.GetUserByEmailAsync(model.Email);
                if (user == null)
                {
                    // Don't reveal that the user doesn't exist
                    return Ok(new { message = "If your email is registered, you will receive a password reset link." });
                }

                // Generate a password reset token
                var token = GeneratePasswordResetToken();

                // Store the token in the database with expiration
                user.PasswordResetToken = token;
                user.PasswordResetTokenExpires = DateTime.UtcNow.AddHours(1); // Token valid for 1 hour

                _userRepository.Update(user);
                await _userRepository.SaveChangesAsync();

                // Send email with reset link
                var resetLink = $"{_configuration["AppUrl"]}/reset-password?token={token}";
                await _emailService.SendPasswordResetEmailAsync(user.Email, resetLink);

                return Ok(new { message = "If your email is registered, you will receive a password reset link." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during forgot password");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        [HttpPost("reset-password")]
        public async Task<ActionResult> ResetPassword(ResetPasswordDto model)
        {
            try
            {
                // Find user by reset token
                var user = await _userRepository.SingleOrDefaultAsync(u => u.PasswordResetToken == model.Token);
                if (user == null)
                {
                    return BadRequest("Invalid or expired token.");
                }

                // Check if token is expired
                if (user.PasswordResetTokenExpires < DateTime.UtcNow)
                {
                    return BadRequest("Token has expired.");
                }

                // Validate password strength
                if (!_passwordValidationService.IsValid(model.Password))
                {
                    return BadRequest(_passwordValidationService.GetValidationErrorMessage());
                }

                // Update password
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(model.Password);
                user.PasswordResetToken = null;
                user.PasswordResetTokenExpires = null;

                _userRepository.Update(user);
                await _userRepository.SaveChangesAsync();

                return Ok(new { message = "Password has been reset successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        // Helper method to generate a secure random token
        private string GeneratePasswordResetToken()
        {
            var randomBytes = new byte[32];
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                rng.GetBytes(randomBytes);
            }
            return Convert.ToBase64String(randomBytes);
        }
    }
}
