using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class SearchingUsersService : ISearchingUsersService
    {
        private readonly ICacheService _cacheService;
        private readonly ILogger<SearchingUsersService> _logger;
        
        // Cache keys
        private const string SearchingUsersKey = "SearchingUsers";
        private const string UserSearchingStatusKey = "User_Searching_{0}"; // UserId
        private const string UserSearchingStartTimeKey = "User_SearchingStartTime_{0}"; // UserId
        
        // Cache expiration times
        private readonly TimeSpan _searchingUsersCacheTime = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _userSearchingStatusCacheTime = TimeSpan.FromMinutes(5);
        
        public SearchingUsersService(
            ICacheService cacheService,
            ILogger<SearchingUsersService> logger)
        {
            _cacheService = cacheService;
            _logger = logger;
        }
        
        public async Task AddUserToSearchingAsync(int userId)
        {
            try
            {
                // Update cache
                var cacheKey = string.Format(UserSearchingStatusKey, userId);
                await _cacheService.SetAsync(cacheKey, true, _userSearchingStatusCacheTime);
                
                // Store the start time
                var startTimeKey = string.Format(UserSearchingStartTimeKey, userId);
                await _cacheService.SetAsync(startTimeKey, DateTime.UtcNow, _userSearchingStatusCacheTime);
                
                // Update searching users list in cache
                var searchingUsers = await _cacheService.GetAsync<List<int>>(SearchingUsersKey) ?? new List<int>();
                
                if (!searchingUsers.Contains(userId))
                {
                    searchingUsers.Add(userId);
                    await _cacheService.SetAsync(SearchingUsersKey, searchingUsers, _searchingUsersCacheTime);
                }
                
                _logger.LogInformation("User {UserId} is now searching", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding user {UserId} to searching users", userId);
                throw;
            }
        }
        
        public async Task RemoveUserFromSearchingAsync(int userId)
        {
            try
            {
                // Update cache
                var cacheKey = string.Format(UserSearchingStatusKey, userId);
                await _cacheService.SetAsync(cacheKey, false, _userSearchingStatusCacheTime);
                
                // Remove the start time
                var startTimeKey = string.Format(UserSearchingStartTimeKey, userId);
                await _cacheService.RemoveAsync(startTimeKey);
                
                // Update searching users list in cache
                var searchingUsers = await _cacheService.GetAsync<List<int>>(SearchingUsersKey);
                
                if (searchingUsers != null && searchingUsers.Contains(userId))
                {
                    searchingUsers.Remove(userId);
                    await _cacheService.SetAsync(SearchingUsersKey, searchingUsers, _searchingUsersCacheTime);
                }
                
                _logger.LogInformation("User {UserId} is no longer searching", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing user {UserId} from searching users", userId);
                throw;
            }
        }
        
        public async Task<List<int>> GetSearchingUserIdsAsync()
        {
            try
            {
                // Try to get from cache first
                var cachedSearchingUsers = await _cacheService.GetAsync<List<int>>(SearchingUsersKey);
                if (cachedSearchingUsers != null)
                {
                    return cachedSearchingUsers;
                }
                
                // If not in cache, return an empty list
                return new List<int>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting searching users");
                throw;
            }
        }
        
        public async Task<bool> IsUserSearchingAsync(int userId)
        {
            try
            {
                // Try to get from cache first
                var cacheKey = string.Format(UserSearchingStatusKey, userId);
                var cachedStatus = await _cacheService.GetAsync<bool?>(cacheKey);
                if (cachedStatus.HasValue)
                {
                    return cachedStatus.Value;
                }
                
                // If not in cache, assume not searching
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} is searching", userId);
                throw;
            }
        }
        
        public async Task<int> GetSearchingUsersCountAsync()
        {
            try
            {
                var searchingUsers = await GetSearchingUserIdsAsync();
                return searchingUsers.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting searching users count");
                throw;
            }
        }
        
        public async Task CleanupInactiveSearchingUsersAsync(TimeSpan searchingTimeout)
        {
            try
            {
                var searchingUsers = await GetSearchingUserIdsAsync();
                var usersToRemove = new List<int>();
                
                foreach (var userId in searchingUsers)
                {
                    var startTimeKey = string.Format(UserSearchingStartTimeKey, userId);
                    var startTime = await _cacheService.GetAsync<DateTime?>(startTimeKey);
                    
                    if (startTime.HasValue && DateTime.UtcNow - startTime.Value > searchingTimeout)
                    {
                        usersToRemove.Add(userId);
                        _logger.LogInformation("User {UserId} removed from searching due to timeout", userId);
                    }
                }
                
                // Remove users who have been searching for too long
                foreach (var userId in usersToRemove)
                {
                    await RemoveUserFromSearchingAsync(userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up inactive searching users");
                throw;
            }
        }
    }
}
