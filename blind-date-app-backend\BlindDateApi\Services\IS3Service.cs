using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace BlindDateApi.Services
{
    public interface IS3Service
    {
        /// <summary>
        /// Uploads a file to Amazon S3
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="userId">The user ID to associate with the file</param>
        /// <returns>The URL of the uploaded file</returns>
        Task<string> UploadFileAsync(IFormFile file, int userId);
        
        /// <summary>
        /// Deletes a file from Amazon S3
        /// </summary>
        /// <param name="fileUrl">The URL of the file to delete</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteFileAsync(string fileUrl);
        
        /// <summary>
        /// Generates a pre-signed URL for a file
        /// </summary>
        /// <param name="fileKey">The key of the file</param>
        /// <param name="expiryMinutes">How long the URL should be valid for (in minutes)</param>
        /// <returns>The pre-signed URL</returns>
        Task<string> GeneratePreSignedUrlAsync(string fileKey, int expiryMinutes = 60);
    }
}
