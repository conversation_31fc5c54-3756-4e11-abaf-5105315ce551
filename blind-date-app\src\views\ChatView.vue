<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useMatchesStore } from '../stores/matches'
import { useChatStore } from '../stores/chat'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const matchesStore = useMatchesStore()
const chatStore = useChatStore()

const matchId = computed(() => route.params.id)
const match = ref(null)
const chat = ref(null)

const message = ref('')
const showMatchConfirmation = ref(false)
const errorMessage = ref('')
const showTimesUpDialog = ref(false)
const timeRemaining = ref(120) // 2 minutes in seconds
const timerInterval = ref(null)
const showTimer = ref(false) // Explicit control for timer visibility

onMounted(async () => {
  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  try {
    // Fetch match data from store
    const matches = await matchesStore.fetchAllMatches()

    // Normalize match ID for comparison (ensure both are strings)
    const normalizedMatchId = String(matchId.value)

    const currentMatch = matches.find(m => String(m.id) === normalizedMatchId)

    if (!currentMatch) {
      errorMessage.value = 'Match not found'
      setTimeout(() => router.push('/matches'), 3000)
      return
    }

    match.value = currentMatch
    // Clear any error message since we found a match
    errorMessage.value = ''

    // Check if we came from the matching process or from the Matches page
    const cameFromMatching = route.query.source === 'matching'

    // Initialize chat differently based on whether this is a new match or existing match
    if (cameFromMatching) {
      // For new matches, create a conversation right away
      chat.value = { messages: [] }

      // Check if a conversation already exists for this match before creating a new one
      try {
        // First, fetch all conversations
        const conversations = await chatStore.fetchConversations()

        // Look for a conversation with the current match user
        const existingConversation = conversations.find(c =>
          c.participants.some(p => p.userId === currentMatch.otherUserId)
        )

        if (existingConversation) {
          await chatStore.setCurrentConversation(existingConversation.id)
        } else {
          await chatStore.createConversation(currentMatch.otherUserId)
        }
      } catch (error) {
        console.error('Error setting up conversation for new match:', error)
      }

      // Set up a watcher to keep our local chat in sync with the store
      watch(() => chatStore.currentMessages, (newMessages) => {
        if (!chat.value) {
          chat.value = { messages: [] }
        }

        // Map the messages from the store to our local format
        const mappedMessages = newMessages.map(msg => {
          return {
            id: msg.id,
            sender: msg.senderId === authStore.user.id ? 'user' : 'match',
            content: msg.decryptedContent || "[Encrypted message]", // Use the decrypted content
            timestamp: msg.sentAt,
            status: 'delivered' // Mark messages from the store as delivered
          }
        })

        // We don't need to preserve local messages anymore since they're all in the store
        // This was causing message duplication
        const localMessages = []

        // Combine store messages with any pending local messages
        const combinedMessages = [...mappedMessages, ...localMessages]
          .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))

        // Update the local chat messages
        chat.value.messages = combinedMessages
      }, { immediate: true, deep: true })

      // Show timer for new matches
      showTimer.value = true
      startTimer()
    } else {
      // For existing matches, we need to find the associated conversation
      try {
        // First, fetch all conversations
        const conversations = await chatStore.fetchConversations()

        // Look for a conversation with the match ID or with the current match user
        let existingConversation = conversations.find(c => c.matchId === currentMatch.id)

        // If not found by matchId, try to find by participant
        if (!existingConversation) {
          existingConversation = conversations.find(c =>
            c.participants.some(p => p.userId === currentMatch.otherUserId)
          )
        }

        if (existingConversation) {
          await chatStore.setCurrentConversation(existingConversation.id)

          // Get messages for this conversation
          await chatStore.fetchMessages(existingConversation.id)
        } else {
          // Create a new conversation with this match
          await chatStore.createConversation(currentMatch.otherUserId)

          // After creating the conversation, update its messages with the match ID
          if (chatStore.currentConversationId) {
            try {
              await chatStore.updateMessagesWithMatchId(chatStore.currentConversationId, currentMatch.id)
            } catch (updateError) {
              console.error('Failed to update messages with match ID:', updateError)
            }
          }
        }
      } catch (error) {
        console.error('Error setting up conversation for existing match:', error)
        errorMessage.value = 'Error loading conversation'
      }

      // Set up a watcher to keep our local chat in sync with the store
      watch(() => chatStore.currentMessages, (newMessages) => {
        if (!chat.value) {
          chat.value = { messages: [] }
        }

        // Map the messages from the store to our local format
        const mappedMessages = newMessages.map(msg => {
          return {
            id: msg.id,
            sender: msg.senderId === authStore.user.id ? 'user' : 'match',
            content: msg.decryptedContent || "[Encrypted message]", // Use the decrypted content
            timestamp: msg.sentAt,
            status: 'delivered' // Mark messages from the store as delivered
          }
        })

        // We don't need to preserve local messages anymore since they're all in the store
        // This was causing message duplication
        const localMessages = []

        // Combine store messages with any pending local messages
        const combinedMessages = [...mappedMessages, ...localMessages]
          .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))

        // Update the local chat messages
        chat.value.messages = combinedMessages
      }, { immediate: true, deep: true })

      // No timer for existing matches
      showTimer.value = false
      stopTimer()
    }
  } catch (error) {
    errorMessage.value = error.message || 'Failed to load chat'
    setTimeout(() => router.push('/matches'), 3000)
  }
})

// Clean up when component is destroyed
onUnmounted(() => {
  stopTimer()
  // Clear error message when component is unmounted
  errorMessage.value = ''
  // Clear current match when component is unmounted
  matchesStore.clearCurrentMatch()
  // Clear current conversation in chat store
  chatStore.setCurrentConversation(null)
})

// Computed property to filter out duplicate messages
const uniqueMessages = computed(() => {
  if (!chat.value || !chat.value.messages) return []

  // Use a Map to keep only the latest version of each message by ID
  const uniqueMap = new Map()

  // Process messages in reverse to ensure we get the latest version
  // (assuming newer messages are at the end of the array)
  for (let i = chat.value.messages.length - 1; i >= 0; i--) {
    const msg = chat.value.messages[i]
    if (!uniqueMap.has(msg.id)) {
      uniqueMap.set(msg.id, msg)
    }
  }

  // Convert back to array and sort by timestamp
  return Array.from(uniqueMap.values())
    .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
})

// Timer functionality (2 minutes)
const formattedTimeRemaining = computed(() => {
  const minutes = Math.floor(timeRemaining.value / 60)
  const seconds = timeRemaining.value % 60
  return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`
})

const startTimer = () => {
  // Clear any existing timer
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
  }

  // Set initial time (2 minutes = 120 seconds)
  timeRemaining.value = 120

  // Start the countdown
  timerInterval.value = setInterval(() => {
    if (timeRemaining.value > 0) {
      timeRemaining.value--
    } else {
      // Time's up
      clearInterval(timerInterval.value)
      showTimesUpDialog.value = true
    }
  }, 1000)
}

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
}

const calculateAge = (birthDate) => {
  if (!birthDate) return ''
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

const sendMessage = async () => {
  if (!message.value.trim()) return

  try {

    // Check if we have a current conversation ID
    if (!chatStore.currentConversationId) {
      console.error('No current conversation ID available, cannot send message')
      errorMessage.value = 'Cannot send message: No active conversation'
      return
    }

    // Store the message content before clearing it
    const messageContent = message.value

    // Send message using the chat store - encryption is handled internally
    await chatStore.sendMessage(chatStore.currentConversationId, messageContent)

    // Clear the input field
    message.value = ''

    // The message will be added to the chat through the watcher
    // We don't need to manually add it to the local chat anymore

    // Otherwise the watcher will update our local chat reference
  } catch (error) {
    errorMessage.value = error.message || 'Failed to send message'
  }
}

const likeMatch = async () => {
  try {
    // Stop the timer
    stopTimer()

    const isMatched = await matchesStore.likeMatch(matchId.value)

    if (isMatched) {
      // Show match confirmation
      showTimesUpDialog.value = false
      showMatchConfirmation.value = true

      // Update messages with the new match ID if we have a current conversation
      if (chatStore.currentConversationId) {
        try {
          await chatStore.updateMessagesWithMatchId(chatStore.currentConversationId, matchId.value)
        } catch (updateError) {
          console.error('Failed to update messages with match ID:', updateError)
          // Don't show error to user, just log it
        }
      }
    } else {
      // No match, go back to home
      // Clear current match before navigating
      matchesStore.clearCurrentMatch()
      router.push('/home')
    }
  } catch (error) {
    errorMessage.value = error.message || 'Failed to like match'
  }
}

const dislikeMatch = async () => {
  try {
    // Stop the timer
    stopTimer()

    await matchesStore.dislikeMatch(matchId.value)
    // Clear current match before navigating
    matchesStore.clearCurrentMatch()
    router.push('/home')
  } catch (error) {
    errorMessage.value = error.message || 'Failed to dislike match'
  }
}

const continueMatching = () => {
  // Clear current match before navigating
  matchesStore.clearCurrentMatch()
  router.push('/home')
}

const navigateToHome = () => {
  // Clear current match before navigating
  matchesStore.clearCurrentMatch()
  router.push('/home')
}

const navigateToMatches = () => {
  router.push('/matches')
}


</script>

<template>
  <v-layout>
    <!-- App Bar -->
    <v-app-bar color="white" elevation="1">
      <v-btn icon @click="navigateToHome">
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>

      <!-- Desktop Navigation -->
      <v-btn variant="text" @click="navigateToMatches" class="d-none d-sm-flex">
        <v-icon start>mdi-account-multiple</v-icon>
        Matches
      </v-btn>

      <v-app-bar-title v-if="match" class="text-truncate">
        {{ match.firstName }}
        {{ chat && chat.messages && chat.messages.length > 0 ? ' ' + match.lastName : '' }}
        <span v-if="match.revealAge && calculateAge(match.birthDate)">({{ calculateAge(match.birthDate) }})</span>
      </v-app-bar-title>

      <v-spacer></v-spacer>

      <!-- Timer (only for new matches from Start Searching) -->
      <v-chip
        v-if="route.query.source === 'matching'"
        color="primary"
        class="timer-chip"
      >
        <v-icon start>mdi-clock-outline</v-icon>
        {{ formattedTimeRemaining }}
      </v-chip>
    </v-app-bar>

    <!-- Main Content -->
    <v-main>
      <v-container>
        <v-row justify="center">
          <v-col cols="12" sm="10" md="8" lg="6">
            <!-- Match Profile Card -->
            <v-card v-if="match" class="mb-4" variant="outlined">
              <v-card-item>
                <template v-slot:prepend>
                  <div v-if="match.revealPicture && match.pictures && match.pictures.length > 0">
                    <v-avatar size="large">
                      <v-img :src="match.pictures[0]" alt="Profile picture"></v-img>
                    </v-avatar>
                  </div>
                  <v-avatar v-else size="large" color="grey-lighten-2">
                    <v-icon size="large" color="grey">mdi-help</v-icon>
                  </v-avatar>
                </template>

                <v-card-title>
                  {{ match.firstName }} {{ match.showLastName ? match.lastName : '' }}
                  <span v-if="match.revealAge && calculateAge(match.birthDate)">({{ calculateAge(match.birthDate) }})</span>

                  <!-- Online status indicator -->
                  <v-icon
                    :color="match.isOnline ? 'success' : 'grey'"
                    size="small"
                    class="ml-2"
                  >
                    mdi-circle
                  </v-icon>
                  <span class="text-caption ml-1">{{ match.isOnline ? 'Online' : 'Offline' }}</span>
                </v-card-title>

                <v-card-subtitle>
                  <div>{{ match.bio }}</div>

                  <!-- Location and distance information -->
                  <div v-if="match.location || match.distance" class="mt-2 d-flex align-center">
                    <v-icon size="small" color="primary" class="mr-1">mdi-map-marker</v-icon>
                    <span>{{ match.location }}</span>

                    <v-chip
                      v-if="match.distance"
                      size="x-small"
                      color="primary"
                      variant="outlined"
                      class="ml-2"
                    >
                      {{ match.distance }} km
                    </v-chip>
                  </div>
                </v-card-subtitle>
              </v-card-item>

              <v-card-text v-if="match.interests && match.interests.length > 0">
                <v-chip
                  v-for="interest in match.interests"
                  :key="interest"
                  class="mr-2 mb-2"
                  color="primary"
                  variant="outlined"
                  size="small"
                >
                  {{ interest }}
                </v-chip>
              </v-card-text>
            </v-card>

            <!-- Chat Card -->
            <v-card class="chat-card" height="400">
              <v-card-text class="chat-messages" style="height: 300px; overflow-y: auto;">
                <v-alert
                  v-if="errorMessage"
                  type="error"
                  variant="tonal"
                  class="mb-4"
                >
                  {{ errorMessage }}
                </v-alert>

                <!-- Connection state notification removed as requested -->

                <div v-if="chat && chat.messages.length > 0" class="d-flex flex-column gap-4">
                  <!-- Use a computed property to get unique messages by ID -->
                  <div
                    v-for="(msg, index) in uniqueMessages"
                    :key="msg.id"
                    :class="[
                      'd-flex',
                      msg.sender === 'user' ? 'justify-end' : 'justify-start',
                      // Add extra margin if sender changes
                      index > 0 && msg.sender !== uniqueMessages[index-1].sender ? 'mt-4' : ''
                    ]"
                  >
                    <v-card
                      :color="msg.sender === 'user' ? 'primary' : 'grey-lighten-3'"
                      :class="['rounded-lg', 'message-bubble', msg.sender === 'user' ? 'text-white' : '']"
                      max-width="70%"
                      elevation="1"
                    >
                      <div>{{ msg.content }}</div>
                      <div class="message-time" :class="msg.sender === 'user' ? 'text-white text-opacity-70' : 'text-grey'">
                        {{ new Date(msg.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) }}
                      </div>
                    </v-card>
                  </div>
                </div>

                <div v-else class="d-flex align-center justify-center h-100 text-grey">
                  Start chatting to get to know each other!
                </div>
              </v-card-text>

              <v-divider></v-divider>

              <v-card-actions>
                <v-form @submit.prevent="sendMessage" class="d-flex w-100 gap-2">
                  <v-text-field
                    v-model="message"
                    placeholder="Type a message..."
                    variant="outlined"
                    density="compact"
                    hide-details
                    :disabled="showMatchConfirmation"
                    @keyup.enter="sendMessage"
                  ></v-text-field>

                  <v-btn
                    color="primary"
                    type="submit"
                    :disabled="!message.trim() || showMatchConfirmation"
                  >
                    <v-icon>mdi-send</v-icon>
                  </v-btn>
                </v-form>
              </v-card-actions>
            </v-card>

            <!-- Like/Dislike buttons have been removed -->
          </v-col>
        </v-row>
      </v-container>
    </v-main>

    <!-- Match Confirmation Dialog -->
    <v-dialog v-model="showMatchConfirmation" max-width="400">
      <v-card>
        <v-card-title class="text-h5 text-center pt-6">
          <v-icon color="success" size="x-large" class="mb-2">mdi-heart</v-icon>
          <div>It's a Match!</div>
        </v-card-title>

        <v-card-text class="text-center">
          You and {{ match?.firstName }} {{ match?.showLastName ? match?.lastName : '' }} liked each other
        </v-card-text>

        <v-card-actions class="justify-center pb-6">
          <v-btn
            color="primary"
            variant="elevated"
            @click="continueMatching"
          >
            Continue Matching
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Time's Up Dialog -->
    <v-dialog v-model="showTimesUpDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h5 text-center pt-6">
          <v-icon color="primary" size="x-large" class="mb-2">mdi-clock-end</v-icon>
          <div>Time's Up!</div>
        </v-card-title>

        <v-card-text class="text-center">
          Did you enjoy chatting with {{ match?.firstName }} {{ match?.showLastName ? match?.lastName : '' }}?
        </v-card-text>

        <v-card-actions class="justify-center pb-6">
          <div class="d-flex equal-width-buttons">
            <v-btn
              color="success"
              variant="elevated"
              block
              min-width="120"
              @click="likeMatch"
            >
              <v-icon start>mdi-thumb-up</v-icon>
              LIKE
            </v-btn>

            <v-btn
              color="error"
              variant="elevated"
              block
              min-width="120"
              @click="dislikeMatch"
            >
              <v-icon start>mdi-thumb-down</v-icon>
              DISLIKE
            </v-btn>
          </div>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<style scoped>
.message-bubble {
  max-width: 70%;
  word-break: break-word;
  padding: 10px 12px !important; /* Increase padding inside message bubbles */
  margin: 4px 0; /* Add vertical spacing between messages */
}

.message-time {
  font-size: 0.7rem;
  text-align: right;
  margin-top: 4px;
  opacity: 0.8;
}

.equal-width-buttons {
  width: 100%;
  max-width: 300px;
  gap: 16px; /* Add space between buttons */
}

.equal-width-buttons .v-btn {
  flex: 1;
  justify-content: center;
  max-width: calc(50% - 8px); /* Ensure buttons don't touch */
}

.timer-chip {
  font-weight: bold;
  font-size: 1rem;
  margin-right: 16px;
}

</style>
