using System;

namespace BlindDateApi.DTOs
{
    public class SubscriptionDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public int ChatsPerDay { get; set; }
        public int MaxInterests { get; set; }
        public int MaxPictures { get; set; }
        public string PictureRevealCondition { get; set; } = string.Empty;
        public bool IsCurrent { get; set; }
    }

    public class SubscriptionPurchaseDto
    {
        public int SubscriptionId { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string PaymentToken { get; set; } = string.Empty;
    }

    public class PaymentConfirmationDto
    {
        public string PaymentIntentId { get; set; } = string.Empty;
    }

    public class UserSubscriptionDto
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public int SubscriptionId { get; set; }
        public string SubscriptionName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime ExpiresAt { get; set; }
        public bool IsActive { get; set; }
        public decimal AmountPaid { get; set; }
    }
}
