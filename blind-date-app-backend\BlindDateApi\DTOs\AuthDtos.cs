using System.ComponentModel.DataAnnotations;
using BlindDateApi.Attributes;

namespace BlindDateApi.DTOs
{
    public class RegisterDto
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StrongPassword]
        public string Password { get; set; } = string.Empty;

        [Required]
        [MinLength(2)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MinLength(2)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        public DateTime BirthDate { get; set; }

        public int? Gender { get; set; }

        public int? Preference { get; set; }

        public string? Location { get; set; }
    }

    public class LoginDto
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string Password { get; set; } = string.Empty;
    }

    public class AuthResponseDto
    {
        public int UserId { get; set; }
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public DateTime TokenExpiration { get; set; }
        public SubscriptionInfoDto Subscription { get; set; } = new SubscriptionInfoDto();
        public string PrivateKey { get; set; } = string.Empty;
        public string PublicKey { get; set; } = string.Empty;
    }

    public class SubscriptionInfoDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int ChatsPerDay { get; set; }
        public int MaxInterests { get; set; }
        public int MaxPictures { get; set; }
        public string PictureRevealCondition { get; set; } = string.Empty;
        public DateTime ExpiresAt { get; set; }
    }
}
