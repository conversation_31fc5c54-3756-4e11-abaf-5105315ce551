using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BlindDateApi.Data.Models
{
    public class Interest
    {
        public Interest()
        {
            ProfileInterests = new HashSet<ProfileInterest>();
        }

        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(255)]
        public string? Description { get; set; }

        // Navigation property
        public virtual ICollection<ProfileInterest> ProfileInterests { get; set; }
    }
}
