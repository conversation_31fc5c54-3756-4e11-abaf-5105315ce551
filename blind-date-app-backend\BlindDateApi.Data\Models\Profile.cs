using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace BlindDateApi.Data.Models
{
    public class Profile
    {
        public Profile()
        {
            ProfileInterests = new HashSet<ProfileInterest>();
        }
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        public DateTime BirthDate { get; set; }

        [MaxLength(500)]
        public string Bio { get; set; } = string.Empty;

        [MaxLength(100)]
        public string Location { get; set; } = string.Empty;

        // Geolocation coordinates
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }

        // Accuracy of the location in meters
        public float? LocationAccuracy { get; set; }

        // When the location was last updated
        public DateTime? LocationUpdatedAt { get; set; }

        // Maximum distance for matching (in kilometers)
        public int MaxDistance { get; set; } = 50;

        // Navigation property for interests
        public virtual ICollection<ProfileInterest> ProfileInterests { get; set; }

        // Stored as comma-separated URLs
        [MaxLength(1000)]
        public string Pictures { get; set; } = string.Empty;

        // Gender: 1 = Male, 2 = Female, 3 = Other
        public int Gender { get; set; }

        // Preference: 1 = Men, 2 = Women, 3 = Both
        public int Preference { get; set; }

        // Age range preferences
        public int MinAgePreference { get; set; } = 18;
        public int MaxAgePreference { get; set; } = 99;

        // Navigation property
        public virtual User? User { get; set; }

        // Helper methods
        [NotMapped]
        public List<string> InterestsList
        {
            get => ProfileInterests?.Select(pi => pi.Interest?.Name ?? string.Empty)
                .Where(name => !string.IsNullOrEmpty(name))
                .ToList() ?? new List<string>();
            set
            {
                // This is a helper for backward compatibility
                // It should be used carefully as it will replace all existing interests
            }
        }

        [NotMapped]
        public List<string> PicturesList
        {
            get => string.IsNullOrEmpty(Pictures)
                ? new List<string>()
                : Pictures.Split(',').ToList();
            set => Pictures = string.Join(",", value);
        }

        [NotMapped]
        public int Age => CalculateAge(BirthDate);

        private int CalculateAge(DateTime birthDate)
        {
            var today = DateTime.Today;
            var age = today.Year - birthDate.Year;
            if (birthDate.Date > today.AddYears(-age)) age--;
            return age;
        }
    }
}
