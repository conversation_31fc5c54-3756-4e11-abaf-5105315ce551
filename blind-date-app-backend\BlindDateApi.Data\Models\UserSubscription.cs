using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlindDateApi.Data.Models
{
    public class UserSubscription
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int SubscriptionId { get; set; }

        [Required]
        public DateTime StartDate { get; set; } = DateTime.UtcNow;

        [Required]
        public DateTime ExpiresAt { get; set; }

        [NotMapped]
        public bool IsActive => DateTime.UtcNow < ExpiresAt;

        // Payment information
        public string? TransactionId { get; set; }

        public decimal AmountPaid { get; set; }

        // Navigation properties
        public virtual User? User { get; set; }
        public virtual Subscription? Subscription { get; set; }
    }
}
