using BlindDateApi.Services;
using Xunit;

namespace BlindDateApi.Tests.Services
{
    public class PasswordValidationServiceTests
    {
        private readonly IPasswordValidationService _passwordValidationService;

        public PasswordValidationServiceTests()
        {
            _passwordValidationService = new PasswordValidationService();
        }

        [Fact]
        public void IsValid_ValidPassword_ReturnsTrue()
        {
            // Arrange
            var validPassword = "Password123!";

            // Act
            var result = _passwordValidationService.IsValid(validPassword);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsValid_TooShortPassword_ReturnsFalse()
        {
            // Arrange
            var shortPassword = "Pw1!";

            // Act
            var result = _passwordValidationService.IsValid(shortPassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsValid_NoLowercasePassword_ReturnsFalse()
        {
            // Arrange
            var noLowercasePassword = "PASSWORD123!";

            // Act
            var result = _passwordValidationService.IsValid(noLowercasePassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsValid_NoUppercasePassword_ReturnsFalse()
        {
            // Arrange
            var noUppercasePassword = "password123!";

            // Act
            var result = _passwordValidationService.IsValid(noUppercasePassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsValid_NoDigitPassword_ReturnsFalse()
        {
            // Arrange
            var noDigitPassword = "Password!";

            // Act
            var result = _passwordValidationService.IsValid(noDigitPassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsValid_NoSpecialCharPassword_ReturnsFalse()
        {
            // Arrange
            var noSpecialCharPassword = "Password123";

            // Act
            var result = _passwordValidationService.IsValid(noSpecialCharPassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsValid_NullPassword_ReturnsFalse()
        {
            // Arrange
            string? nullPassword = null;

            // Act
            var result = _passwordValidationService.IsValid(nullPassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsValid_EmptyPassword_ReturnsFalse()
        {
            // Arrange
            var emptyPassword = string.Empty;

            // Act
            var result = _passwordValidationService.IsValid(emptyPassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void GetValidationErrorMessage_ReturnsExpectedMessage()
        {
            // Act
            var message = _passwordValidationService.GetValidationErrorMessage();

            // Assert
            Assert.Contains("at least 8 characters", message);
            Assert.Contains("lowercase letter", message);
            Assert.Contains("uppercase letter", message);
            Assert.Contains("digit", message);
            Assert.Contains("special character", message);
        }
    }
}
