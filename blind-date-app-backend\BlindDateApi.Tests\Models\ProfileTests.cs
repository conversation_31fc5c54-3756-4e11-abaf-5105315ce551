using System;
using System.Collections.Generic;
using System.Linq;
using BlindDateApi.Data.Models;
using Xunit;

namespace BlindDateApi.Tests.Models
{
    public class ProfileTests
    {
        [Fact]
        public void InterestsList_EmptyInterests_ReturnsEmptyList()
        {
            // Arrange
            var profile = new Profile();
            // No interests added

            // Act
            var interests = profile.InterestsList;

            // Assert
            Assert.Empty(interests);
        }

        [Fact]
        public void InterestsList_WithInterests_ReturnsList()
        {
            // Arrange
            var profile = new Profile();
            var interests = new List<Interest>
            {
                new Interest { Id = 1, Name = "Music" },
                new Interest { Id = 2, Name = "Movies" },
                new Interest { Id = 3, Name = "Sports" }
            };

            profile.ProfileInterests = interests.Select(i => new ProfileInterest
            {
                ProfileId = 1,
                InterestId = i.Id,
                Interest = i
            }).ToList();

            // Act
            var interestsList = profile.InterestsList;

            // Assert
            Assert.Equal(3, interestsList.Count);
            Assert.Contains("Music", interestsList);
            Assert.Contains("Movies", interestsList);
            Assert.Contains("Sports", interestsList);
        }

        [Fact]
        public void PicturesList_EmptyPictures_ReturnsEmptyList()
        {
            // Arrange
            var profile = new Profile
            {
                Pictures = ""
            };

            // Act
            var pictures = profile.PicturesList;

            // Assert
            Assert.Empty(pictures);
        }

        [Fact]
        public void PicturesList_WithPictures_ReturnsList()
        {
            // Arrange
            var profile = new Profile
            {
                Pictures = "pic1.jpg,pic2.jpg,pic3.jpg"
            };

            // Act
            var pictures = profile.PicturesList;

            // Assert
            Assert.Equal(3, pictures.Count);
            Assert.Contains("pic1.jpg", pictures);
            Assert.Contains("pic2.jpg", pictures);
            Assert.Contains("pic3.jpg", pictures);
        }

        [Fact]
        public void PicturesList_SetList_UpdatesPicturesString()
        {
            // Arrange
            var profile = new Profile();
            var picturesList = new List<string> { "pic1.jpg", "pic2.jpg", "pic3.jpg" };

            // Act
            profile.PicturesList = picturesList;

            // Assert
            Assert.Equal("pic1.jpg,pic2.jpg,pic3.jpg", profile.Pictures);
        }

        [Fact]
        public void Age_CalculatesCorrectly()
        {
            // Arrange
            var today = DateTime.Today;
            var birthDate = today.AddYears(-30);
            var profile = new Profile
            {
                BirthDate = birthDate
            };

            // Act
            var age = profile.Age;

            // Assert
            Assert.Equal(30, age);
        }

        [Fact]
        public void Age_BirthdayTomorrow_CalculatesCorrectly()
        {
            // Arrange
            var today = DateTime.Today;
            var birthDate = today.AddYears(-30).AddDays(1); // Birthday is tomorrow
            var profile = new Profile
            {
                BirthDate = birthDate
            };

            // Act
            var age = profile.Age;

            // Assert
            Assert.Equal(29, age); // Should be 29 until birthday
        }

        [Fact]
        public void Age_BirthdayToday_CalculatesCorrectly()
        {
            // Arrange
            var today = DateTime.Today;
            var birthDate = today.AddYears(-30); // Birthday is today
            var profile = new Profile
            {
                BirthDate = birthDate
            };

            // Act
            var age = profile.Age;

            // Assert
            Assert.Equal(30, age);
        }
    }
}
