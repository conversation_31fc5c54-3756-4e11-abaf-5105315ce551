using System;
using System.Collections.Generic;

namespace BlindDateApi.DTOs
{
    public class MatchDto
    {
        public int Id { get; set; }
        public int OtherUserId { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Bio { get; set; } = string.Empty;
        public int? Age { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsMatched { get; set; }
        public bool HasResponded { get; set; }
        public List<string>? Pictures { get; set; }
        public DateTime? LastMessageAt { get; set; }
        public string? LastMessagePreview { get; set; }
        public int UnreadCount { get; set; }
        public int? Distance { get; set; }
        public string? Location { get; set; }
        public bool IsOnline { get; set; }
    }

    public class MatchResponseDto
    {
        public int MatchId { get; set; }
        public bool Like { get; set; }
    }

    public class MatchSearchDto
    {
        public int? MinAge { get; set; }
        public int? MaxAge { get; set; }
        public int? Gender { get; set; }
        public List<string>? Interests { get; set; }
        public string? Location { get; set; }
    }
}
