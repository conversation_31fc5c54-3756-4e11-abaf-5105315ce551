using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlindDateApi.Tests.Services
{
    public class ChatServiceTests
    {
        private readonly Mock<IRepository<Conversation>> _mockConversationRepository;
        private readonly Mock<IRepository<ConversationParticipant>> _mockParticipantRepository;
        private readonly Mock<IRepository<Message>> _mockMessageRepository;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IRepository<Profile>> _mockProfileRepository;
        private readonly Mock<ICacheService> _mockCacheService;
        private readonly Mock<IChatNotificationService> _mockChatNotificationService;
        private readonly Mock<ILogger<ChatService>> _mockLogger;
        private readonly ChatService _chatService;

        public ChatServiceTests()
        {
            _mockConversationRepository = new Mock<IRepository<Conversation>>();
            _mockParticipantRepository = new Mock<IRepository<ConversationParticipant>>();
            _mockMessageRepository = new Mock<IRepository<Message>>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockProfileRepository = new Mock<IRepository<Profile>>();
            _mockCacheService = new Mock<ICacheService>();
            _mockChatNotificationService = new Mock<IChatNotificationService>();
            _mockLogger = new Mock<ILogger<ChatService>>();

            _chatService = new ChatService(
                _mockConversationRepository.Object,
                _mockParticipantRepository.Object,
                _mockMessageRepository.Object,
                _mockUserRepository.Object,
                _mockProfileRepository.Object,
                _mockCacheService.Object,
                _mockChatNotificationService.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task CreateConversationAsync_ShouldCreateConversationAndAddParticipants()
        {
            // Arrange
            var userId = 1;
            var dto = new CreateConversationDto
            {
                ParticipantIds = new List<int> { 2 }, // User 1 will be added automatically
                PublicKey = "test-public-key",
                EncryptedKeys = new Dictionary<int, string>
                {
                    { 1, "encrypted-key-1" },
                    { 2, "encrypted-key-2" }
                }
            };

            var conversation = new Conversation
            {
                Id = 1,
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(2),
                PublicKey = dto.PublicKey
            };

            var subscription1 = new Subscription { Id = 1, Name = "Free", ChatsPerDay = 10 };
            var userSubscription1 = new UserSubscription
            {
                SubscriptionId = 1,
                Subscription = subscription1,
                ExpiresAt = DateTime.UtcNow.AddDays(30)
            };

            var user1 = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                Profile = new Profile { FirstName = "User", LastName = "One" },
                Subscriptions = new List<UserSubscription> { userSubscription1 }
            };

            var userSubscription2 = new UserSubscription
            {
                SubscriptionId = 1,
                Subscription = subscription1,
                ExpiresAt = DateTime.UtcNow.AddDays(30)
            };

            var user2 = new User
            {
                Id = 2,
                Email = "<EMAIL>",
                Profile = new Profile { FirstName = "User", LastName = "Two" },
                Subscriptions = new List<UserSubscription> { userSubscription2 }
            };

            var participant1 = new ConversationParticipant
            {
                Id = 1,
                ConversationId = 1,
                UserId = 1,
                User = user1,
                EncryptedConversationKey = "encrypted-key-1"
            };

            var participant2 = new ConversationParticipant
            {
                Id = 2,
                ConversationId = 1,
                UserId = 2,
                User = user2,
                EncryptedConversationKey = "encrypted-key-2"
            };

            // Setup mocks
            _mockConversationRepository.Setup(r => r.AddAsync(It.IsAny<Conversation>()))
                .Callback<Conversation>(c => conversation = c)
                .Returns(Task.CompletedTask);

            _mockConversationRepository.Setup(r => r.SaveChangesAsync())
                .ReturnsAsync(1);

            _mockParticipantRepository.Setup(r => r.AddAsync(It.IsAny<ConversationParticipant>()))
                .Returns(Task.CompletedTask);

            _mockParticipantRepository.Setup(r => r.SaveChangesAsync())
                .ReturnsAsync(1);

            _mockParticipantRepository.Setup(r => r.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<ConversationParticipant, bool>>>(),
                    It.IsAny<Func<IQueryable<ConversationParticipant>, IIncludableQueryable<ConversationParticipant, object>>>()))
                .ReturnsAsync(new ConversationParticipant
                {
                    ConversationId = 1,
                    UserId = 1,
                    Conversation = conversation,
                    User = user1
                });

            _mockUserRepository.Setup(r => r.GetUserWithSubscriptionsAsync(userId))
                .ReturnsAsync(user1);

            _mockCacheService.Setup(c => c.GetAsync<int?>(It.IsAny<string>()))
                .ReturnsAsync(10);

            // Act
            var result = await _chatService.CreateConversationAsync(userId, dto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Id);
            Assert.Equal(dto.PublicKey, result.PublicKey);

            _mockConversationRepository.Verify(r => r.AddAsync(It.IsAny<Conversation>()), Times.Once);
            _mockConversationRepository.Verify(r => r.SaveChangesAsync(), Times.Once);
            _mockParticipantRepository.Verify(r => r.AddAsync(It.IsAny<ConversationParticipant>()), Times.AtLeast(2));
            _mockParticipantRepository.Verify(r => r.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task SendMessageAsync_ShouldSendMessageToConversation()
        {
            // Arrange
            var userId = 1;
            var dto = new SendChatMessageDto
            {
                ConversationId = 1,
                EncryptedContent = "encrypted-content",
                IV = "initialization-vector",
                Type = MessageType.Text
            };

            var conversation = new Conversation
            {
                Id = 1,
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(5),
                PublicKey = "test-public-key"
            };

            var participant = new ConversationParticipant
            {
                ConversationId = 1,
                UserId = 1,
                Conversation = conversation,
                MessageCount = 0
            };

            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                Profile = new Profile { FirstName = "User", LastName = "One" }
            };

            // Setup mocks
            _mockParticipantRepository.Setup(r => r.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<ConversationParticipant, bool>>>(),
                    It.IsAny<Func<IQueryable<ConversationParticipant>, IIncludableQueryable<ConversationParticipant, object>>>()))
                .ReturnsAsync(participant);

            _mockMessageRepository.Setup(r => r.AddAsync(It.IsAny<Message>()))
                .Callback<Message>(m => m.Id = 1)
                .Returns(Task.CompletedTask);

            _mockMessageRepository.Setup(r => r.SaveChangesAsync())
                .ReturnsAsync(1);

            _mockParticipantRepository.Setup(r => r.SaveChangesAsync())
                .ReturnsAsync(1);

            _mockUserRepository.Setup(r => r.GetUserWithProfileAsync(userId))
                .ReturnsAsync(user);

            // Act
            var result = await _chatService.SendMessageAsync(userId, dto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Id);
            Assert.Equal(dto.ConversationId, result.ConversationId);
            Assert.Equal(userId, result.SenderId);
            Assert.Equal(dto.EncryptedContent, result.EncryptedContent);
            Assert.Equal(dto.IV, result.IV);
            Assert.Equal(dto.Type, result.Type);

            _mockMessageRepository.Verify(r => r.AddAsync(It.IsAny<Message>()), Times.Once);
            _mockMessageRepository.Verify(r => r.SaveChangesAsync(), Times.Once);
            _mockParticipantRepository.Verify(r => r.Update(It.IsAny<ConversationParticipant>()), Times.Once);
            _mockParticipantRepository.Verify(r => r.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task GetRemainingChatsAsync_ShouldReturnRemainingChats()
        {
            // Arrange
            var userId = 1;
            var subscription = new Subscription { Id = 1, Name = "Free", ChatsPerDay = 10 };
            var userSubscription = new UserSubscription
            {
                SubscriptionId = 1,
                Subscription = subscription,
                ExpiresAt = DateTime.UtcNow.AddDays(30)
            };

            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                Subscriptions = new List<UserSubscription> { userSubscription }
            };

            // Setup mocks
            _mockUserRepository.Setup(r => r.GetUserWithSubscriptionsAsync(userId))
                .ReturnsAsync(user);

            _mockCacheService.Setup(c => c.GetAsync<int?>(It.IsAny<string>()))
                .ReturnsAsync(7); // 7 remaining chats

            // Act
            var result = await _chatService.GetRemainingChatsAsync(userId);

            // Assert
            Assert.Equal(7, result);
        }
    }
}
