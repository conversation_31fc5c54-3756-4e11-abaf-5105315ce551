using System;
using System.Security.Claims;
using System.Threading.Tasks;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class OnlineStatusController : ControllerBase
    {
        private readonly IOnlineStatusService _onlineStatusService;
        private readonly ILogger<OnlineStatusController> _logger;

        public OnlineStatusController(
            IOnlineStatusService onlineStatusService,
            ILogger<OnlineStatusController> logger)
        {
            _onlineStatusService = onlineStatusService;
            _logger = logger;
        }

        [HttpPost("ping")]
        public async Task<IActionResult> Ping()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                await _onlineStatusService.UpdateUserActivityAsync(userIdInt);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user activity");
                return StatusCode(500, "An error occurred while updating user activity");
            }
        }

        [HttpPost("offline")]
        public async Task<IActionResult> SetOffline()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                await _onlineStatusService.SetUserOfflineAsync(userIdInt);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting user offline");
                return StatusCode(500, "An error occurred while setting user offline");
            }
        }

        [HttpGet("status/{id}")]
        public async Task<IActionResult> GetUserStatus(int id)
        {
            try
            {
                var isOnline = await _onlineStatusService.IsUserOnlineAsync(id);
                return Ok(new { isOnline });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user status");
                return StatusCode(500, "An error occurred while getting user status");
            }
        }
    }
}
