using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.Xml;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class MatchService : IMatchService
    {
        private readonly IRepository<Match> _matchRepository;
        private readonly IRepository<Profile> _profileRepository;
        private readonly IUserRepository _userRepository;
        private readonly ICacheService _cacheService;
        private readonly IOnlineStatusService _onlineStatusService;
        private readonly IGeoLocationService _geoLocationService;
        private readonly ISearchingUsersService _searchingUsersService;
        private readonly ILogger<MatchService> _logger;

        // Lock for synchronizing match creation
        private static readonly SemaphoreSlim _matchingLock = new SemaphoreSlim(1, 1);

        private const string RemainingMatchesKey = "RemainingMatches_{0}_{1}"; // UserId_Date

        public MatchService(
            IRepository<Match> matchRepository,
            IRepository<Profile> profileRepository,
            IUserRepository userRepository,
            ICacheService cacheService,
            IOnlineStatusService onlineStatusService,
            IGeoLocationService geoLocationService,
            ISearchingUsersService searchingUsersService,
            ILogger<MatchService> logger)
        {
            _matchRepository = matchRepository;
            _profileRepository = profileRepository;
            _userRepository = userRepository;
            _cacheService = cacheService;
            _onlineStatusService = onlineStatusService;
            _geoLocationService = geoLocationService;
            _searchingUsersService = searchingUsersService;
            _logger = logger;
        }

        public async Task<List<MatchDto>> GetMatchesAsync(int userId)
        {
            try
            {
                // Get all matches where the user is either User1 or User2 and the match is confirmed
                var matches = await _matchRepository.GetAllAsync(
                    m => (m.User1Id == userId || m.User2Id == userId),
                    query => query.Include(m => m.User1)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.User2)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.Messages)
                );

                var matchDtos = new List<MatchDto>();

                foreach (var match in matches)
                {
                    // Get the other user in the match
                    var otherUser = match.GetOtherUser(userId);
                    if (otherUser == null || otherUser.Profile == null)
                    {
                        continue;
                    }

                    // Calculate age if birth date is available
                    int? age = null;
                    var today = DateTime.Today;
                    var birthDate = otherUser.Profile.BirthDate;
                    var ageValue = today.Year - birthDate.Year;
                    if (birthDate.Date > today.AddYears(-ageValue))
                    {
                        ageValue--;
                    }
                    age = ageValue;

                    // Get the last message if any
                    var lastMessage = match.Messages.OrderByDescending(m => m.SentAt).FirstOrDefault();
                    string lastMessagePreview = null;

                    if (lastMessage != null)
                    {
                        if (lastMessage.SenderId == userId)
                            // For encrypted messages, just show a placeholder
                            lastMessagePreview = lastMessage.Content;
                        else
                            lastMessagePreview = JsonSerializer.Serialize(new { encryptedText = lastMessage.EncryptedContent, encryptedKey = lastMessage.EncryptedKey, iv = lastMessage.IV });
                        
                    }

                    var lastMessageAt = lastMessage?.SentAt;

                    // Count unread messages (messages sent by the other user that haven't been read)
                    var unreadCount = match.Messages.Count(m =>
                        m.SenderId != userId &&
                        (!m.IsRead));

                    // Get the current user's profile for distance calculation
                    var userProfile = await _profileRepository.SingleOrDefaultAsync(p => p.UserId == userId);

                    // Calculate distance if both users have location data
                    int? distance = null;
                    if (userProfile != null && userProfile.Latitude.HasValue && userProfile.Longitude.HasValue &&
                        otherUser.Profile.Latitude.HasValue && otherUser.Profile.Longitude.HasValue)
                    {
                        distance = (int)Math.Round(_geoLocationService.CalculateDistance(
                            userProfile.Latitude.Value, userProfile.Longitude.Value,
                            otherUser.Profile.Latitude.Value, otherUser.Profile.Longitude.Value
                        ));
                    }

                    // Check if the other user is online
                    bool isOnline = await _onlineStatusService.IsUserOnlineAsync(otherUser.Id);

                    // Create the match DTO
                    var matchDto = new MatchDto
                    {
                        Id = match.Id,
                        OtherUserId = otherUser.Id,
                        FirstName = otherUser.Profile.FirstName,
                        LastName = otherUser.Profile.LastName,
                        Age = age,
                        Bio = otherUser.Profile.Bio,
                        CreatedAt = match.CreatedAt,
                        IsMatched = match.IsMatched,
                        HasResponded = match.GetUserResponse(userId).HasValue,
                        Pictures = otherUser.Profile.Pictures?.Split(',').ToList() ?? new List<string>(),
                        LastMessageAt = lastMessageAt,
                        LastMessagePreview = lastMessagePreview,
                        UnreadCount = unreadCount,
                        Distance = distance,
                        Location = otherUser.Profile.Location,
                        IsOnline = isOnline
                    };

                    matchDtos.Add(matchDto);
                }

                return matchDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting matches for user {UserId}", userId);
                throw;
            }
        }

        public async Task<MatchDto> FindMatchAsync(int userId)
        {
            try
            {
                if (await HasReachedMatchLimitAsync(userId))
                    throw new InvalidOperationException("You have reached your daily match limit");

                var user = await _userRepository.GetUserWithProfileAsync(userId);
                if (user == null || user.Profile == null)
                    throw new InvalidOperationException("User profile not found");

                var today = DateTime.Today;
                var minBirthDate = today.AddYears(-user.Profile.MaxAgePreference - 1).AddDays(1);
                var maxBirthDate = today.AddYears(-user.Profile.MinAgePreference);
                var genderPref = user.Profile.Preference;

                // Fetch potential matches
                var potentialMatches = await _profileRepository.GetAllAsync(
                    p => p.UserId != userId &&
                         p.Gender == genderPref &&
                         p.BirthDate >= minBirthDate &&
                         p.BirthDate <= maxBirthDate,
                    q => q.Include(p => p.User)
                );

                if (!potentialMatches.Any()) return null;

                // Filter by distance if user has location data
                if (user.Profile.Latitude.HasValue && user.Profile.Longitude.HasValue)
                {
                    var userLat = user.Profile.Latitude.Value;
                    var userLon = user.Profile.Longitude.Value;
                    var maxDistance = user.Profile.MaxDistance;

                    // Filter matches by distance
                    potentialMatches = potentialMatches
                        .Where(p =>
                            // Include profiles without location data
                            !p.Latitude.HasValue || !p.Longitude.HasValue ||
                            // Or profiles within the distance limit
                            _geoLocationService.IsWithinDistance(
                                userLat, userLon,
                                p.Latitude.Value, p.Longitude.Value,
                                maxDistance)
                        )
                        .ToList();

                    if (!potentialMatches.Any()) return null;
                }

                // Remove already matched users
                var existingMatches = await _matchRepository.GetAllAsync(
                    m => (m.User1Id == userId || m.User2Id == userId) && m.Status == 1
                );

                var previouslyMatchedIds = existingMatches
                    .Select(m => m.User1Id == userId ? m.User2Id : m.User1Id)
                    .ToHashSet();

                potentialMatches = potentialMatches
                    .Where(p => !previouslyMatchedIds.Contains(p.UserId))
                    .ToList();

                if (!potentialMatches.Any()) return null;

                // Add the current user to the searching users list
                await _searchingUsersService.AddUserToSearchingAsync(userId);

                // Get all users who are currently searching
                var searchingUserIds = await _searchingUsersService.GetSearchingUserIdsAsync();

                // Remove the current user from the list
                searchingUserIds.Remove(userId);

                // Check if there are any other users searching
                if (searchingUserIds.Count == 0)
                {
                    _logger.LogInformation("No other users are currently searching for user {UserId}", userId);
                    return null;
                }

                // Filter potential matches to only include users who are also searching
                var searchingMatches = potentialMatches.Where(p => searchingUserIds.Contains(p.UserId)).ToList();

                if (!searchingMatches.Any())
                {
                    _logger.LogInformation("No searching users match the criteria for user {UserId}", userId);
                    return null;
                }

                // Get online users for status display
                var onlineUserIds = await _onlineStatusService.GetOnlineUserIdsAsync();

                // Select randomly from searching matches
                var random = new Random();
                var selectedProfile = searchingMatches[random.Next(searchingMatches.Count)];
                var otherUser = selectedProfile.User;

                Match match;

                // Acquire the lock before checking/creating matches
                await _matchingLock.WaitAsync();

                try
                {
                    // Check for existing match between these two users
                    match = await _matchRepository.SingleOrDefaultAsync(
                        m => (m.User1Id == userId && m.User2Id == otherUser.Id) ||
                             (m.User1Id == otherUser.Id && m.User2Id == userId)
                    );

                    if (match == null)
                    {
                        match = new Match
                        {
                            User1Id = userId,
                            User2Id = otherUser.Id,
                            CreatedAt = DateTime.UtcNow,
                            Status = 0,
                            User1Response = null, // Current user likes the match
                            User2Response = null  // Other user hasn't responded yet
                        };

                        await _matchRepository.AddAsync(match);
                        await _matchRepository.SaveChangesAsync();
                    }
                }
                finally
                {
                    // Always release the lock
                    _matchingLock.Release();
                }

                await DecrementRemainingMatchesAsync(userId);

                var age = today.Year - selectedProfile.BirthDate.Year;
                if (selectedProfile.BirthDate > today.AddYears(-age)) age--;

                int otherUserId = (match.User1Id == userId) ? match.User2Id : match.User1Id;

                // Calculate distance if both users have location data
                int? distance = null;
                if (user.Profile.Latitude.HasValue && user.Profile.Longitude.HasValue &&
                    selectedProfile.Latitude.HasValue && selectedProfile.Longitude.HasValue)
                {
                    distance = (int)Math.Round(_geoLocationService.CalculateDistance(
                        user.Profile.Latitude.Value, user.Profile.Longitude.Value,
                        selectedProfile.Latitude.Value, selectedProfile.Longitude.Value
                    ));
                }

                // Check if the other user is online
                bool isOnline = onlineUserIds.Contains(otherUserId);

                return new MatchDto
                {
                    Id = match.Id,
                    OtherUserId = otherUserId,
                    FirstName = selectedProfile.FirstName,
                    LastName = selectedProfile.LastName,
                    Bio = selectedProfile.Bio,
                    Age = age,
                    CreatedAt = match.CreatedAt,
                    IsMatched = match.Status == 1,
                    HasResponded = match.GetUserResponse(userId).HasValue,
                    Pictures = selectedProfile.Pictures?.Split(',').ToList() ?? new List<string>(),
                    LastMessageAt = null,
                    LastMessagePreview = null,
                    UnreadCount = 0,
                    Distance = distance,
                    Location = selectedProfile.Location,
                    IsOnline = isOnline
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding match for user {UserId}", userId);
                throw;
            }
        }


        public async Task<bool> RespondToMatchAsync(int userId, MatchResponseDto response)
        {
            try
            {
                // Get the match
                var match = await _matchRepository.GetByIdAsync(response.MatchId);
                if (match == null)
                {
                    throw new InvalidOperationException("Match not found");
                }

                // Verify the user is part of the match
                if (match.User1Id != userId && match.User2Id != userId)
                {
                    throw new InvalidOperationException("User is not part of this match");
                }

                // Acquire the lock before updating the match
                await _matchingLock.WaitAsync();

                try
                {
                    // Reload the match to ensure we have the latest state
                    match = await _matchRepository.GetByIdAsync(response.MatchId);
                    if (match == null)
                    {
                        throw new InvalidOperationException("Match not found");
                    }

                    // Set the user's response
                    match.SetUserResponse(userId, response.Like);

                    // Save changes
                    await _matchRepository.SaveChangesAsync();

                    // Remove the user from the searching list
                    await _searchingUsersService.RemoveUserFromSearchingAsync(userId);

                    // If it's a match, also remove the other user from searching
                    if (match.IsMatched)
                    {
                        int otherUserId = match.User1Id == userId ? match.User2Id : match.User1Id;
                        await _searchingUsersService.RemoveUserFromSearchingAsync(otherUserId);
                    }

                    // Return whether it's a mutual match
                    return match.IsMatched;
                }
                finally
                {
                    // Always release the lock
                    _matchingLock.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error responding to match {MatchId} for user {UserId}", response.MatchId, userId);
                throw;
            }
        }

        public async Task<MatchDto> GetMatchByIdAsync(int userId, int matchId)
        {
            try
            {
                // Get the match
                var match = await _matchRepository.SingleOrDefaultAsync(
                    m => m.Id == matchId && (m.User1Id == userId || m.User2Id == userId),
                    query => query.Include(m => m.User1)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.User2)
                                  .ThenInclude(u => u.Profile)
                                  .Include(m => m.Messages)
                );

                if (match == null)
                {
                    throw new InvalidOperationException("Match not found");
                }

                // Get the other user in the match
                var otherUser = match.GetOtherUser(userId);
                if (otherUser == null || otherUser.Profile == null)
                {
                    throw new InvalidOperationException("Other user not found");
                }

                // Calculate age
                int? age = null;
                var today = DateTime.Today;
                var birthDate = otherUser.Profile.BirthDate;
                var ageValue = today.Year - birthDate.Year;
                if (birthDate.Date > today.AddYears(-ageValue))
                {
                    ageValue--;
                }
                age = ageValue;

                // Get the last message if any
                var lastMessage = match.Messages.OrderByDescending(m => m.SentAt).FirstOrDefault();
                var lastMessagePreview = lastMessage?.Content != null ?
                    lastMessage.Content.Substring(0, Math.Min(50, lastMessage.Content.Length)) : null;

                var lastMessageAt = lastMessage?.SentAt;

                // Count unread messages (messages sent by the other user that haven't been read)
                var unreadCount = match.Messages.Count(m =>
                    m.SenderId != userId &&
                    (!m.IsRead));

                // Get the current user's profile for distance calculation
                var userProfile = await _profileRepository.SingleOrDefaultAsync(p => p.UserId == userId);

                // Calculate distance if both users have location data
                int? distance = null;
                if (userProfile != null && userProfile.Latitude.HasValue && userProfile.Longitude.HasValue &&
                    otherUser.Profile.Latitude.HasValue && otherUser.Profile.Longitude.HasValue)
                {
                    distance = (int)Math.Round(_geoLocationService.CalculateDistance(
                        userProfile.Latitude.Value, userProfile.Longitude.Value,
                        otherUser.Profile.Latitude.Value, otherUser.Profile.Longitude.Value
                    ));
                }

                // Check if the other user is online
                bool isOnline = await _onlineStatusService.IsUserOnlineAsync(otherUser.Id);

                // Create the match DTO
                return new MatchDto
                {
                    Id = match.Id,
                    OtherUserId = otherUser.Id,
                    FirstName = otherUser.Profile.FirstName,
                    LastName = otherUser.Profile.LastName,
                    Age = age,
                    CreatedAt = match.CreatedAt,
                    IsMatched = match.IsMatched,
                    HasResponded = match.GetUserResponse(userId).HasValue,
                    Pictures = otherUser.Profile.Pictures?.Split(',').ToList() ?? new List<string>(),
                    LastMessageAt = lastMessageAt,
                    LastMessagePreview = lastMessagePreview,
                    UnreadCount = unreadCount,
                    Distance = distance,
                    Location = otherUser.Profile.Location,
                    IsOnline = isOnline
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting match {MatchId} for user {UserId}", matchId, userId);
                throw;
            }
        }

        public async Task<bool> HasReachedMatchLimitAsync(int userId)
        {
            var remaining = await GetRemainingMatchesAsync(userId);
            return remaining <= 0;
        }

        public async Task<int> GetRemainingMatchesAsync(int userId)
        {
            // Get the user's subscription
            var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
            if (user == null || user.CurrentSubscription == null)
            {
                return 0;
            }

            var subscription = user.CurrentSubscription.Subscription;
            if (subscription == null)
            {
                return 0;
            }

            // If unlimited matches (-1), return a high number
            if (subscription.ChatsPerDay < 0)
            {
                return 999;
            }

            // Check cache for remaining matches
            var today = DateTime.UtcNow.Date.ToString("yyyyMMdd");
            var cacheKey = string.Format(RemainingMatchesKey, userId, today);

            var remainingMatches = await _cacheService.GetAsync<int?>(cacheKey);
            if (remainingMatches.HasValue)
            {
                return remainingMatches.Value;
            }

            // If not in cache, calculate remaining matches
            var startOfDay = DateTime.UtcNow.Date;
            var endOfDay = startOfDay.AddDays(1);

            var matchesStartedToday = await _matchRepository.GetAllAsync(
                m => (m.User1Id == userId || m.User2Id == userId) &&
                     m.CreatedAt >= startOfDay &&
                     m.CreatedAt < endOfDay
            );

            var matchesUsed = matchesStartedToday.Count();
            var remaining = Math.Max(0, subscription.ChatsPerDay - matchesUsed);

            // Cache the result
            await _cacheService.SetAsync(cacheKey, remaining, TimeSpan.FromHours(1));

            return remaining;
        }

        private async Task DecrementRemainingMatchesAsync(int userId)
        {
            var today = DateTime.UtcNow.Date.ToString("yyyyMMdd");
            var cacheKey = string.Format(RemainingMatchesKey, userId, today);

            var remainingMatches = await _cacheService.GetAsync<int?>(cacheKey);
            if (remainingMatches.HasValue)
            {
                var newValue = Math.Max(0, remainingMatches.Value - 1);
                await _cacheService.SetAsync(cacheKey, newValue, TimeSpan.FromHours(1));
            }
        }
    }
}
