# Blind Date App Backend

This is the backend API for the Blind Date App, built with ASP.NET Core 8.0.

## Features

- JWT Authentication
- Entity Framework Core with SQL Server
- End-to-End Encryption for chat messages
- RESTful API design
- Swagger documentation

## Prerequisites

- .NET 8.0 SDK
- SQL Server (LocalDB or full instance)
- Visual Studio 2022 or Visual Studio Code

## Getting Started

1. Clone the repository
2. Navigate to the BlindDateApi directory
3. Update the connection string in `appsettings.json` if needed
4. Run the following commands:

```bash
# Install EF Core tools globally
dotnet tool install --global dotnet-ef

# Create initial migration
dotnet ef migrations add InitialCreate

# Apply migration to create the database
dotnet ef database update

# Run the application
dotnet run
```

5. The API will be available at https://localhost:5001 and http://localhost:5000
6. Swagger documentation will be available at https://localhost:5001/swagger

## Project Structure

- **Controllers/**: API endpoints
- **Data/**: Database context and configurations
- **DTOs/**: Data Transfer Objects
- **Helpers/**: Utility classes
- **Middleware/**: Custom middleware components
- **Models/**: Entity models
- **Services/**: Business logic and services

## API Endpoints

- **Auth**: User registration and login
- **Profiles**: User profile management
- **Matches**: Match finding and management
- **Messages**: Chat functionality with E2EE
- **Subscriptions**: Subscription plans and management

## Security Features

- JWT token-based authentication
- Password hashing with BCrypt
- End-to-End Encryption for chat messages
- HTTPS enforcement
- CORS policy configuration

## Subscription Plans

The system includes three subscription plans:

1. **Free**:
   - 10 chats per day
   - 2 interests selection
   - 2 pictures
   - Picture reveal if match

2. **Premium** (€4.99/month):
   - 30 chats per day
   - 4 interests selection
   - 4 pictures
   - Picture reveal after 10 messages

3. **VIP** (€9.99/month):
   - Unlimited chats per day
   - 6 interests selection
   - 6 pictures
   - Picture and Age reveal on chat start

## End-to-End Encryption

The chat system uses RSA encryption for secure messaging:

1. Each user generates a public/private key pair
2. Messages are encrypted with the recipient's public key
3. Only the recipient can decrypt messages using their private key
4. Keys are generated client-side for maximum security

## Database Schema

- **Users**: User accounts
- **Profiles**: User profiles with preferences
- **Matches**: Match relationships between users
- **Messages**: Chat messages with encryption
- **Subscriptions**: Available subscription plans
- **UserSubscriptions**: User subscription relationships

## License

This project is licensed under the MIT License - see the LICENSE file for details.
