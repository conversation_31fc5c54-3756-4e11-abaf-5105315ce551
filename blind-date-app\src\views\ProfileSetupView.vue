<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useProfileStore } from '../stores/profile'
import { getCurrentLocation, reverseGeocode, getFallbackLocation } from '../services/geolocation'

const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()

const step = ref(1)
const totalSteps = 3
const isLoading = ref(false)
const errorMessage = ref('')
const locationLoading = ref(false)
const locationError = ref(null)

// Profile data
const profile = ref({
  firstName: '',
  lastName: '',
  birthDate: '',
  bio: '',
  pictures: [],
  interests: [],
  location: '',
  latitude: null,
  longitude: null,
  locationAccuracy: null,
  gender: null,
  preference: null,
  minAgePreference: 18,
  maxAgePreference: 40
})

// Search preferences
const searchPreferences = ref({
  ageRange: [18, 40],
  distance: 50,
  interests: [],
  gender: 'women' // Default value
})

// Computed property for max interests based on subscription
const maxInterests = computed(() => {
  // Get the subscription from the user object
  const userSubscription = authStore.user?.subscription

  // Check if subscription is an object with a name property
  if (userSubscription && typeof userSubscription === 'object' && userSubscription.name) {
    const subscriptionName = userSubscription.name
    return subscriptionName === 'Free' ? 2 :
           subscriptionName === 'Premium' ? 4 : 6
  }

  // Check if subscription is a string
  if (typeof userSubscription === 'string') {
    return userSubscription === 'Free' ? 2 :
           userSubscription === 'Premium' ? 4 : 6
  }

  // Default to Free subscription (2 interests)
  return 2
})

onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // Pre-fill data from auth if available
  if (authStore.user) {
    // Set first name and last name
    if (authStore.user.firstName && authStore.user.lastName) {
      profile.value.firstName = authStore.user.firstName
      profile.value.lastName = authStore.user.lastName
    } else if (authStore.user.name) {
      // Split the name into first and last name
      const nameParts = authStore.user.name.split(' ')
      profile.value.firstName = nameParts[0] || ''
      profile.value.lastName = nameParts.slice(1).join(' ') || ''
    }

    // Set birth date if available
    if (authStore.user.birthDate) {
      profile.value.birthDate = authStore.user.birthDate
    }

    // Set default gender and preference if not already set
    if (!profile.value.gender) {
      profile.value.gender = 1 // Default to male
    }

    if (!profile.value.preference) {
      profile.value.preference = 2 // Default to women
    }

    // Sync searchPreferences.gender with profile.preference
    searchPreferences.value.gender = profile.value.preference === 1 ? 'men' : 'women'
  }

  // Fetch interests from API or use defaults
  try {
    isLoading.value = true
    await profileStore.fetchInterests()
  } catch (error) {
    console.error('Failed to fetch interests:', error)
    // No need to show error message as we're using default values
  } finally {
    isLoading.value = false
  }

  // Try to get the user's location automatically
  // We do this after the component is mounted to avoid blocking the UI
  setTimeout(() => {
    if (!profile.value.latitude && !profile.value.longitude) {
      getLocation()
    }
  }, 1000)
})

const nextStep = () => {
  if (step.value < totalSteps) {
    step.value++
  }
}

const prevStep = () => {
  if (step.value > 1) {
    step.value--
  }
}

const toggleInterest = (interest) => {
  const index = profile.value.interests.indexOf(interest)

  if (index === -1) {
    // Add interest if not at max
    if (profile.value.interests.length < maxInterests.value) {
      profile.value.interests.push(interest)
    }
  } else {
    // Remove interest
    profile.value.interests.splice(index, 1)
  }
}

// Sync the preference radio buttons with the searchPreferences.gender
const updateGenderPreference = (value) => {
  // Update searchPreferences.gender based on profile.preference
  searchPreferences.value.gender = value === 1 ? 'men' : 'women'
}

// Get user's location using browser geolocation API
const getLocation = async () => {
  locationLoading.value = true
  locationError.value = null

  try {
    // Try to get the current location
    const position = await getCurrentLocation()

    // Update profile with coordinates
    profile.value.latitude = position.latitude
    profile.value.longitude = position.longitude
    profile.value.locationAccuracy = position.accuracy

    // Get a human-readable location name using reverse geocoding
    const locationName = await reverseGeocode(position.latitude, position.longitude)
    profile.value.location = locationName

  } catch (error) {
    console.error('Error getting location:', error)
    locationError.value = error.message

    // Try to get a fallback location based on IP
    try {
      const fallbackPosition = await getFallbackLocation()

      // Update profile with fallback coordinates
      profile.value.latitude = fallbackPosition.latitude
      profile.value.longitude = fallbackPosition.longitude
      profile.value.locationAccuracy = fallbackPosition.accuracy

      // Get a human-readable location name
      const locationName = await reverseGeocode(fallbackPosition.latitude, fallbackPosition.longitude)
      profile.value.location = locationName

      // Show a warning that we're using an approximate location
      locationError.value = "Using approximate location. For better accuracy, please allow location access."

    } catch (fallbackError) {
      console.error('Error getting fallback location:', fallbackError)
      locationError.value = "Couldn't determine your location. Please enter it manually."
    }
  } finally {
    locationLoading.value = false
  }
}

const completeSetup = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''

    // Prepare profile data with gender, preference values, and search preferences
    const profileData = { ...profile.value }

    // Set default gender if not set (1 for male, 2 for female)
    if (!profileData.gender) {
      profileData.gender = 1 // Default to male
    }

    // Set default preference if not set (1 for men, 2 for women)
    if (!profileData.preference) {
      profileData.preference = searchPreferences.value.gender === 'men' ? 1 : 2
    }

    // Set age preferences directly in the profile data
    profileData.minAgePreference = searchPreferences.value.ageRange[0]
    profileData.maxAgePreference = searchPreferences.value.ageRange[1]

    // If location is empty but we have coordinates, try to get a location name
    if (!profileData.location && profileData.latitude && profileData.longitude) {
      try {
        profileData.location = await reverseGeocode(profileData.latitude, profileData.longitude)
      } catch (error) {
        console.error('Error getting location name:', error)
        profileData.location = 'Unknown Location'
      }
    }

    // If we don't have coordinates but have a location string, we'll keep it as is
    // The backend will handle string-based locations for backward compatibility

    // Save all profile data in a single request
    try {
      await profileStore.updateProfile(profileData)

      // Also update the search preferences in the store (without making another API call)
      profileStore.setSearchPreferences({
        ageRange: [profileData.minAgePreference, profileData.maxAgePreference],
        gender: profileData.preference === 1 ? 'men' : 'women',
        distance: searchPreferences.value.distance
      })
    } catch (profileError) {
      console.error('Error saving profile data:', profileError)
      // Continue with the process even if the API call fails
      // The profile store will save to localStorage as a fallback
    }

    // Update user profile completion status and remove isNewRegistration flag
    const updatedUser = authStore.updateProfile({ profileCompleted: true })

    // Force localStorage update
    localStorage.setItem('user', JSON.stringify(updatedUser || authStore.user))

    // Small delay to ensure all updates are processed
    await new Promise(resolve => setTimeout(resolve, 300))

    // Navigate to home
    router.push('/home')
  } catch (error) {
    errorMessage.value = error.message || 'Failed to save profile'
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <v-layout>
    <v-main>
      <v-container fluid class="fill-height profile-container">
        <v-row justify="center" align="center">
          <v-col cols="12" sm="11" md="9" lg="7" xl="6">
            <v-card class="profile-card" elevation="8">
              <v-card-title class="text-center text-h4 font-weight-bold">Set Up Your Profile</v-card-title>
              <v-card-subtitle class="text-center">Step {{ step }} of {{ totalSteps }}</v-card-subtitle>

              <!-- Progress bar -->
              <v-card-text>
                <div class="d-flex justify-center mb-4">
                  <v-chip-group>
                    <v-chip
                      v-for="i in totalSteps"
                      :key="i"
                      :color="i <= step ? 'primary' : undefined"
                      :variant="i <= step ? 'elevated' : 'outlined'"
                      size="small"
                      class="mx-1"
                    >
                      {{ i }}
                    </v-chip>
                  </v-chip-group>
                </div>

                <div v-if="isLoading" class="d-flex justify-center my-4">
                  <v-progress-circular
                    indeterminate
                    color="primary"
                    size="64"
                  ></v-progress-circular>
                </div>

                <v-alert
                  v-if="errorMessage"
                  type="error"
                  variant="tonal"
                  class="mt-4"
                >
                  {{ errorMessage }}
                </v-alert>

                <!-- Step 1: Basic Information -->
                <v-window v-model="step">
                  <v-window-item :value="1">
                    <v-sheet class="pa-4 mt-4">
                      <h3 class="text-h5 mb-4">Basic Information</h3>

                      <v-form>
                        <!-- First Name, Last Name, and Birth Date fields removed as they're already collected during registration -->

                        <div class="location-section">
                          <h4 class="text-subtitle-1 mb-2">Your Location</h4>

                          <div v-if="profile.latitude && profile.longitude" class="location-display mb-2">
                            <v-icon start>mdi-map-marker</v-icon>
                            <span>{{ profile.location }}</span>
                            <v-chip size="small" color="primary" class="ml-2">
                              <v-icon size="x-small" start>mdi-check-circle</v-icon>
                              Location verified
                            </v-chip>
                          </div>

                          <v-text-field
                            v-model="profile.location"
                            label="Location"
                            variant="outlined"
                            required
                            :disabled="locationLoading"
                            :hint="profile.latitude ? `Coordinates: ${profile.latitude.toFixed(4)}, ${profile.longitude.toFixed(4)}` : ''"
                            persistent-hint
                          >
                            <template v-slot:append>
                              <v-btn
                                icon
                                variant="text"
                                :loading="locationLoading"
                                @click="getLocation"
                                :disabled="locationLoading"
                                color="primary"
                              >
                                <v-icon>mdi-crosshairs-gps</v-icon>
                              </v-btn>
                            </template>
                          </v-text-field>

                          <v-alert
                            v-if="locationError"
                            type="error"
                            variant="tonal"
                            class="mt-2"
                            density="compact"
                          >
                            {{ locationError }}
                          </v-alert>

                          <p class="text-caption mt-1">
                            We use your location to help you find matches nearby. Your exact coordinates are never shared with other users.
                          </p>
                        </div>

                        <v-textarea
                          v-model="profile.bio"
                          label="Bio"
                          variant="outlined"
                          rows="4"
                        ></v-textarea>
                      </v-form>
                    </v-sheet>
                  </v-window-item>

                  <!-- Step 2: Interests -->
                  <v-window-item :value="2">
                    <v-sheet class="pa-4 mt-4">
                      <h3 class="text-h5 mb-2">Your Interests</h3>
                      <p class="text-body-2 mb-4">
                        Select up to {{ maxInterests }} interests
                      </p>

                      <div class="interest-chip-container">
                        <v-chip
                          v-for="interest in profileStore.interestsData"
                          :key="interest.id"
                          :color="profile.interests.includes(interest.name) ? 'primary' : undefined"
                          :variant="profile.interests.includes(interest.name) ? 'elevated' : 'outlined'"
                          class="ma-1 interest-chip"
                          @click="toggleInterest(interest.name)"
                          size="large"
                          pill
                        >
                          {{ interest.name }}
                        </v-chip>
                      </div>
                    </v-sheet>
                  </v-window-item>

                  <!-- Step 3: Search Preferences -->
                  <v-window-item :value="3">
                    <v-sheet class="pa-4 mt-4">
                      <h3 class="text-h5 mb-4">Search Preferences</h3>

                      <v-form>
                        <h4 class="text-subtitle-1 mb-2">Age Range</h4>
                        <v-row>
                          <v-col cols="12" sm="5">
                            <v-text-field
                              v-model.number="searchPreferences.ageRange[0]"
                              label="Min Age"
                              type="number"
                              variant="outlined"
                              min="18"
                              max="100"
                              density="compact"
                            ></v-text-field>
                          </v-col>

                          <v-col cols="12" sm="2" style="display: flex; align-items: center; justify-content: center;" class="py-0 py-sm-3">
                            <span class="my-2 my-sm-0">to</span>
                          </v-col>

                          <v-col cols="12" sm="5">
                            <v-text-field
                              v-model.number="searchPreferences.ageRange[1]"
                              label="Max Age"
                              type="number"
                              variant="outlined"
                              min="18"
                              max="100"
                              density="compact"
                            ></v-text-field>
                          </v-col>
                        </v-row>

                        <h4 class="text-subtitle-1 mb-2">Maximum Distance (km)</h4>
                        <div class="distance-slider-container">
                          <v-slider
                            v-model="searchPreferences.distance"
                            min="5"
                            max="100"
                            step="5"
                            show-ticks="always"
                            :ticks="[5, 25, 50, 75, 100]"
                            thumb-label
                          >
                            <template v-slot:thumb-label="{ modelValue }">
                              {{ modelValue }} km
                            </template>
                          </v-slider>

                          <div class="distance-display text-center">
                            <v-chip color="primary" size="small">
                              <v-icon start size="small">mdi-map-marker-distance</v-icon>
                              {{ searchPreferences.distance }} km
                            </v-chip>
                          </div>
                        </div>

                        <p class="text-caption mt-1 mb-4">
                          We'll show you matches within this distance from your location.
                        </p>

                        <h4 class="text-subtitle-1 mb-2">I am interested in</h4>
                        <v-radio-group
                          v-model="profile.preference"
                          inline
                          class="mt-1"
                          @update:model-value="updateGenderPreference"
                        >
                          <v-radio
                            label="Men"
                            :value="1"
                            color="primary"
                          ></v-radio>
                          <v-radio
                            label="Women"
                            :value="2"
                            color="primary"
                          ></v-radio>
                        </v-radio-group>

                        <h4 class="text-subtitle-1 mb-2 mt-4">I am a</h4>
                        <v-radio-group
                          v-model="profile.gender"
                          inline
                          class="mt-1"
                        >
                          <v-radio
                            label="Man"
                            :value="1"
                            color="primary"
                          ></v-radio>
                          <v-radio
                            label="Woman"
                            :value="2"
                            color="primary"
                          ></v-radio>
                        </v-radio-group>
                      </v-form>
                    </v-sheet>
                  </v-window-item>
                </v-window>
              </v-card-text>

              <!-- Navigation buttons -->
              <v-card-actions class="justify-space-between nav-buttons">
                <v-btn
                  v-if="step > 1"
                  variant="outlined"
                  @click="prevStep"
                  class="nav-button"
                >
                  <v-icon start>mdi-arrow-left</v-icon>
                  <span>Back</span>
                </v-btn>
                <v-spacer v-else></v-spacer>

                <v-btn
                  v-if="step < totalSteps"
                  color="primary"
                  variant="elevated"
                  @click="nextStep"
                  class="nav-button"
                >
                  <span>Next</span>
                  <v-icon end>mdi-arrow-right</v-icon>
                </v-btn>
                <v-btn
                  v-else
                  color="primary"
                  variant="elevated"
                  :loading="isLoading"
                  @click="completeSetup"
                  class="nav-button"
                >
                  <span>Complete</span>
                  <v-icon end>mdi-check</v-icon>
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  </v-layout>
</template>

<style scoped>
.profile-container {
  padding: 1rem;
}

.profile-card {
  padding: 2rem;
  max-width: 100%;
  margin: 0 auto;
}

.interest-chip-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  margin: 0 auto;
  max-width: 100%;
  overflow-x: hidden;
}

.interest-chip {
  margin: 0.25rem !important;
  font-size: 1rem;
  height: auto !important;
  padding: 0.5rem 1rem;
  white-space: nowrap;
}

/* Location section styles */
.location-section {
  margin-bottom: 1.5rem;
}

.location-display {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 8px;
  background-color: rgba(var(--v-theme-primary), 0.05);
}

/* Distance slider styles */
.distance-slider-container {
  margin-bottom: 0.5rem;
}

.distance-display {
  margin-top: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .profile-card {
    padding: 1.5rem;
  }
}

@media (max-width: 600px) {
  .profile-card {
    padding: 1rem;
  }

  .interest-chip {
    margin: 0.2rem !important;
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
  }

  .interest-chip-container {
    gap: 4px;
  }

  /* Make title smaller on mobile */
  :deep(.v-card-title) {
    font-size: 1.5rem !important;
  }

  /* Adjust location display for mobile */
  .location-display {
    flex-wrap: wrap;
  }

  .location-display .v-chip {
    margin-top: 0.5rem;
    margin-left: 0 !important;
  }
}

@media (max-width: 400px) {
  .profile-card {
    padding: 0.75rem;
  }

  .interest-chip {
    margin: 0.15rem !important;
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }

  .interest-chip-container {
    gap: 2px;
  }

  /* Make title even smaller on very small screens */
  :deep(.v-card-title) {
    font-size: 1.25rem !important;
  }
}

/* Navigation button styles */
.nav-buttons {
  margin-top: 1rem;
}

.nav-button {
  min-width: 100px;
}

.mobile-icon-only {
  min-width: unset;
  width: 40px;
  height: 40px;
  padding: 0;
}

@media (max-width: 400px) {
  .nav-button {
    min-width: 80px;
  }
}
</style>
