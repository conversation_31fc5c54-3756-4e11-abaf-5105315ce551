using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class SearchingUsersCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SearchingUsersCleanupService> _logger;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(1);
        private readonly TimeSpan _searchingTimeout = TimeSpan.FromMinutes(5);

        public SearchingUsersCleanupService(
            IServiceProvider serviceProvider,
            ILogger<SearchingUsersCleanupService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Searching Users Cleanup Service is starting.");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CleanupInactiveSearchingUsers();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while cleaning up inactive searching users.");
                }

                await Task.Delay(_cleanupInterval, stoppingToken);
            }

            _logger.LogInformation("Searching Users Cleanup Service is stopping.");
        }

        private async Task CleanupInactiveSearchingUsers()
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var searchingUsersService = scope.ServiceProvider.GetRequiredService<ISearchingUsersService>();
                await searchingUsersService.CleanupInactiveSearchingUsersAsync(_searchingTimeout);
            }
        }
    }
}
