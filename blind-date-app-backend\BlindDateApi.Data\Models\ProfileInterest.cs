using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlindDateApi.Data.Models
{
    public class ProfileInterest
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProfileId { get; set; }

        [Required]
        public int InterestId { get; set; }

        // Navigation properties
        [ForeignKey("ProfileId")]
        public virtual Profile? Profile { get; set; }

        [ForeignKey("InterestId")]
        public virtual Interest? Interest { get; set; }
    }
}
