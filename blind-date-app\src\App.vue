<script setup>
import { onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'
import { useOnlineStatusStore } from './stores/onlineStatus'
import { useChatStore } from './stores/chat'

const router = useRouter()
const authStore = useAuthStore()
const onlineStatusStore = useOnlineStatusStore()
const chatStore = useChatStore()

const isAuthenticated = computed(() => authStore.isAuthenticated)

onMounted(() => {
  // Check authentication status on app load
  if (isAuthenticated.value) {
    // Redirect to home if already authenticated
    if (router.currentRoute.value.path === '/') {
      router.push('/home')
    }

    // Start pinging to maintain online status
    onlineStatusStore.startPinging()

    // Initialize chat functionality
    chatStore.initialize()
  }
})

onBeforeUnmount(() => {
  // Set user as offline when app is closed
  if (isAuthenticated.value) {
    onlineStatusStore.setOffline()
    chatStore.cleanup()
  }
})
</script>

<template>
  <v-app>
    <v-main>
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </v-main>
  </v-app>
</template>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive styles */
@media (max-width: 600px) {
  .v-container {
    padding: 12px !important;
  }

  .v-card {
    padding: 16px !important;
  }

  .v-card-title {
    font-size: 1.25rem !important;
  }

  .v-card-subtitle {
    font-size: 0.875rem !important;
  }
}

/* Ensure text doesn't overflow on small screens */
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Improve touch targets for mobile */
.v-btn {
  min-height: 36px;
}

/* Ensure proper spacing between elements */
.mobile-spacing {
  margin-bottom: 16px;
}
</style>
