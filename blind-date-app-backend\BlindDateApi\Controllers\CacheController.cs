using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")] // Restrict to admin users only
    public class CacheController : ControllerBase
    {
        private readonly ICacheService _cacheService;
        private readonly ILogger<CacheController> _logger;

        public CacheController(ICacheService cacheService, ILogger<CacheController> logger)
        {
            _cacheService = cacheService;
            _logger = logger;
        }

        /// <summary>
        /// Clears a specific cache entry
        /// </summary>
        /// <param name="key">The cache key to clear</param>
        /// <returns>A status message</returns>
        [HttpDelete("clear/{key}")]
        public async Task<IActionResult> ClearCacheEntry(string key)
        {
            try
            {
                await _cacheService.RemoveAsync(key);
                _logger.LogInformation("Cache entry with key {Key} cleared", key);
                return Ok(new { message = $"Cache entry with key '{key}' cleared" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache entry with key {Key}", key);
                return StatusCode(500, new { message = "An error occurred while clearing the cache entry" });
            }
        }

        /// <summary>
        /// Clears all cache entries with a specific prefix
        /// </summary>
        /// <param name="prefix">The prefix of cache keys to clear</param>
        /// <returns>A status message</returns>
        [HttpDelete("clear-prefix/{prefix}")]
        public IActionResult ClearCacheByPrefix(string prefix)
        {
            // Note: This is a placeholder. The actual implementation would depend on the cache provider.
            // Redis has commands to scan and delete by pattern, but in-memory cache doesn't.
            return StatusCode(501, new { message = "This feature is not implemented yet" });
        }

        /// <summary>
        /// Gets information about the cache
        /// </summary>
        /// <returns>Cache statistics</returns>
        [HttpGet("info")]
        public IActionResult GetCacheInfo()
        {
            // Note: This is a placeholder. The actual implementation would depend on the cache provider.
            // Redis has commands to get statistics, but in-memory cache doesn't expose this information easily.
            var cacheInfo = new
            {
                provider = _cacheService.GetType().Name,
                status = "active"
            };

            return Ok(cacheInfo);
        }
    }
}
