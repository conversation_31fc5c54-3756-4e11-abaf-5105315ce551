using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class RedisCacheService : ICacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly TimeSpan _defaultCacheTime = TimeSpan.FromMinutes(10);
        private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        public RedisCacheService(IDistributedCache distributedCache, ILogger<RedisCacheService> logger)
        {
            _distributedCache = distributedCache;
            _logger = logger;
        }

        public T Get<T>(string key)
        {
            try
            {
                var cachedBytes = _distributedCache.Get(key);
                if (cachedBytes == null)
                {
                    _logger.LogDebug("Cache miss for key: {Key}", key);
                    return default;
                }

                var cachedJson = System.Text.Encoding.UTF8.GetString(cachedBytes);
                var cachedItem = JsonSerializer.Deserialize<T>(cachedJson, _jsonOptions);
                
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return cachedItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item from cache with key: {Key}", key);
                return default;
            }
        }

        public async Task<T> GetAsync<T>(string key)
        {
            try
            {
                var cachedBytes = await _distributedCache.GetAsync(key);
                if (cachedBytes == null)
                {
                    _logger.LogDebug("Cache miss for key: {Key}", key);
                    return default;
                }

                var cachedJson = System.Text.Encoding.UTF8.GetString(cachedBytes);
                var cachedItem = JsonSerializer.Deserialize<T>(cachedJson, _jsonOptions);
                
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return cachedItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting item from cache with key: {Key}", key);
                return default;
            }
        }

        public void Set<T>(string key, T value, TimeSpan? absoluteExpiration = null)
        {
            try
            {
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = absoluteExpiration ?? _defaultCacheTime
                };

                var jsonData = JsonSerializer.Serialize(value, _jsonOptions);
                var encodedData = System.Text.Encoding.UTF8.GetBytes(jsonData);
                
                _distributedCache.Set(key, encodedData, options);
                _logger.LogDebug("Item added to cache with key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting item in cache with key: {Key}", key);
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpiration = null)
        {
            try
            {
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = absoluteExpiration ?? _defaultCacheTime
                };

                var jsonData = JsonSerializer.Serialize(value, _jsonOptions);
                var encodedData = System.Text.Encoding.UTF8.GetBytes(jsonData);
                
                await _distributedCache.SetAsync(key, encodedData, options);
                _logger.LogDebug("Item added to cache with key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting item in cache with key: {Key}", key);
            }
        }

        public void Remove(string key)
        {
            try
            {
                _distributedCache.Remove(key);
                _logger.LogDebug("Item removed from cache with key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing item from cache with key: {Key}", key);
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                await _distributedCache.RemoveAsync(key);
                _logger.LogDebug("Item removed from cache with key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing item from cache with key: {Key}", key);
            }
        }
    }
}
