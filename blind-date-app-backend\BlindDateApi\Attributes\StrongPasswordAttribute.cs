using System;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace BlindDateApi.Attributes
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class StrongPasswordAttribute : ValidationAttribute
    {
        private const string LowercasePattern = "[a-z]";
        private const string UppercasePattern = "[A-Z]";
        private const string DigitPattern = "[0-9]";
        private const string SpecialCharPattern = "[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]";
        private const int MinLength = 8;

        public StrongPasswordAttribute() : base("Password must be at least {0} characters long and contain at least one lowercase letter, one uppercase letter, one digit, and one special character.")
        {
        }

        public override bool IsValid(object? value)
        {
            var password = value as string;
            if (string.IsNullOrEmpty(password))
                return false;

            if (password.Length < MinLength)
                return false;

            if (!Regex.IsMatch(password, LowercasePattern))
                return false;

            if (!Regex.IsMatch(password, UppercasePattern))
                return false;

            if (!Regex.IsMatch(password, DigitPattern))
                return false;

            if (!Regex.IsMatch(password, SpecialCharPattern))
                return false;

            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return string.Format(ErrorMessageString, MinLength);
        }
    }
}
