using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ProfilesController : ControllerBase
    {
        private readonly IRepository<Profile> _profileRepository;
        private readonly IRepository<Interest> _interestRepository;
        private readonly IRepository<ProfileInterest> _profileInterestRepository;
        private readonly IUserRepository _userRepository;
        private readonly ILogger<ProfilesController> _logger;

        public ProfilesController(
            IRepository<Profile> profileRepository,
            IRepository<Interest> interestRepository,
            IRepository<ProfileInterest> profileInterestRepository,
            IUserRepository userRepository,
            ILogger<ProfilesController> logger)
        {
            _profileRepository = profileRepository;
            _interestRepository = interestRepository;
            _profileInterestRepository = profileInterestRepository;
            _userRepository = userRepository;
            _logger = logger;
        }

        [HttpGet("me")]
        public async Task<ActionResult<ProfileDto>> GetMyProfile()
        {
            var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
            if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                return Unauthorized();

            var profile = await _profileRepository.SingleOrDefaultAsync(
                p => p.UserId == userIdInt,
                include: query => query.Include(p => p.ProfileInterests)
                    .ThenInclude(pi => pi.Interest));

            if (profile == null)
                return NotFound();

            return Ok(new ProfileDto
            {
                Id = profile.Id,
                FirstName = profile.FirstName,
                LastName = profile.LastName,
                BirthDate = profile.BirthDate,
                Age = profile.Age,
                Bio = profile.Bio,
                Location = profile.Location,
                Latitude = profile.Latitude,
                Longitude = profile.Longitude,
                LocationAccuracy = profile.LocationAccuracy,
                LocationUpdatedAt = profile.LocationUpdatedAt,
                MaxDistance = profile.MaxDistance,
                Gender = profile.Gender,
                Preference = profile.Preference,
                MinAgePreference = profile.MinAgePreference,
                MaxAgePreference = profile.MaxAgePreference,
                Interests = profile.InterestsList,
                Pictures = profile.PicturesList
            });
        }

        [HttpPut("me")]
        public async Task<ActionResult> UpdateMyProfile(UpdateProfileDto updateProfileDto)
        {
            var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
            if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                return Unauthorized();

            var user = await _userRepository.GetUserWithSubscriptionsAsync(userIdInt);
            var profile = await _profileRepository.SingleOrDefaultAsync(p => p.UserId == userIdInt);

            if (profile == null)
                return NotFound();

            // Get user's subscription to check limits
            var subscription = user?.CurrentSubscription?.Subscription;
            if (subscription == null)
                return BadRequest("No active subscription found");

            // Check if interests exceed the limit
            if (updateProfileDto.Interests != null &&
                updateProfileDto.Interests.Count > subscription.MaxInterests)
            {
                return BadRequest($"You can only have up to {subscription.MaxInterests} interests with your current subscription");
            }

            // Check if pictures exceed the limit
            if (updateProfileDto.Pictures != null &&
                updateProfileDto.Pictures.Count > subscription.MaxPictures)
            {
                return BadRequest($"You can only have up to {subscription.MaxPictures} pictures with your current subscription");
            }

            // Update profile
            if (!string.IsNullOrEmpty(updateProfileDto.FirstName))
                profile.FirstName = updateProfileDto.FirstName;

            if (!string.IsNullOrEmpty(updateProfileDto.LastName))
                profile.LastName = updateProfileDto.LastName;

            if (!string.IsNullOrEmpty(updateProfileDto.Bio))
                profile.Bio = updateProfileDto.Bio;

            if (!string.IsNullOrEmpty(updateProfileDto.Location))
                profile.Location = updateProfileDto.Location;

            // Update geolocation fields if provided
            if (updateProfileDto.Latitude.HasValue && updateProfileDto.Longitude.HasValue)
            {
                profile.Latitude = updateProfileDto.Latitude.Value;
                profile.Longitude = updateProfileDto.Longitude.Value;
                profile.LocationUpdatedAt = DateTime.UtcNow;

                if (updateProfileDto.LocationAccuracy.HasValue)
                    profile.LocationAccuracy = updateProfileDto.LocationAccuracy.Value;
            }

            if (updateProfileDto.MaxDistance.HasValue)
                profile.MaxDistance = updateProfileDto.MaxDistance.Value;

            if (updateProfileDto.Gender.HasValue)
                profile.Gender = updateProfileDto.Gender.Value;

            if (updateProfileDto.Preference.HasValue)
                profile.Preference = updateProfileDto.Preference.Value;

            if (updateProfileDto.MinAgePreference.HasValue)
                profile.MinAgePreference = updateProfileDto.MinAgePreference.Value;

            if (updateProfileDto.MaxAgePreference.HasValue)
                profile.MaxAgePreference = updateProfileDto.MaxAgePreference.Value;

            // Update interests if provided
            if (updateProfileDto.Interests != null)
            {
                // Get existing profile interests
                var existingProfileInterests = await _profileInterestRepository
                    .GetAllAsync(pi => pi.ProfileId == profile.Id, include: i => i.Include(pi => pi.Interest));

                // Remove interests that are no longer selected
                foreach (var profileInterest in existingProfileInterests)
                {
                    if (profileInterest.Interest != null &&
                        !updateProfileDto.Interests.Contains(profileInterest.Interest.Name))
                    {
                        await _profileInterestRepository.DeleteAsync(profileInterest.Id);
                    }
                }

                // Add new interests
                var existingInterestNames = existingProfileInterests
                    .Where(pi => pi.Interest != null)
                    .Select(pi => pi.Interest!.Name)
                    .ToList();

                var newInterestNames = updateProfileDto.Interests
                    .Where(name => !existingInterestNames.Contains(name))
                    .ToList();

                if (newInterestNames.Any())
                {
                    // Get all interests from the database
                    var allInterests = await _interestRepository.GetAllAsync();
                    var interestsByName = allInterests.ToDictionary(i => i.Name, i => i);

                    foreach (var interestName in newInterestNames)
                    {
                        if (interestsByName.TryGetValue(interestName, out var interest))
                        {
                            var profileInterest = new ProfileInterest
                            {
                                ProfileId = profile.Id,
                                InterestId = interest.Id
                            };

                            await _profileInterestRepository.AddAsync(profileInterest);
                        }
                    }
                }
            }

            if (updateProfileDto.Pictures != null)
                profile.PicturesList = updateProfileDto.Pictures;

            try
            {
                _profileRepository.Update(profile);
                await _profileRepository.SaveChangesAsync();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile");
                return StatusCode(500, "An error occurred while updating the profile");
            }
        }
    }


}
