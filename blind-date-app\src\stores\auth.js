import { defineStore } from 'pinia'
import { authService } from '../services/api'
import { useOnlineStatusStore } from './onlineStatus'
import signalRService from '../services/signalr'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: JSON.parse(localStorage.getItem('user')) || null,
    isAuthenticated: localStorage.getItem('user') !== null,
    isLoading: false,
    error: null,
    privateKey: localStorage.getItem('privateKey') || null,
    publicKey: localStorage.getItem('publicKey') || null,
    passwordResetStatus: null
  }),

  getters: {
    token: (state) => state.user?.token || null
  },

  actions: {
    async login(email, password) {
      this.isLoading = true
      this.error = null

      try {
        const response = await authService.login(email, password)

        const userData = response.data
        const user = {
          id: userData.userId,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          token: userData.token,
          tokenExpiration: userData.tokenExpiration,
          subscription: userData.subscription,
          profileCompleted: true
        }

        this.user = user
        this.isAuthenticated = true
        localStorage.setItem('user', JSON.stringify(user))

        // Store encryption keys if provided
        if (userData.publicKey) {
          this.publicKey = userData.publicKey
          localStorage.setItem('publicKey', userData.publicKey)
        } else {
          // Generate keys if not provided
          await this.generateKeyPair()
        }

        if (userData.privateKey) {
          this.privateKey = userData.privateKey
          localStorage.setItem('privateKey', userData.privateKey)

          // Store the encrypted private key on the server using the login password
          if (password) {
            this.storeEncryptedPrivateKey(email, password).catch(error => {
              console.error('Failed to store encrypted private key on server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        } else {
          // Try to retrieve the encrypted private key from the server
          if (password) {
            this.retrievePrivateKey(password).catch(error => {
              console.error('Failed to retrieve private key from server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        }

        // Start pinging to maintain online status
        const onlineStatusStore = useOnlineStatusStore()
        onlineStatusStore.startPinging()

        // Reconnect SignalR with the new token
        signalRService.reconnectWithNewToken().catch(error => {
          console.error('Failed to reconnect SignalR after login:', error)
        })

        return user
      } catch (error) {
        // Handle validation errors from the API
        if (error.response?.data?.errors) {
          // Extract validation error messages
          const errorMessages = []
          const errors = error.response.data.errors

          // Loop through all error fields
          for (const field in errors) {
            if (Array.isArray(errors[field])) {
              // Add each error message for this field
              errors[field].forEach(message => {
                errorMessages.push(message)
              })
            }
          }

          // Join all error messages
          this.error = errorMessages.join('\n')
        } else {
          // Handle other types of errors
          this.error = error.response?.data || error.message || 'Failed to login'
        }

        throw new Error(this.error)
      } finally {
        this.isLoading = false
      }
    },

    async register(registerData) {
      this.isLoading = true
      this.error = null
      try {
        const response = await authService.register(registerData)
        const userData = response.data

        // Ensure we have a valid token
        if (!userData.token) {
          throw new Error('Authentication failed: No token received')
        }

        const user = {
          id: userData.userId,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          token: userData.token,
          tokenExpiration: userData.tokenExpiration,
          subscription: userData.subscription,
          profileCompleted: false,
          // Add a flag to indicate this is a new registration
          isNewRegistration: true
        }

        this.user = user
        this.isAuthenticated = true
        localStorage.setItem('user', JSON.stringify(user))

        // Store encryption keys if provided
        if (userData.publicKey) {
          this.publicKey = userData.publicKey
          localStorage.setItem('publicKey', userData.publicKey)
        } else {
          // Generate keys if not provided
          await this.generateKeyPair()
        }

        if (this.privateKey) {
          // Store the encrypted private key on the server using the registration password
          if (registerData && registerData.password) {
            await this.storeEncryptedPrivateKey(registerData.email,registerData.password).catch(error => {
              console.error('Failed to store encrypted private key on server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        } else {
          // Try to retrieve the encrypted private key from the server
          if (registerData && registerData.password) {
            this.retrievePrivateKey(registerData.password).catch(error => {
              console.error('Failed to retrieve private key from server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        }

        // Start pinging to maintain online status
        const onlineStatusStore = useOnlineStatusStore()
        onlineStatusStore.startPinging()

        // Reconnect SignalR with the new token
        signalRService.reconnectWithNewToken().catch(error => {
          console.error('Failed to reconnect SignalR after registration:', error)
        })

        // Force a small delay to ensure localStorage is updated
        await new Promise(resolve => setTimeout(resolve, 100))

        // Redirect directly to profile setup after successful registration
        setTimeout(() => {
          // Force a full page reload to the profile setup page
          window.location.replace('/profile-setup')
        }, 500)

        return user
      } catch (error) {
        // Handle validation errors from the API
        if (error.response?.data?.errors) {
          // Extract validation error messages
          const errorMessages = []
          const errors = error.response.data.errors

          // Loop through all error fields
          for (const field in errors) {
            if (Array.isArray(errors[field])) {
              // Add each error message for this field
              errors[field].forEach(message => {
                errorMessages.push(message)
              })
            }
          }

          // Join all error messages
          this.error = errorMessages.join('\n')
        } else {
          // Handle other types of errors
          this.error = error.response?.data || error.message || 'Failed to register'
        }

        throw new Error(this.error)
      } finally {
        this.isLoading = false
      }
    },

    async loginWithSocial(provider, providedUserData = null) {
      this.isLoading = true
      this.error = null

      try {
        // If userData is not provided, get it from the provider
        const socialUserData = providedUserData || await this._getSocialUserData(provider)

        //Generate secondary password
        // Create a base string using user data
        const baseString = `${socialUserData.email}|social-login-user|secondary-generated-password`;

        // Create a hash of the base string
        const encoder = new TextEncoder();
        const data = encoder.encode(baseString);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);

        // Convert hash to Base64
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashBase64 = btoa(String.fromCharCode.apply(null, hashArray));

        // Call our backend API with the social login data
        const response = await authService.socialLogin({
          email: socialUserData.email,
          firstName: socialUserData.firstName,
          lastName: socialUserData.lastName,
          password: hashBase64,
          provider: provider,
          providerId: socialUserData.id
        })

        const userData = response.data
        const user = {
          id: userData.userId,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          token: userData.token,
          tokenExpiration: userData.tokenExpiration,
          subscription: userData.subscription,
          profileCompleted: userData.publicKey ? true : false,
          // Add a flag to indicate this is a new registration
          isNewRegistration: userData.publicKey ? false : true
        }

        this.user = user
        this.isAuthenticated = true
        localStorage.setItem('user', JSON.stringify(user))

        // Store encryption keys if provided
        if (userData.publicKey) {
          this.publicKey = userData.publicKey
          localStorage.setItem('publicKey', userData.publicKey)
        } else {
          // Generate keys if not provided
          await this.generateKeyPair()
        }

        if (this.privateKey) {
          // Store the encrypted private key on the server using the registration password
          if (hashBase64) {
            await this.storeEncryptedPrivateKey(userData.email, hashBase64).catch(error => {
              console.error('Failed to store encrypted private key on server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        } else {
          // Try to retrieve the encrypted private key from the server
          if (hashBase64) {
            await this.retrievePrivateKey(hashBase64).catch(error => {
              console.error('Failed to retrieve private key from server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        }

        // Start pinging to maintain online status
        const onlineStatusStore = useOnlineStatusStore()
        onlineStatusStore.startPinging()

        // Reconnect SignalR with the new token
        signalRService.reconnectWithNewToken().catch(error => {
          console.error('Failed to reconnect SignalR after social login:', error)
        })

        // Force a small delay to ensure localStorage is updated
        await new Promise(resolve => setTimeout(resolve, 100))

        // Redirect directly to profile setup after successful social login
        setTimeout(() => {
          if (!userData.publicKey)
            // Force a full page reload to the profile setup page
            window.location.replace('/profile-setup')
          else
          window.location.replace('/home')
        }, 500)

        return user
      } catch (error) {
        // Handle validation errors from the API
        if (error.response?.data?.errors) {
          // Extract validation error messages
          const errorMessages = []
          const errors = error.response.data.errors

          // Loop through all error fields
          for (const field in errors) {
            if (Array.isArray(errors[field])) {
              // Add each error message for this field
              errors[field].forEach(message => {
                errorMessages.push(message)
              })
            }
          }

          // Join all error messages
          this.error = errorMessages.join('\n')
        } else {
          // Handle other types of errors
          this.error = error.response?.data || error.message || `Failed to login with ${provider}`
        }

        throw new Error(this.error)
      } finally {
        this.isLoading = false
      }
    },

    // Helper method to get user data from social provider
    async _getSocialUserData(provider) {
      try {
        // Import the social auth service
        const { getSocialUserData } = await import('../services/socialAuth')

        // Get user data from the social provider
        const userData = await getSocialUserData(provider)

        // Return the user data
        return {
          id: userData.id,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          picture: userData.picture,
          token: userData.token,
          provider: userData.provider
        }
      } catch (error) {
        console.error(`Error getting user data from ${provider}:`, error)
        throw new Error(`Failed to get user data from ${provider}: ${error.message}`)
      }
    },

    async logout() {
      // Set user as offline
      const onlineStatusStore = useOnlineStatusStore()
      await onlineStatusStore.setOffline()
      onlineStatusStore.stopPinging()

      this.user = null
      this.isAuthenticated = false
      this.privateKey = null
      this.publicKey = null
      localStorage.removeItem('user')
      localStorage.removeItem('privateKey')
      localStorage.removeItem('publicKey')
    },

    updateProfile(profileData) {
      // Create a new user object with the updated data
      const updatedUser = { ...this.user, ...profileData }

      // If profile is being marked as completed, ensure the flag is set to true
      if (profileData.profileCompleted) {
        updatedUser.profileCompleted = true

        // Also remove the isNewRegistration flag
        updatedUser.isNewRegistration = false
      }

      // Update the store
      this.user = updatedUser

      // Save to localStorage
      localStorage.setItem('user', JSON.stringify(this.user))

      // Return the updated user object
      return this.user
    },

    // Get the public key of a user
    async getPublicKey(userId) {
      try {
        // Import the key validation function
        const { isValidPublicKey } = await import('../services/encryption')

        // First check if we have it in localStorage
        const cachedKey = localStorage.getItem(`publicKey_${userId}`)
        if (cachedKey) {
          console.log(`Found cached public key for user ${userId} in localStorage`)

          return cachedKey
        }

        // If not in localStorage or invalid, try to get from the server
        const response = await authService.getPublicKey(userId)
        const publicKey = response.data.publicKey

        // Validate the server key
        if (publicKey) {
          // Cache the valid key in localStorage
          localStorage.setItem(`publicKey_${userId}`, publicKey)
          return publicKey
        }
      } catch (error) {
        console.error(`Failed to get public key for user ${userId}:`, error)

        // If the user doesn't have a public key and it's the current user, generate one
        if (userId === this.user?.id) {
          try {
            // Generate a new key pair for the current user
            const { publicKey } = await this.generateKeyPair()

            // Update the user's public key on the server
            await this.updatePublicKey(publicKey)

            // Cache the key in localStorage
            localStorage.setItem(`publicKey_${userId}`, publicKey)

            return publicKey
          } catch (genError) {
            console.error('Failed to generate and update key pair:', genError)
            // Return a simple key as a last resort
            const simpleKey = Math.random().toString(36).substring(2, 15) + Date.now().toString(36)
            return simpleKey
          }
        }

        // For other users, return a placeholder key
        return 'placeholder-key-' + userId
      }
    },

    // Update the current user's public key
    async updatePublicKey(publicKey) {
      try {
        await authService.updatePublicKey(publicKey)
        this.publicKey = publicKey
        localStorage.setItem('publicKey', publicKey)
        return true
      } catch (error) {
        console.error('Error updating public key:', error)
        throw error
      }
    },

    // Generate a new key pair using the encryption service
    async generateKeyPair() {
      try {
        // Import the key generation function
        const { generateKeyPair } = await import('../services/encryption')

        // Generate a new key pair
        const { privateKey, publicKey } = await generateKeyPair()

        // Store the keys
        this.privateKey = privateKey
        this.publicKey = publicKey

        // Store in localStorage
        localStorage.setItem('privateKey', privateKey)
        localStorage.setItem('publicKey', publicKey)

        // Update the public key on the server
        await this.updatePublicKey(publicKey)
        
        return { privateKey, publicKey }
      } catch (error) {
        console.error('Failed to generate key pair:', error)
        throw error
      }
    },

    // Helper method to convert ArrayBuffer to Base64
    _arrayBufferToBase64(buffer) {
      const binary = String.fromCharCode.apply(null, new Uint8Array(buffer))
      return window.btoa(binary)
    },

    // Store the encrypted private key on the server
    async storeEncryptedPrivateKey(email, password) {
      try {
        if (!this.privateKey) {
          throw new Error('No private key available to encrypt')
        }

        if (!password) {
          throw new Error('Password is required to encrypt the private key')
        }

        // Import the encryption function
        const { encryptPrivateKey } = await import('../services/encryption')

        // Encrypt the private key with the password
        const { encryptedPrivateKey, salt, iv } = await encryptPrivateKey(this.privateKey, password)

        // Store the encrypted private key on the server
        await authService.updateEncryptedPrivateKey(encryptedPrivateKey, salt, iv)

        if (email) {
          let masterKey = `${email}|master-password|eff4f9a8-410f-4a9b-974b-5bf01cc37aa8|7761b7f3-852b-415c-aabd-c2645d155295`

          const { encryptedPrivateKey, salt, iv } = await encryptPrivateKey(this.privateKey, masterKey)

          await authService.updateEncryptedMasterPrivateKey(encryptedPrivateKey, salt, iv)
        }

        return true
      } catch (error) {
        console.error('Failed to store encrypted private key:', error)
        throw error
      }
    },

    // Retrieve and decrypt the private key from the server
    async retrievePrivateKey(password) {
      try {
        if (!password) {
          throw new Error('Password is required to decrypt the private key')
        }

        // If not found in localStorage or invalid, try to get from the server
        const response = await authService.getEncryptedPrivateKey()
        const { encryptedPrivateKey, salt, iv } = response.data

        if (!encryptedPrivateKey || !salt || !iv) {
          throw new Error('Encrypted private key not found on server')
        }

        // Import the decryption function
        const { decryptPrivateKey } = await import('../services/encryption')

        // Decrypt the private key with the password
        const privateKey = await decryptPrivateKey({ encryptedPrivateKey, salt, iv }, password)

        // Store the decrypted private key
        this.privateKey = privateKey
        localStorage.setItem('privateKey', privateKey)

        return privateKey
      } catch (error) {
        console.error('Failed to retrieve private key:', error)

        // Generate a new key pair as a fallback
        try {
          const { privateKey, publicKey } = await this.generateKeyPair()

          // Update the public key on the server
          await this.updatePublicKey(publicKey)

          return privateKey
        } catch (genError) {
          console.error('Failed to generate fallback key pair:', genError)

          // Last resort: create a simple key
          const simpleKey = Math.random().toString(36).substring(2, 15) + Date.now().toString(36)
          this.privateKey = simpleKey
          localStorage.setItem('privateKey', simpleKey)

          return simpleKey
        }
      }
    },

    // Retrieve and decrypt the private key from the server
    async retrieveMasterPrivateKey() {
      try {
        // If not found in localStorage or invalid, try to get from the server
        const response = await authService.getEncryptedMasterPrivateKey()
        const { email, encryptedPrivateKey, salt, iv } = response.data

        let masterKey = `${email}|master-password|eff4f9a8-410f-4a9b-974b-5bf01cc37aa8|7761b7f3-852b-415c-aabd-c2645d155295`

        if (!encryptedPrivateKey || !salt || !iv) {
          throw new Error('Encrypted private key not found on server')
        }

        // Import the decryption function
        const { decryptPrivateKey } = await import('../services/encryption')

        // Decrypt the private key with the password
        const privateKey = await decryptPrivateKey({ encryptedPrivateKey, salt, iv }, masterKey)

        // Store the decrypted private key
        this.privateKey = privateKey
        localStorage.setItem('privateKey', privateKey)

        return privateKey
      } catch (error) {
        console.error('Failed to retrieve private key:', error)

        // Generate a new key pair as a fallback
        try {
          const { privateKey, publicKey } = await this.generateKeyPair()

          // Update the public key on the server
          await this.updatePublicKey(publicKey)

          return privateKey
        } catch (genError) {
          console.error('Failed to generate fallback key pair:', genError)

          // Last resort: create a simple key
          const simpleKey = Math.random().toString(36).substring(2, 15) + Date.now().toString(36)
          this.privateKey = simpleKey
          localStorage.setItem('privateKey', simpleKey)

          return simpleKey
        }
      }
    },

    // Forgot password - request a password reset link
    async forgotPassword(email) {
      this.isLoading = true
      this.error = null
      this.passwordResetStatus = null

      try {
        const response = await authService.forgotPassword(email)
        this.passwordResetStatus = 'email-sent'
        return response.data
      } catch (error) {
        if (error.response?.data) {
          this.error = error.response.data
        } else {
          this.error = error.message || 'Failed to process forgot password request'
        }
        throw new Error(this.error)
      } finally {
        this.isLoading = false
      }
    },

    // Reset password with token
    async resetPassword(token, password, confirmPassword) {
      this.isLoading = true
      this.error = null
      this.passwordResetStatus = null

      try {
        const response = await authService.resetPassword(token, password, confirmPassword)
        this.passwordResetStatus = 'reset-success'

        return response.data
      } catch (error) {
        if (error.response?.data) {
          this.error = error.response.data
        } else {
          this.error = error.message || 'Failed to reset password'
        }
        throw new Error(this.error)
      } finally {
        this.isLoading = false
      }
    }
  }
})
