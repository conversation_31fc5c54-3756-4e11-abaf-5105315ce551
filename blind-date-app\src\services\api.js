import axios from 'axios'

const API_URL = (import.meta.env.VITE_API_URL || 'http://localhost:5192') + '/api'

// Create axios instance with base URL
export const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Add request interceptor to add auth token to requests
apiClient.interceptors.request.use(
  config => {
    try {
      const userJson = localStorage.getItem('user')

      if (userJson) {
        const user = JSON.parse(userJson)

        if (user && user.token) {
          config.headers.Authorization = `Bearer ${user.token}`
        }
      }
    } catch (error) {
      // Silent error handling for auth token processing
    }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Add response interceptor to handle common errors
apiClient.interceptors.response.use(
  response => {
    return response
  },
  error => {
    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      // Only redirect to login if not on register, login, or profile-setup page
      const currentPath = window.location.pathname

      // Check if this is a new registration
      const user = JSON.parse(localStorage.getItem('user') || 'null')
      const isNewRegistration = user && user.isNewRegistration

      if (currentPath !== '/register' && currentPath !== '/login' &&
          !(isNewRegistration && currentPath === '/profile-setup')) {
        // Clear user data and redirect to login
        localStorage.removeItem('user')
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  }
)

export const authService = {
  login(email, password) {
    return apiClient.post('/auth/login', { email, password })
  },

  register(registerData) {
    return apiClient.post('/auth/register', registerData)
  },

  socialLogin(socialLoginData) {
    return apiClient.post('/auth/social-login', socialLoginData)
  },

  getPublicKey(userId) {
    return apiClient.get(`/keys/public/${userId}`)
  },

  updatePublicKey(publicKey) {
    return apiClient.put('/keys/public', { publicKey })
  },

  // New methods for encrypted private key
  getEncryptedPrivateKey() {
    return apiClient.get('/keys/private')
  },

  updateEncryptedPrivateKey(encryptedPrivateKey, salt, iv) {
    return apiClient.put('/keys/private', {
      encryptedPrivateKey,
      salt,
      iv
    })
  },

  getEncryptedMasterPrivateKey() {
    return apiClient.get('/keys/master')
  },

  updateEncryptedMasterPrivateKey(encryptedPrivateKey, salt, iv) {
    return apiClient.put('/keys/master', {
      encryptedPrivateKey,
      salt,
      iv
    })
  },

  getAllKeys() {
    return apiClient.get('/keys/all')
  },

  forgotPassword(email) {
    return apiClient.post('/auth/forgot-password', { email })
  },

  resetPassword(token, password, confirmPassword) {
    return apiClient.post('/auth/reset-password', {
      token,
      password,
      confirmPassword
    })
  }
}

export const profileService = {
  getProfile() {
    return apiClient.get('/profiles/me')
  },

  updateProfile(profileData) {
    return apiClient.put('/profiles/me', profileData)
  },

  updateSearchPreferences(preferences) {
    // This maps to the same endpoint as updateProfile, but with different data
    return apiClient.put('/profiles/me', {
      minAgePreference: preferences.ageRange[0],
      maxAgePreference: preferences.ageRange[1],
      preference: preferences.gender === 'women' ? 2 : 1, // 1 for men, 2 for women
      maxDistance: preferences.distance // Add distance parameter
    })
  },

  uploadProfilePicture(formData) {
    return apiClient.post('/profile-pictures/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  deleteProfilePicture(pictureUrl) {
    // URL encode the picture URL to handle special characters
    const encodedUrl = encodeURIComponent(pictureUrl)
    return apiClient.delete(`/profile-pictures/${encodedUrl}`)
  },

  getInterests() {
    return apiClient.get('/interests')
  }
}

export const matchService = {
  getMatches() {
    return apiClient.get('/matches')
  },

  getSearchingStatus() {
    return apiClient.get('/matches/searching-status')
  },

  startSearching() {
    return apiClient.post('/matches/start-searching')
  },

  stopSearching() {
    return apiClient.post('/matches/stop-searching')
  },

  findMatch() {
    return apiClient.post('/matches/search')
  },

  likeMatch(matchId) {
    return apiClient.post(`/matches/${matchId}/like`)
  },

  dislikeMatch(matchId) {
    return apiClient.post(`/matches/${matchId}/dislike`)
  }
}

export const chatService = {
  getConversations() {
    return apiClient.get('/chat/conversations')
  },

  getConversation(id) {
    return apiClient.get(`/chat/conversations/${id}`)
  },

  getMessages(conversationId, lastMessageId = null, count = 20) {
    let url = `/chat/conversations/${conversationId}/messages`
    if (lastMessageId) {
      url += `?lastMessageId=${lastMessageId}&count=${count}`
    } else if (count !== 20) {
      url += `?count=${count}`
    }
    return apiClient.get(url)
  },

  sendMessage(message) {
    return apiClient.post('/chat/messages', message)
  },

  markAsRead(conversationId, lastMessageId = null) {
    return apiClient.post(`/chat/conversations/${conversationId}/read`, {
      conversationId,
      lastMessageId
    })
  },

  createConversation(dto) {
    return apiClient.post('/chat/conversations', dto)
  },

  updateMessagesWithMatchId(conversationId, matchId) {
    return apiClient.put(`/chat/conversations/${conversationId}/update-match`, { matchId })
  }
}

export const subscriptionService = {
  getSubscriptions() {
    return apiClient.get('/subscriptions')
  },

  subscribe(subscriptionId, paymentDetails) {
    return apiClient.post('/subscriptions/subscribe', {
      subscriptionId,
      ...paymentDetails
    })
  }
}

export const onlineStatusService = {
  ping() {
    return apiClient.post('/onlinestatus/ping')
  },

  setOffline() {
    return apiClient.post('/onlinestatus/offline')
  },

  getUserStatus(userId) {
    return apiClient.get(`/onlinestatus/status/${userId}`)
  }
}

export default {
  auth: authService,
  profile: profileService,
  match: matchService,
  chat: chatService,
  subscription: subscriptionService,
  onlineStatus: onlineStatusService
}
