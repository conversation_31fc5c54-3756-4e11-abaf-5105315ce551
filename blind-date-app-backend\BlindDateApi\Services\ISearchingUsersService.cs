using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BlindDateApi.Services
{
    public interface ISearchingUsersService
    {
        /// <summary>
        /// Adds a user to the searching users list
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Task</returns>
        Task AddUserToSearchingAsync(int userId);
        
        /// <summary>
        /// Removes a user from the searching users list
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Task</returns>
        Task RemoveUserFromSearchingAsync(int userId);
        
        /// <summary>
        /// Gets all users who are currently searching
        /// </summary>
        /// <returns>List of user IDs</returns>
        Task<List<int>> GetSearchingUserIdsAsync();
        
        /// <summary>
        /// Checks if a user is currently searching
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>True if the user is searching, false otherwise</returns>
        Task<bool> IsUserSearchingAsync(int userId);
        
        /// <summary>
        /// Gets the count of users who are currently searching
        /// </summary>
        /// <returns>Count of searching users</returns>
        Task<int> GetSearchingUsersCountAsync();
        
        /// <summary>
        /// Cleans up users who have been searching for too long
        /// </summary>
        /// <param name="searchingTimeout">The timeout period</param>
        /// <returns>Task</returns>
        Task CleanupInactiveSearchingUsersAsync(TimeSpan searchingTimeout);
    }
}
