import { defineStore } from 'pinia'
import { useAuthStore } from './auth'
import { chatService } from '../services/api'
import signalRService from '../services/signalr'
import { decryptMessage } from '../services/encryption'

// Helper function to check if user is authenticated and has a token
function isUserAuthenticated() {
  try {
    const userJson = localStorage.getItem('user')
    if (userJson) {
      const user = JSON.parse(userJson)
      return user && user.token
    }
  } catch (error) {
    console.error('Error checking authentication status:', error)
  }
  return false
}

export const useChatStore = defineStore('chat', {
  state: () => ({
    conversations: [],
    currentConversationId: null,
    messages: {},
    isLoading: false,
    error: null,
    connectionState: 'disconnected',
    unreadCounts: {}
  }),

  getters: {
    currentConversation: (state) => {
      if (!state.currentConversationId) return null
      return state.conversations.find(c => c.id === state.currentConversationId)
    },

    currentMessages: (state) => {
      if (!state.currentConversationId) {
        return []
      }
      const messages = state.messages[state.currentConversationId] || []
      return messages
    },

    totalUnreadCount: (state) => {
      return Object.values(state.unreadCounts).reduce((total, count) => total + count, 0)
    }
  },

  actions: {
    async initialize() {
      try {
        // Check if the user is authenticated and has a token
        if (!isUserAuthenticated()) {
          return
        }

        // Set up event handlers first, so we don't miss any events
        // Use arrow functions to preserve 'this' context
        signalRService.onMessage((message) => this.handleNewMessage(message))
        signalRService.onNotification((notification) => this.handleNotification(notification))
        signalRService.onMessagesRead((readInfo) => this.handleMessagesRead(readInfo))
        signalRService.onConnectionStateChange(state => {
          this.connectionState = state

          // If reconnected, rejoin the current conversation
          if (state === 'connected' && this.currentConversationId) {
            signalRService.joinConversation(this.currentConversationId)
          }
        })

        // Load conversations (don't wait for SignalR connection)
        this.fetchConversations().catch(error => {
          console.error('Error fetching conversations:', error)
        })

        // Connect to SignalR (in the background)
        signalRService.start().catch(error => {
          console.error('Error starting SignalR connection:', error)
          this.error = 'Chat connection failed. Messages may not be delivered in real-time.'

          // Check if the error is due to authentication
          if (error.message && error.message.includes('authentication')) {
            signalRService.reconnectWithNewToken().catch(e => {
              console.error('Reconnect with new token failed:', e)
            })
          } else {
            // Try again after a delay
            setTimeout(() => {
              signalRService.start().catch(e => {
                console.error('Retry failed:', e)
              })
            }, 5000)
          }
        })
      } catch (error) {
        console.error('Error initializing chat store:', error)
        this.error = error.message
      }
    },

    async cleanup() {
      await signalRService.stop()
    },

    async fetchConversations() {
      this.isLoading = true
      this.error = null

      try {
        const response = await chatService.getConversations()
        this.conversations = response.data

        // Initialize unread counts
        this.conversations.forEach(conversation => {
          this.unreadCounts[conversation.id] = conversation.unreadCount
        })

        return this.conversations
      } catch (error) {
        this.error = error.message || 'Failed to fetch conversations'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async fetchConversation(conversationId) {
      this.isLoading = true
      this.error = null

      try {
        const response = await chatService.getConversation(conversationId)

        // Update or add the conversation
        const index = this.conversations.findIndex(c => c.id === conversationId)
        if (index >= 0) {
          this.conversations[index] = response.data
        } else {
          this.conversations.push(response.data)
        }

        return response.data
      } catch (error) {
        this.error = error.message || 'Failed to fetch conversation'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async fetchMessages(conversationId, lastMessageId = null, count = 20) {
      this.isLoading = true
      this.error = null

      try {
        const response = await chatService.getMessages(conversationId, lastMessageId, count)

        // Initialize the messages array if it doesn't exist
        if (!this.messages[conversationId]) {
          this.messages[conversationId] = []
        }

        // Add the messages to the store
        let newMessages = response.data

        // Get the current user ID to identify sent messages
        const authStore = useAuthStore()
        const currentUserId = authStore.user?.id

        // Process all messages
        const processedMessages = await Promise.all(
          newMessages.map(async (message) => {
            // Check if this is a message sent by the current user
            const isSentByCurrentUser = message.senderId === currentUserId

            if (isSentByCurrentUser) {
              // For messages sent by the current user, we need to store them with the decrypted content
              // but we can't decrypt them (since they were encrypted with recipient's public key)
              // Instead, we'll mark them for special handling
              message.isSenderMessage = true
            }

            // Decrypt the message if it wasn't sent by the current user
            let decryptedMessage = message
            if (!isSentByCurrentUser) {
              decryptedMessage = await this.decryptMessage(message)
            } else {
              // For sender's messages, try to use the content field if available
              // or mark it for special display
              if (message.content) {
                const selfEncryptedData = JSON.parse(message.content);

                const privateKey = authStore.privateKey;
                const decryptedAsymmetricMessage = await decryptMessage(selfEncryptedData, privateKey);

                decryptedMessage = {
                  ...message,
                  decryptedContent: decryptedAsymmetricMessage || '[Your encrypted message]'
                }
              }
              else {
                decryptedMessage = {
                  ...message,
                  decryptedContent: message.content || '[Your encrypted message]'
                }
              }
            }

            return decryptedMessage
          })
        )

        newMessages = processedMessages

        // Add the processed messages to the store
        if (lastMessageId) {
          // If we're loading older messages, prepend them
          // Create a map of existing message IDs for quick lookup
          const existingMessageIds = new Set(this.messages[conversationId].map(m => m.id))

          // Filter out any messages that already exist in the array
          const uniqueNewMessages = newMessages.filter(msg => !existingMessageIds.has(msg.id))

          // Add the new messages to the beginning of the array
          this.messages[conversationId] = [...uniqueNewMessages, ...this.messages[conversationId]]
        } else {
          // If we're loading the initial messages, replace the array
          this.messages[conversationId] = newMessages
        }

        // Sort messages by timestamp to ensure correct order
        this.messages[conversationId].sort((a, b) => {
          return new Date(a.timestamp) - new Date(b.timestamp);
        });

        return newMessages
      } catch (error) {
        this.error = error.message || 'Failed to fetch messages'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async sendMessage(conversationId, content) {
      this.isLoading = true
      this.error = null

      try {
        // Get the current conversation
        const conversation = this.conversations.find(c => c.id === conversationId)
        if (!conversation) {
          throw new Error('Conversation not found')
        }

        // Get the recipient's public key for encryption
        const authStore = useAuthStore()
        const currentUserId = authStore.user.id
        const otherParticipant = conversation.participants.find(p => p.userId !== currentUserId)

        if (!otherParticipant) {
          throw new Error('No recipient found in conversation')
        }

        // Try to get the recipient's public key
        let recipientPublicKey = null;

        try {
          // First try to get it from the server
          recipientPublicKey = await authStore.getPublicKey(otherParticipant.userId)
        } catch (error) {
          console.error('Failed to get recipient public key from server:', error)
        }

        // If still not found, try localStorage as a fallback
        if (!recipientPublicKey) {
          recipientPublicKey = localStorage.getItem(`publicKey_${otherParticipant.userId}`)
        }

        // If we still don't have a public key, use a default one
        if (!recipientPublicKey) {
          recipientPublicKey = 'default-key'
        }

        // Encrypt the message content with the recipient's public key
        let encryptedData
        let encryptedDataForSelf
        // Import the encryption function if needed
        const { encryptMessage } = await import('../services/encryption')

        // Make sure we have a valid public key
        if (!recipientPublicKey || recipientPublicKey === 'default-key') {
          // Try to get the public key one more time
          try {
            recipientPublicKey = await authStore.getPublicKey(otherParticipant.userId)

            // Store it for future use
            if (recipientPublicKey) {
              localStorage.setItem(`publicKey_${otherParticipant.userId}`, recipientPublicKey)
            } else {
              throw new Error('Could not retrieve recipient public key')
            }
          } catch (keyError) {
            console.error('Failed to get recipient public key:', keyError)
            return {
              encryptedText: btoa(content),
              iv: btoa(Math.random().toString(36).substring(2, 15)),
              encryptedKey: ''
            }
          }
        }

        try {
          // Encrypt the message with the recipient's public key
          encryptedData = await encryptMessage(content, recipientPublicKey)
          // Encrypt for self (so you can read your own messages)
          encryptedDataForSelf = await encryptMessage(content, authStore.publicKey);
        } catch (encryptError) {
          console.error('Encryption failed:', encryptError)
          // Use Base64 encoding as a temporary fallback
          console.warn('Using Base64 encoding as temporary fallback')
          return {
            encryptedText: btoa(content),
            iv: btoa(Math.random().toString(36).substring(2, 15)),
            encryptedKey: ''
          }
        }

        // Create the message object
        const message = {
          conversationId,
          encryptedContent: encryptedData.encryptedText,
          iv: encryptedData.iv,
          encryptedKey: encryptedData.encryptedKey || '',
          content: JSON.stringify({
            encryptedText: encryptedDataForSelf.encryptedText,
            iv: encryptedDataForSelf.iv,
            encryptedKey: encryptedDataForSelf.encryptedKey
          }),
          type: 0 // Text message
        }

        // Check if SignalR is connected
        if (this.connectionState !== 'connected') {
          // Try to reconnect
          signalRService.start().catch(error => {
            console.error('Failed to reconnect SignalR:', error)
          })
        }
        
        // Send the message
        const response = await chatService.sendMessage(message)

        // For sent messages, we already know the plaintext content
        // So we'll create a properly formatted message with the decrypted content
        const decryptedMessage = {
          ...response.data,
          encryptedContent: encryptedData.encryptedText,
          iv: encryptedData.iv,
          encryptedKey: encryptedData.encryptedKey || '',
          decryptedContent: content, // Store the original content directly
          isSenderMessage: true // Mark this as a sender's message
        }

        // Add the message to the store
        if (!this.messages[conversationId]) {
          this.messages[conversationId] = []
        }

        // Check if the message already exists to prevent duplicates
        const existingMessageIndex = this.messages[conversationId].findIndex(m => m.id === decryptedMessage.id)
        if (existingMessageIndex !== -1) {
          // Update the existing message
          this.messages[conversationId][existingMessageIndex] = decryptedMessage
        } else {
          // Add the new message
          this.messages[conversationId].push(decryptedMessage)
        }

        // Trigger reactivity
        this.messages[conversationId] = [...this.messages[conversationId]]

        // Update the conversation's last message
        const conversationIndex = this.conversations.findIndex(c => c.id === conversationId)
        if (conversationIndex >= 0) {
          this.conversations[conversationIndex].lastMessage = response.data
        }

        return response.data
      } catch (error) {
        console.error('Error sending message:', error)
        this.error = error.message || 'Failed to send message'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async markAsRead(conversationId, lastMessageId = null) {
      try {
        await chatService.markAsRead(conversationId, lastMessageId)

        // Update the unread count for this conversation
        this.unreadCounts[conversationId] = 0

        return true
      } catch (error) {
        console.error('Error marking messages as read:', error)
        return false
      }
    },

    async setCurrentConversation(conversationId) {
      // Store the previous conversation ID
      const previousConversationId = this.currentConversationId

      // Update the current conversation ID
      this.currentConversationId = conversationId

      // If we have a new conversation ID
      if (conversationId) {

        // Check if the user is authenticated and has a token
        if (!isUserAuthenticated()) {
          console.warn('User is not authenticated or has no token, cannot join conversation')
          return
        }

        // Join the SignalR group for this conversation
        const joined = await signalRService.joinConversation(conversationId)

        if (!joined) {
          // Try again after a delay
          setTimeout(async () => {
            if (this.currentConversationId === conversationId) {
              await signalRService.joinConversation(conversationId)
            }
          }, 2000)
        }

        // Load messages if they're not already loaded
        if (!this.messages[conversationId] || this.messages[conversationId].length === 0) {
          await this.fetchMessages(conversationId)
        }

        // Mark messages as read
        await this.markAsRead(conversationId)
      }

      // If we had a previous conversation, leave that group
      if (previousConversationId && previousConversationId !== conversationId) {
        await signalRService.leaveConversation(previousConversationId)
      }
    },

    async createConversation(matchUserId) {
      this.isLoading = true
      this.error = null

      try {
        // First, check if a conversation with this user already exists
        const existingConversation = this.conversations.find(c => {
          return c.participants && c.participants.some(p => p.userId === matchUserId)
        })

        if (existingConversation) {
          // Set as current conversation
          await this.setCurrentConversation(existingConversation.id)

          const recipientPublicKey = await authStore.getPublicKey(matchUserId)
          console.log('Got recipient public key from server:', recipientPublicKey)

          // Store the recipient's public key for future use
          localStorage.setItem(`publicKey_${matchUserId}`, recipientPublicKey)

          return existingConversation
        }

        // Get the current user's ID and auth store
        const authStore = useAuthStore()
        const currentUserId = authStore.user.id

        // Create the participant IDs array with both users
        const participantIds = [currentUserId, matchUserId]

        // Get the user's public key from the auth store
        let publicKey = authStore.publicKey

        // If we don't have a public key, generate a new key pair
        if (!publicKey) {
          const keyPair = await authStore.generateKeyPair()
          publicKey = keyPair.publicKey
        }

        // Try to get the recipient's public key from the server
        try {
          const recipientPublicKey = await authStore.getPublicKey(matchUserId)

          // Store the recipient's public key for future use
          localStorage.setItem(`publicKey_${matchUserId}`, recipientPublicKey)
        } catch (error) {
          console.error('Failed to get recipient public key:', error)
        }

        // Create the DTO with our public key and empty encrypted keys (backend requires this)
        const dto = {
          participantIds,
          publicKey,
          encryptedKeys: {}
        }

        const response = await chatService.createConversation(dto)

        // Add the new conversation to our list
        this.conversations.unshift(response.data)

        // Set as current conversation
        await this.setCurrentConversation(response.data.id)

        return response.data
      } catch (error) {
        this.error = error.message || 'Failed to create conversation'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Decrypt a message
    async decryptMessage(message) {
      try {
        // Get the conversation
        const conversation = this.conversations.find(c => c.id === message.conversationId)
        if (!conversation) {
          return { ...message, decryptedContent: message.encryptedContent || '[No content]' }
        }

        // Create a copy of the message
        const decryptedMessage = { ...message }

        // Try to decrypt the content
        try {
          // Get the auth store to access the user's private key
          const authStore = useAuthStore()

          // Try to get the private key from multiple sources
          let privateKey = null

          // 1. First check auth store
          if (authStore.privateKey) {
            privateKey = authStore.privateKey
            console.log('Using private key from auth store')
          }

          // 2. Then check user-specific localStorage
          if (!privateKey && authStore.user && authStore.user.id) {
            const userPrivateKey = localStorage.getItem(`privateKey`)
            if (userPrivateKey) {
              privateKey = userPrivateKey

              // Update auth store and general localStorage
              authStore.privateKey = privateKey
            }
          }

          // 4. If still not found, try to retrieve from the server
          if (!privateKey) {
            try {
              console.log('No private key found, attempting to retrieve from server')
              // Ask the user for their password to decrypt the private key
              const password = prompt('Please enter your password to decrypt your private key')

              if (password) {
                // Try to retrieve and decrypt the private key from the server
                privateKey = await authStore.retrievePrivateKey(password)
                console.log('Successfully retrieved private key from server')
              } else {
                console.warn('No password provided, generating a new key pair')
                // Generate a new key pair
                const { privateKey: newPrivateKey } = await authStore.generateKeyPair()
                privateKey = newPrivateKey
                console.log('Generated new key pair')
              }
            } catch (error) {
              console.error('Failed to retrieve private key:', error)
              throw new Error('Failed to retrieve private key: ' + error.message)
            }
          }

          // Import the decryption function
          const { decryptMessage } = await import('../services/encryption')

          // Check if we have encrypted content
          if (!message.encryptedContent) {
            decryptedMessage.decryptedContent = message.content || '[No content]'
            return decryptedMessage
          }

          // Check if the message is already decrypted
          if (message.decryptedContent) {
            return decryptedMessage
          }

          // Create the encrypted data object with content, IV, and encrypted key
          const encryptedData = {
            encryptedText: message.encryptedContent,
            iv: message.iv || '',
            encryptedKey: message.encryptedKey || ''
          }

          // Decrypt the message
          try {
            const decryptedContent = await decryptMessage(encryptedData, privateKey)
            decryptedMessage.decryptedContent = decryptedContent
          } catch (decryptError) {
            console.error('Error during decryption:', decryptError)

            // Check if this might be a Base64 encoded message (fallback encryption)
            if (message.encryptedContent &&
                (!message.encryptedKey || message.encryptedKey === '') &&
                /^[A-Za-z0-9+/=]+$/.test(message.encryptedContent)) {
              try {
                console.log('Attempting to decode as Base64 (fallback encryption)')
                const directDecoded = atob(message.encryptedContent)
                decryptedMessage.decryptedContent = directDecoded
              } catch (b64Error) {
                console.error('Base64 decoding failed:', b64Error)
                decryptedMessage.decryptedContent = '[Decryption failed]'
              }
            } else {
              decryptedMessage.decryptedContent = '[Decryption failed]'
            }
          }
        } catch (error) {
          console.error('Failed to decrypt message:', error)
          // If decryption fails, try to use the original content if available
          decryptedMessage.decryptedContent = message.content || message.encryptedContent || '[Encryption error]'
        }

        return decryptedMessage
      } catch (error) {
        return { ...message, decryptedContent: message.content || message.encryptedContent || '[Error]' }
      }
    },

    // SignalR event handlers
    async handleNewMessage(message) {
      try {
        const conversationId = message.conversationId

        // Check if we already have this message (to prevent duplicates)
        const existingMessageIndex = this.messages[conversationId] ?
          this.messages[conversationId].findIndex(m => m.id === message.id) : -1

        if (existingMessageIndex !== -1) {
          // We'll update it with the decrypted content below
        }

        // Make sure we have the encryptedKey property
        if (message.encryptedKey === undefined) {
          message.encryptedKey = ''
        }

        // Get the current user ID
        let currentUserId = null
        try {
          currentUserId = useAuthStore().user?.id
        } catch (error) {
          console.warn('Could not get current user ID:', error)
        }

        // Check if this is a message sent by the current user
        const isSentByCurrentUser = message.senderId === currentUserId || message.isSenderMessage

        let decryptedMessage;
        if (isSentByCurrentUser) {
          // For messages sent by the current user, we can't decrypt them
          // (since they were encrypted with recipient's public key)
          decryptedMessage = {
            ...message,
            isSenderMessage: true,
            // If we have content, use it, otherwise use a placeholder
            decryptedContent: message.content || '[Your encrypted message]'
          };
          console.log('Handling sender message:', decryptedMessage.decryptedContent);
        } else {
          // For messages from others, decrypt normally
          decryptedMessage = await this.decryptMessage(message);
          console.log('Message decrypted successfully:', decryptedMessage.decryptedContent);
        }

        // Add or update the message in the store
        if (!this.messages) {
          console.warn('Messages object is undefined, initializing it')
          this.messages = {}
        }

        if (this.messages[conversationId]) {
          if (existingMessageIndex !== -1) {
            this.messages[conversationId][existingMessageIndex] = decryptedMessage
          } else {
            this.messages[conversationId].push(decryptedMessage)
          }

          this.messages[conversationId] = [...this.messages[conversationId]]
        } else {
          this.messages[conversationId] = [decryptedMessage]
        }

        // Update the conversation's last message
        if (!this.conversations) {
          this.conversations = []
        }

        const conversationIndex = this.conversations.findIndex(c => c.id === conversationId)
        if (conversationIndex >= 0) {
          this.conversations[conversationIndex].lastMessage = message

          // Move this conversation to the top of the list
          const conversation = this.conversations[conversationIndex]
          this.conversations.splice(conversationIndex, 1)
          this.conversations.unshift(conversation)
        } else {
          // Try to fetch the conversation from the server
          try {
            this.fetchConversation(conversationId).catch(error => {
              console.error('Failed to fetch conversation:', error)
            })
          } catch (error) {
            console.error('Error fetching conversation:', error)
          }
        }

        // Update unread count if this is not the current conversation or the message is from someone else
        try {
          const userId = useAuthStore().user?.id
          if (
            message.senderId !== userId &&
            (this.currentConversationId !== conversationId)
          ) {
            this.unreadCounts[conversationId] = (this.unreadCounts[conversationId] || 0) + 1
          }
        } catch (updateError) {
          console.error('Error updating unread count:', updateError)
        }
      } catch (error) {
        console.error('Error handling new message:', error)
      }
    },

    handleNotification(notification) {
      if (notification.type === 'NewConversation') {
        // Add the new conversation to our list
        const exists = this.conversations.some(c => c.id === notification.conversation.id)
        if (!exists) {
          this.conversations.unshift(notification.conversation)
          this.unreadCounts[notification.conversation.id] = 0
        }
      }
    },

    handleMessagesRead(readInfo) {
      const { conversationId, userId, lastMessageId } = readInfo

      // Update read status for messages in this conversation
      if (this.messages[conversationId]) {
        this.messages[conversationId].forEach(message => {
          if (message.id <= lastMessageId && message.senderId !== userId) {
            message.isRead = true
            message.readAt = new Date()
          }
        })
      }
    },

    // Update messages with match ID after users match
    async updateMessagesWithMatchId(conversationId, matchId) {
      this.isLoading = true;
      this.error = null;

      try {
        // Call the API to update messages
        await chatService.updateMessagesWithMatchId(conversationId, matchId);

        // Update local messages in the store
        if (this.messages[conversationId]) {
          // Update all messages in this conversation with the match ID
          this.messages[conversationId] = this.messages[conversationId].map(msg => ({
            ...msg,
            matchId: matchId
          }));

          // Trigger reactivity
          this.messages[conversationId] = [...this.messages[conversationId]];
        }

        // Update the conversation in the store with the match ID
        const conversationIndex = this.conversations.findIndex(c => c.id === conversationId);
        if (conversationIndex >= 0) {
          this.conversations[conversationIndex] = {
            ...this.conversations[conversationIndex],
            matchId: matchId
          };
        }

        return true;
      } catch (error) {
        console.error('Error updating messages with match ID:', error);
        this.error = error.message || 'Failed to update messages with match ID';
        throw error;
      } finally {
        this.isLoading = false;
      }
    }
  }
})
