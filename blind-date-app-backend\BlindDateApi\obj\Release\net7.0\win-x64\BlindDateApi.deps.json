{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {}, ".NETCoreApp,Version=v7.0/win-x64": {"BlindDateApi/1.0.0": {"dependencies": {"AWSSDK.S3": "3.7.416.16", "BCrypt.Net-Next": "4.0.3", "BlindDateApi.Data": "1.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "7.0.14", "Microsoft.AspNetCore.OpenApi": "7.0.14", "Microsoft.EntityFrameworkCore.Design": "7.0.14", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.14", "Microsoft.EntityFrameworkCore.Tools": "7.0.14", "Microsoft.Extensions.Caching.StackExchangeRedis": "7.0.14", "Stripe.net": "48.0.2", "Swashbuckle.AspNetCore": "6.5.0", "System.IdentityModel.Tokens.Jwt": "7.0.3"}, "runtime": {"BlindDateApi.dll": {}}}, "AWSSDK.Core/3.7.402.46": {"runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.402.46"}}}, "AWSSDK.S3/3.7.416.16": {"dependencies": {"AWSSDK.Core": "3.7.402.46"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.416.16"}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Text.Json": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.700.22.46903"}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "Humanizer.Core/2.14.1": {}, "Microsoft.AspNetCore.Authentication.JwtBearer/7.0.14": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.AspNetCore.OpenApi/7.0.14": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Data.SqlClient/5.1.1": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"native": {"runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/7.0.14": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.14", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.14", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1423.52313"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.14": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1423.52313"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.14": {}, "Microsoft.EntityFrameworkCore.Design/7.0.14": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.14", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}}, "Microsoft.EntityFrameworkCore.Relational/7.0.14": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.14", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1423.52313"}}}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.14": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.14"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1423.52313"}}}, "Microsoft.EntityFrameworkCore.Tools/7.0.14": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.14"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Caching.StackExchangeRedis/7.0.14": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1", "StackExchange.Redis": "2.2.4"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Caching.StackExchangeRedis.dll": {"assemblyVersion": "********", "fileVersion": "7.0.1423.52316"}}}, "Microsoft.Extensions.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Physical": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "System.Text.Json": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "7.0.0", "Microsoft.Extensions.FileSystemGlobbing": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "Microsoft.Extensions.Options/7.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Primitives/7.0.0": {}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.47.2.0", "fileVersion": "4.47.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "7.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "7.0.3"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Pipelines.Sockets.Unofficial/2.2.0": {"dependencies": {"System.IO.Pipelines": "5.0.0"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.45337"}}}, "StackExchange.Redis/2.2.4": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.0", "System.Diagnostics.PerformanceCounter": "5.0.0"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.4.27433"}}}, "Stripe.net/48.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"lib/net7.0/Stripe.net.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/4.4.0": {}, "System.Configuration.ConfigurationManager/7.0.0": {"dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/7.0.0": {}, "System.Diagnostics.PerformanceCounter/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Formats.Asn1/5.0.0": {}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "System.IO.Pipelines/5.0.0": {}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"runtime": {"runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Permissions/7.0.0": {"dependencies": {"System.Windows.Extensions": "7.0.0"}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Windows.Extensions/7.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "BlindDateApi.Data/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.14", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.14", "Microsoft.Extensions.Configuration": "7.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "7.0.0", "Microsoft.Extensions.Configuration.Json": "7.0.0"}, "runtime": {"BlindDateApi.Data.dll": {}}}}}, "libraries": {"BlindDateApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.Core/3.7.402.46": {"type": "package", "serviceable": true, "sha512": "sha512-/W2Y/8PmNilP4qONoPJz5/AgHwvwblmE8wvf1nG+oqqd2fX88vUNRToHmdVxZUqjLtx64VryI7iGKRaPUDMkTA==", "path": "awssdk.core/3.7.402.46", "hashPath": "awssdk.core.3.7.402.46.nupkg.sha512"}, "AWSSDK.S3/3.7.416.16": {"type": "package", "serviceable": true, "sha512": "sha512-ny3iI0bZqh7sIr3TpiI73vmLN/9KlV7LNzJQ+Vxota6h2jGVtnelzQHh9HPBh5wI7KrFU9z4/gTR4xcicKjUuA==", "path": "awssdk.s3/3.7.416.16", "hashPath": "awssdk.s3.3.7.416.16.nupkg.sha512"}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-640Jjm2NnpQYHew0I2RyP2PjBKfB1ItSDYHX1TD+ooJov+b+ELPuo/GgrJCNJr3ZI1YmplmsQv88d8JRojZ+fA==", "path": "microsoft.aspnetcore.authentication.jwtbearer/7.0.14", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.7.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-GvrqOw9jyBSAUPQqEFmptKGwr7IH4Wvn+iDZKaEeccCmlXiBPRbTzdlqduc+teiMqnK/JJByg4FMwYHkPin1OA==", "path": "microsoft.aspnetcore.openapi/7.0.14", "hashPath": "microsoft.aspnetcore.openapi.7.0.14.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MW5E9HFvCaV069o8b6YpuRDPBux8s96qDnOJ+4N9QNUCs7c5W3KxwQ+ftpAjbMUlImL+c9WR+l+f5hzjkqhu2g==", "path": "microsoft.data.sqlclient/5.1.1", "hashPath": "microsoft.data.sqlclient.5.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-0KYkAemPygW6yzifciFlmMzkO4sI4Dw69xLgwg3ui5rXJS5XvzuAWVvfdrKJciqeCbCnVS/ZbOWpcwWgqce5bQ==", "path": "microsoft.entityframeworkcore/7.0.14", "hashPath": "microsoft.entityframeworkcore.7.0.14.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-aEcXDSYpDdD5wdIRKTqcS44f3W4capqQ1BWVRPJgacATfHkO62RX9Nnh0hUFg+rei9OLuJp0Y4zsy1fNeOXv5g==", "path": "microsoft.entityframeworkcore.abstractions/7.0.14", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.14.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-esI4RF6mix4DDFBhWB9k1vJxAL8GouSf5ZV8oFJoVsIQ9d2J3MPgC1VL2qM9Vw5cH7Vg7TzRyKNpCRXFVkWs9w==", "path": "microsoft.entityframeworkcore.analyzers/7.0.14", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.14.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-XpJgGQH4en21omeHncOobpGwB03oB6OIArl1a7AmhltUSdEqfhJ1aEqAQXkRdrFu2O8JaHQl10QCt4tHRok5Mw==", "path": "microsoft.entityframeworkcore.design/7.0.14", "hashPath": "microsoft.entityframeworkcore.design.7.0.14.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-MrVBnWOFYwfLMGQfrcIuqEM9Xvokv1vJeYxqNH3K3xOtAdHwHQTrKnpDP97tU+LBlvcnyXAtAtryYcpLXWtRNA==", "path": "microsoft.entityframeworkcore.relational/7.0.14", "hashPath": "microsoft.entityframeworkcore.relational.7.0.14.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-d9hqEw4W/TdQ1WDm03uyFuDoehL6GNq/NMChFaC4dcV60I42vKdUC0fYTuE2QPunVUpf5XUTCkJ6fYGjMos2AA==", "path": "microsoft.entityframeworkcore.sqlserver/7.0.14", "hashPath": "microsoft.entityframeworkcore.sqlserver.7.0.14.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-RjGysXi4k0yM7b2Znyasdhqu5FxOeuyZzo9g2w/ZhP87VsgFeoiykDPs69qIGTjjLbW0N5+srrl439ZQnMlHXw==", "path": "microsoft.entityframeworkcore.tools/7.0.14", "hashPath": "microsoft.entityframeworkcore.tools.7.0.14.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.StackExchangeRedis/7.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-gTYshp3AyOcJeN/EpjHt+2EO3k/3/Sa4NOOzHu/hlS2MGpCrO8E5wEILEESNOtuUsbl8wXfIYTGVhZ3GfkEbWA==", "path": "microsoft.extensions.caching.stackexchangeredis/7.0.14", "hashPath": "microsoft.extensions.caching.stackexchangeredis.7.0.14.nupkg.sha512"}, "Microsoft.Extensions.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tldQUBWt/xeH2K7/hMPPo5g8zuLc3Ro9I5d4o/XrxvxOCA2EZBtW7bCHHTc49fcBtvB8tLAb/Qsmfrq+2SJ4vA==", "path": "microsoft.extensions.configuration/7.0.0", "hashPath": "microsoft.extensions.configuration.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xk2lRJ1RDuqe57BmgvRPyCt6zyePKUmvT6iuXqiHR+/OIIgWVR8Ff5k2p6DwmqY8a17hx/OnrekEhziEIeQP6Q==", "path": "microsoft.extensions.configuration.fileextensions/7.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LDNYe3uw76W35Jci+be4LDf2lkQZe0A7EEYQVChFbc509CpZ4Iupod8li4PUXPBhEUOFI/rlQNf5xkzJRQGvtA==", "path": "microsoft.extensions.configuration.json/7.0.0", "hashPath": "microsoft.extensions.configuration.json.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K8D2MTR+EtzkbZ8z80LrG7Ur64R7ZZdRLt1J5cgpc/pUWl0C6IkAUapPuK28oionHueCPELUqq0oYEvZfalNdg==", "path": "microsoft.extensions.fileproviders.physical/7.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jONjKHiF+E92ynz2ZFcr9OvxIw+rTGMPEH+UZGeHTEComVav93jQUWGkso8yWwVBcEJGcNcZAaqY01FFJcj7w==", "path": "microsoft.extensions.filesystemglobbing/7.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pkeBFx0vqMW/A3aUVHh7MPu3WkBhaVlezhSZeb1c9XD0vUReYH1TLFSy5MxJgZfmz5LZzYoErMorlYZiwpOoNA==", "path": "microsoft.extensions.logging.abstractions/7.0.1", "hashPath": "microsoft.extensions.logging.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pZRDYdN1FpepOIfHU62QoBQ6zdAoTvnjxFfqAzEd9Jhb2dfhA5i6jeTdgGgcgTWFRC7oT0+3XrbQu4LjvgX1Nw==", "path": "microsoft.extensions.options/7.0.1", "hashPath": "microsoft.extensions.options.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cfPUWdjigLIRIJSKz3uaZxShgf86RVDXHC1VEEchj1gnY25akwPYpbrfSoIGDCqA9UmOMdlctq411+2pAViFow==", "path": "microsoft.identitymodel.abstractions/7.0.3", "hashPath": "microsoft.identitymodel.abstractions.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vxjHVZbMKD3rVdbvKhzAW+7UiFrYToUVm3AGmYfKSOAwyhdLl/ELX1KZr+FaLyyS5VReIzWRWJfbOuHM9i6ywg==", "path": "microsoft.identitymodel.jsonwebtokens/7.0.3", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-b6GbGO+2LOTBEccHhqoJsOsmemG4A/MY+8H0wK/ewRhiG+DCYwEnucog1cSArPIY55zcn+XdZl0YEiUHkpDISQ==", "path": "microsoft.identitymodel.logging/7.0.3", "hashPath": "microsoft.identitymodel.logging.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "path": "microsoft.identitymodel.protocols/6.24.0", "hashPath": "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-wB+LlbDjhnJ98DULjmFepqf9eEMh/sDs6S6hFh68iNRHmwollwhxk+nbSSfpA5+j+FbRyNskoaY4JsY1iCOKCg==", "path": "microsoft.identitymodel.tokens/7.0.3", "hashPath": "microsoft.identitymodel.tokens.7.0.3.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hzHplEIVOGBl5zOQZGX/DiJDHjq+RVRVrYgDiqXb6RriqWAdacXxp+XO9WSrATCEXyNOUOQg9aqQArsjase/A==", "path": "pipelines.sockets.unofficial/2.2.0", "hashPath": "pipelines.sockets.unofficial.2.2.0.nupkg.sha512"}, "StackExchange.Redis/2.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-wM0OuRyRaZTFndFRjIOvas4jjkeclRJsmNm0eAx5tOju3SQisrLubNaSFT/dBypi4Vh1n7nYc1gWpw9L7ernOg==", "path": "stackexchange.redis/2.2.4", "hashPath": "stackexchange.redis.2.2.4.nupkg.sha512"}, "Stripe.net/48.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ygn4kVkhNL9acuHTYZptbbjVetagBbvbrCt3uAkeeEJgYo62xaMHGb4ljvKpeyPmGyGsLnIOqH/z6s9eh+xZug==", "path": "stripe.net/48.0.2", "hashPath": "stripe.net.48.0.2.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "path": "system.configuration.configurationmanager/7.0.0", "hashPath": "system.configuration.configurationmanager.7.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kcQWWtGVC3MWMNXdMDWfrmIlFZZ2OdoeT6pSNVRtk9+Sa7jwdPiMlNwb0ZQcS7NRlT92pCfmjRtkSWUW3RAKwg==", "path": "system.diagnostics.performancecounter/5.0.0", "hashPath": "system.diagnostics.performancecounter.5.0.0.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-caEe+OpQNYNiyZb+DJpUVROXoVySWBahko2ooNfUcllxa9ZQUM8CgM/mDjP6AoFn6cQU9xMmG+jivXWub8cbGg==", "path": "system.identitymodel.tokens.jwt/7.0.3", "hashPath": "system.identitymodel.tokens.jwt.7.0.3.nupkg.sha512"}, "System.IO.Pipelines/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irMYm3vhVgRsYvHTU5b2gsT2CwT/SMM6LZFzuJjpIvT5Z4CshxNsaoBC1X/LltwuR3Opp8d6jOS/60WwOb7Q2Q==", "path": "system.io.pipelines/5.0.0", "hashPath": "system.io.pipelines.5.0.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "path": "system.security.cryptography.protecteddata/7.0.0", "hashPath": "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512"}, "System.Security.Permissions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "path": "system.security.permissions/7.0.0", "hashPath": "system.security.permissions.7.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Windows.Extensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "path": "system.windows.extensions/7.0.0", "hashPath": "system.windows.extensions.7.0.0.nupkg.sha512"}, "BlindDateApi.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}