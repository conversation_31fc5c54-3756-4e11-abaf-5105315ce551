using System;
using System.Security.Claims;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BlindDateApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class KeysController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly ILogger<KeysController> _logger;

        public KeysController(
            IUserRepository userRepository,
            ILogger<KeysController> logger)
        {
            _userRepository = userRepository;
            _logger = logger;
        }

        [HttpGet("public/{userId}")]
        public async Task<ActionResult<PublicKeyDto>> GetPublicKey(int userId)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                if (string.IsNullOrEmpty(user.PublicKey))
                {
                    return NotFound("User has no public key");
                }

                return Ok(new PublicKeyDto
                {
                    UserId = user.Id,
                    PublicKey = user.PublicKey
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting public key for user {UserId}", userId);
                return StatusCode(500, "An error occurred while retrieving the public key");
            }
        }

        [HttpPut("public")]
        public async Task<ActionResult> UpdatePublicKey(UpdatePublicKeyDto dto)
        {
            try
            {
                // Get the current user ID from the token
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int currentUserId))
                    return Unauthorized();

                var user = await _userRepository.GetByIdAsync(currentUserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                // Update the public key
                user.PublicKey = dto.PublicKey;
                _userRepository.Update(user);
                await _userRepository.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating public key for user");
                return StatusCode(500, "An error occurred while updating the public key");
            }
        }

        [HttpGet("private")]
        public async Task<ActionResult<EncryptedPrivateKeyDto>> GetEncryptedPrivateKey()
        {
            try
            {
                // Get the current user ID from the token
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int currentUserId))
                    return Unauthorized();

                var user = await _userRepository.GetByIdAsync(currentUserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                if (string.IsNullOrEmpty(user.EncryptedPrivateKey) ||
                    string.IsNullOrEmpty(user.PrivateKeySalt) ||
                    string.IsNullOrEmpty(user.PrivateKeyIV))
                {
                    return NotFound("User has no encrypted private key");
                }

                return Ok(new EncryptedPrivateKeyDto
                {
                    EncryptedPrivateKey = user.EncryptedPrivateKey,
                    Salt = user.PrivateKeySalt,
                    IV = user.PrivateKeyIV
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting encrypted private key for user");
                return StatusCode(500, "An error occurred while retrieving the encrypted private key");
            }
        }

        [HttpPut("private")]
        public async Task<ActionResult> UpdateEncryptedPrivateKey(UpdateEncryptedPrivateKeyDto dto)
        {
            try
            {
                // Get the current user ID from the token
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int currentUserId))
                    return Unauthorized();

                var user = await _userRepository.GetByIdAsync(currentUserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                // Update the encrypted private key and related data
                user.EncryptedPrivateKey = dto.EncryptedPrivateKey;
                user.PrivateKeySalt = dto.Salt;
                user.PrivateKeyIV = dto.IV;

                _userRepository.Update(user);
                await _userRepository.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating encrypted private key for user");
                return StatusCode(500, "An error occurred while updating the encrypted private key");
            }
        }

        [HttpGet("master")]
        public async Task<ActionResult<EncryptedPrivateKeyDto>> GetEncryptedMasterPrivateKey()
        {
            try
            {
                // Get the current user ID from the token
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int currentUserId))
                    return Unauthorized();

                var user = await _userRepository.GetByIdAsync(currentUserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                if (string.IsNullOrEmpty(user.EncryptedMasterPrivateKey) ||
                    string.IsNullOrEmpty(user.PrivateMasterKeySalt) ||
                    string.IsNullOrEmpty(user.PrivateMasterKeyIV))
                {
                    return NotFound("User has no encrypted private key");
                }

                return Ok(new EncryptedPrivateKeyDto
                {
                    Email = user.Email,
                    EncryptedPrivateKey = user.EncryptedMasterPrivateKey,
                    Salt = user.PrivateMasterKeySalt,
                    IV = user.PrivateMasterKeyIV
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting encrypted private key for user");
                return StatusCode(500, "An error occurred while retrieving the encrypted private key");
            }
        }

        [HttpPut("master")]
        public async Task<ActionResult> UpdateEncryptedMasterPrivateKey(UpdateEncryptedPrivateKeyDto dto)
        {
            try
            {
                // Get the current user ID from the token
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int currentUserId))
                    return Unauthorized();

                var user = await _userRepository.GetByIdAsync(currentUserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                // Update the encrypted private key and related data
                user.EncryptedMasterPrivateKey = dto.EncryptedPrivateKey;
                user.PrivateMasterKeySalt = dto.Salt;
                user.PrivateMasterKeyIV = dto.IV;

                _userRepository.Update(user);
                await _userRepository.SaveChangesAsync();

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating encrypted private key for user");
                return StatusCode(500, "An error occurred while updating the encrypted private key");
            }
        }

        [HttpGet("all")]
        public async Task<ActionResult<UserKeysDto>> GetAllKeys()
        {
            try
            {
                // Get the current user ID from the token
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int currentUserId))
                    return Unauthorized();

                var user = await _userRepository.GetByIdAsync(currentUserId);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                return Ok(new UserKeysDto
                {
                    PublicKey = user.PublicKey ?? string.Empty,
                    EncryptedPrivateKey = user.EncryptedPrivateKey,
                    Salt = user.PrivateKeySalt,
                    IV = user.PrivateKeyIV
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all keys for user");
                return StatusCode(500, "An error occurred while retrieving the keys");
            }
        }
    }
}
