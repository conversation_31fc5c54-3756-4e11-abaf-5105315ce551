namespace BlindDateApi.Services
{
    public interface IGeoLocationService
    {
        /// <summary>
        /// Calculate distance between two points using the Haversine formula
        /// </summary>
        /// <param name="lat1">Latitude of first point</param>
        /// <param name="lon1">Longitude of first point</param>
        /// <param name="lat2">Latitude of second point</param>
        /// <param name="lon2">Longitude of second point</param>
        /// <returns>Distance in kilometers</returns>
        double CalculateDistance(double lat1, double lon1, double lat2, double lon2);
        
        /// <summary>
        /// Check if a user is within the specified distance of another user
        /// </summary>
        /// <param name="userLat">User's latitude</param>
        /// <param name="userLon">User's longitude</param>
        /// <param name="otherLat">Other user's latitude</param>
        /// <param name="otherLon">Other user's longitude</param>
        /// <param name="maxDistance">Maximum distance in kilometers</param>
        /// <returns>True if within distance, false otherwise</returns>
        bool IsWithinDistance(double userLat, double userLon, double otherLat, double otherLon, int maxDistance);
    }
}
