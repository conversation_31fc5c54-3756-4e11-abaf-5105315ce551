using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using Stripe;

// Use alias to avoid ambiguity with Stripe.Subscription
using SubscriptionModel = BlindDateApi.Data.Models.Subscription;

namespace BlindDateApi.Services
{
    public class StripeService : IStripeService
    {
        private readonly IConfiguration _configuration;
        private readonly IRepository<SubscriptionModel> _subscriptionRepository;
        private readonly IRepository<UserSubscription> _userSubscriptionRepository;
        private readonly IUserRepository _userRepository;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<StripeService> _logger;

        private readonly string? _stripeSecretKey;
        private readonly string? _stripeWebhookSecret;

        public StripeService(
            IConfiguration configuration,
            IRepository<SubscriptionModel> subscriptionRepository,
            IRepository<UserSubscription> userSubscriptionRepository,
            IUserRepository userRepository,
            ApplicationDbContext context,
            ILogger<StripeService> logger)
        {
            _configuration = configuration;
            _subscriptionRepository = subscriptionRepository;
            _userSubscriptionRepository = userSubscriptionRepository;
            _userRepository = userRepository;
            _context = context;
            _logger = logger;

            _stripeSecretKey = _configuration["Stripe:SecretKey"];
            _stripeWebhookSecret = _configuration["Stripe:WebhookSecret"];

            // Configure Stripe API key
            if (!string.IsNullOrEmpty(_stripeSecretKey))
            {
                StripeConfiguration.ApiKey = _stripeSecretKey;
            }
            else
            {
                _logger.LogWarning("Stripe Secret Key is not configured. Stripe functionality will not work properly.");
            }
        }

        public async Task<string> CreatePaymentIntentAsync(int subscriptionId, int userId)
        {
            try
            {
                // Get subscription
                var subscription = await _subscriptionRepository.GetByIdAsync(subscriptionId);
                if (subscription == null)
                {
                    throw new ArgumentException($"Subscription with ID {subscriptionId} not found");
                }

                // Get user
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException($"User with ID {userId} not found");
                }

                // Get or create Stripe customer
                var customerId = await GetOrCreateCustomerIdAsync(userId, user.Email);

                // Create payment intent
                var options = new PaymentIntentCreateOptions
                {
                    Amount = (long)(subscription.Price * 100), // Amount in cents
                    Currency = "eur",
                    Customer = customerId,
                    Metadata = new Dictionary<string, string>
                    {
                        { "userId", userId.ToString() },
                        { "subscriptionId", subscriptionId.ToString() }
                    },
                    PaymentMethodTypes = new List<string> { "card" }
                };

                var service = new PaymentIntentService();
                var paymentIntent = await service.CreateAsync(options);

                return paymentIntent.ClientSecret;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment intent for user {UserId} and subscription {SubscriptionId}", userId, subscriptionId);
                throw;
            }
        }

        public async Task<UserSubscriptionDto> ConfirmPaymentAsync(string paymentIntentId, int userId)
        {
            try
            {
                // Get payment intent
                var service = new PaymentIntentService();
                var paymentIntent = await service.GetAsync(paymentIntentId);

                if (paymentIntent.Status != "succeeded")
                {
                    throw new InvalidOperationException($"Payment intent {paymentIntentId} has not succeeded. Status: {paymentIntent.Status}");
                }

                // Extract metadata
                if (!paymentIntent.Metadata.TryGetValue("subscriptionId", out var subscriptionIdStr) ||
                    !int.TryParse(subscriptionIdStr, out var subscriptionId))
                {
                    throw new InvalidOperationException("Invalid subscription ID in payment intent metadata");
                }

                if (!paymentIntent.Metadata.TryGetValue("userId", out var paymentUserIdStr) ||
                    !int.TryParse(paymentUserIdStr, out var paymentUserId) ||
                    paymentUserId != userId)
                {
                    throw new InvalidOperationException("Invalid user ID in payment intent metadata");
                }

                // Get subscription
                var subscription = await _subscriptionRepository.GetByIdAsync(subscriptionId);
                if (subscription == null)
                {
                    throw new ArgumentException($"Subscription with ID {subscriptionId} not found");
                }

                // Get user with subscriptions
                var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException($"User with ID {userId} not found");
                }

                // Check if user already has this subscription
                var existingSubscription = user.Subscriptions
                    .FirstOrDefault(us => us.SubscriptionId == subscriptionId && us.IsActive);

                if (existingSubscription != null)
                {
                    // Extend existing subscription
                    existingSubscription.ExpiresAt = existingSubscription.ExpiresAt.AddMonths(1);
                    await _context.SaveChangesAsync();

                    return new UserSubscriptionDto
                    {
                        Id = existingSubscription.Id,
                        UserId = existingSubscription.UserId,
                        SubscriptionId = existingSubscription.SubscriptionId,
                        SubscriptionName = subscription.Name,
                        StartDate = existingSubscription.StartDate,
                        ExpiresAt = existingSubscription.ExpiresAt,
                        IsActive = existingSubscription.IsActive,
                        AmountPaid = subscription.Price
                    };
                }

                // Create new subscription
                var userSubscription = new UserSubscription
                {
                    UserId = userId,
                    SubscriptionId = subscription.Id,
                    StartDate = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMonths(1),
                    TransactionId = paymentIntent.Id,
                    AmountPaid = subscription.Price
                };

                _context.UserSubscriptions.Add(userSubscription);
                await _context.SaveChangesAsync();

                return new UserSubscriptionDto
                {
                    Id = userSubscription.Id,
                    UserId = userSubscription.UserId,
                    SubscriptionId = userSubscription.SubscriptionId,
                    SubscriptionName = subscription.Name,
                    StartDate = userSubscription.StartDate,
                    ExpiresAt = userSubscription.ExpiresAt,
                    IsActive = userSubscription.IsActive,
                    AmountPaid = userSubscription.AmountPaid
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming payment {PaymentIntentId} for user {UserId}", paymentIntentId, userId);
                throw;
            }
        }

        public async Task<bool> CancelSubscriptionAsync(int userId)
        {
            try
            {
                // Get user with subscriptions
                var user = await _userRepository.GetUserWithSubscriptionsAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException($"User with ID {userId} not found");
                }

                // Get active subscriptions (excluding Free plan)
                var activeSubscriptions = user.Subscriptions
                    .Where(us => us.IsActive && us.SubscriptionId != 1)
                    .ToList();

                if (!activeSubscriptions.Any())
                {
                    // No active paid subscriptions to cancel
                    return false;
                }

                // Cancel all active paid subscriptions by setting expiry to now
                foreach (var subscription in activeSubscriptions)
                {
                    subscription.ExpiresAt = DateTime.UtcNow;
                }

                // Create a new Free plan subscription
                var freePlan = await _subscriptionRepository.GetByIdAsync(1); // Free plan ID is 1
                if (freePlan == null)
                {
                    throw new InvalidOperationException("Free plan not found");
                }

                var freeSubscription = new UserSubscription
                {
                    UserId = userId,
                    SubscriptionId = 1, // Free plan
                    StartDate = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddYears(100), // Very long expiry for free plan
                    TransactionId = null,
                    AmountPaid = 0
                };

                _context.UserSubscriptions.Add(freeSubscription);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling subscription for user {UserId}", userId);
                throw;
            }
        }

        public async Task<string> CreateCustomerAsync(int userId, string email)
        {
            try
            {
                var options = new CustomerCreateOptions
                {
                    Email = email,
                    Metadata = new Dictionary<string, string>
                    {
                        { "userId", userId.ToString() }
                    }
                };

                var service = new CustomerService();
                var customer = await service.CreateAsync(options);

                // Store the customer ID in the user record
                var user = await _userRepository.GetByIdAsync(userId);
                if (user != null)
                {
                    user.StripeCustomerId = customer.Id;
                    await _context.SaveChangesAsync();
                }

                return customer.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Stripe customer for user {UserId}", userId);
                throw;
            }
        }

        public async Task<Customer> GetCustomerAsync(int userId)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null)
                {
                    throw new ArgumentException($"User with ID {userId} not found");
                }

                if (string.IsNullOrEmpty(user.StripeCustomerId))
                {
                    return null;
                }

                var service = new CustomerService();
                return await service.GetAsync(user.StripeCustomerId);
            }
            catch (StripeException ex) when (ex.StripeError.Code == "resource_missing")
            {
                // Customer not found in Stripe
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Stripe customer for user {UserId}", userId);
                throw;
            }
        }

        private async Task<string> GetOrCreateCustomerIdAsync(int userId, string email)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                throw new ArgumentException($"User with ID {userId} not found");
            }

            if (!string.IsNullOrEmpty(user.StripeCustomerId))
            {
                try
                {
                    // Verify the customer exists in Stripe
                    var service = new CustomerService();
                    await service.GetAsync(user.StripeCustomerId);
                    return user.StripeCustomerId;
                }
                catch (StripeException ex) when (ex.StripeError.Code == "resource_missing")
                {
                    // Customer not found in Stripe, create a new one
                    return await CreateCustomerAsync(userId, email);
                }
            }

            // No customer ID, create a new one
            return await CreateCustomerAsync(userId, email);
        }
    }
}
