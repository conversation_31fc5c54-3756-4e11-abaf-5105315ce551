﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BlindDateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddUserPublicKeyField : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON><PERSON>",
                table: "Users",
                type: "nvarchar(2048)",
                maxLength: 2048,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>K<PERSON>",
                table: "Users");
        }
    }
}
