{"name": "blind-date-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@mdi/font": "^7.4.47", "@microsoft/signalr": "^7.0.14", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "axios": "^1.8.4", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuetify": "^3.8.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.2.0"}}