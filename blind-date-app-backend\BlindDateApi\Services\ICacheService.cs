using System;
using System.Threading.Tasks;

namespace BlindDateApi.Services
{
    public interface ICacheService
    {
        /// <summary>
        /// Gets a cached item by key
        /// </summary>
        /// <typeparam name="T">Type of cached item</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached item or default value if not found</returns>
        T Get<T>(string key);
        
        /// <summary>
        /// Gets a cached item by key asynchronously
        /// </summary>
        /// <typeparam name="T">Type of cached item</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached item or default value if not found</returns>
        Task<T> GetAsync<T>(string key);
        
        /// <summary>
        /// Sets a cached item with the specified key
        /// </summary>
        /// <typeparam name="T">Type of item</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Item to cache</param>
        /// <param name="absoluteExpiration">Absolute expiration time</param>
        void Set<T>(string key, T value, TimeSpan? absoluteExpiration = null);
        
        /// <summary>
        /// Sets a cached item with the specified key asynchronously
        /// </summary>
        /// <typeparam name="T">Type of item</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Item to cache</param>
        /// <param name="absoluteExpiration">Absolute expiration time</param>
        Task SetAsync<T>(string key, T value, TimeSpan? absoluteExpiration = null);
        
        /// <summary>
        /// Removes a cached item by key
        /// </summary>
        /// <param name="key">Cache key</param>
        void Remove(string key);
        
        /// <summary>
        /// Removes a cached item by key asynchronously
        /// </summary>
        /// <param name="key">Cache key</param>
        Task RemoveAsync(string key);
    }
}
