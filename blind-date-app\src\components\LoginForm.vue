<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import axios from 'axios'
import { authService } from '../services/api'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')
const errorMessage = ref('')
const isLoading = ref(false)
const showPassword = ref(false)

const handleLogin = async () => {
  // Clear any previous error
  errorMessage.value = ''

  // Validate inputs
  if (!email.value || !password.value) {
    errorMessage.value = 'Please enter both email and password'
    return
  }

  try {
    isLoading.value = true

    // Direct API call instead of using the store
    const response = await axios.post('http://localhost:5192/api/auth/login', {
      email: email.value,
      password: password.value
    })

    const userData = response.data
    const user = {
      id: userData.userId,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      token: userData.token,
      publicKey: userData.publicKey,
      privateKey: userData.privateKey,
      tokenExpiration: userData.tokenExpiration,
      subscription: userData.subscription,
      profileCompleted: true
    }

    // Update auth store manually
    authStore.user = user
    authStore.isAuthenticated = true
    localStorage.setItem('user', JSON.stringify(user))

    if (user.publicKey) {
      authStore.publicKey = user.publicKey
      localStorage.setItem('publicKey', userData.publicKey)

      console.log(authStore.passwordResetStatus)

      if (authStore.passwordResetStatus == 'reset-success') {
        await authStore.retrieveMasterPrivateKey()
        await authStore.storeEncryptedPrivateKey(null, password)

        authStore.passwordResetStatus = null
      }
    } else {
      // Generate keys if not provided
      await authStore.generateKeyPair()
    }

    if (userData.privateKey) {
      authStore.privateKey = userData.privateKey
      localStorage.setItem('privateKey', userData.privateKey)

      // Store the encrypted private key on the server using the login password
      if (password) {
        authStore.storeEncryptedPrivateKey(email.value, password).catch(error => {
          console.error('Failed to store encrypted private key on server:', error)
              // This is not critical, so we'll just log the error
          })
        }
      } else {
        // Try to retrieve the encrypted private key from the server
        if (password) {
            authStore.retrievePrivateKey(password).catch(error => {
              console.error('Failed to retrieve private key from server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        }

    // Navigate based on profile completion
    if (user.profileCompleted) {
      router.push('/home')
    } else {
      router.push('/profile-setup')
    }
  } catch (error) {
    console.error('Login error:', error)

    // Handle validation errors from the API
    if (error.response?.data?.errors) {
      // Extract validation error messages
      const errorMessages = []
      const errors = error.response.data.errors

      // Loop through all error fields
      for (const field in errors) {
        if (Array.isArray(errors[field])) {
          // Add each error message for this field
          errors[field].forEach(message => {
            errorMessages.push(message)
          })
        }
      }

      // Join all error messages
      errorMessage.value = errorMessages.join('\n')
    } else {
      // Handle other types of errors
      errorMessage.value = error.response?.data || error.message || 'Invalid email or password'
    }
  } finally {
    isLoading.value = false
  }
}

const loginWithSocial = async (provider) => {
  try {
    console.log(`Social login with ${provider} called`)
    isLoading.value = true
    errorMessage.value = ''

    // Call the auth store method to handle social login
    await authStore.loginWithSocial(provider)
    console.log(`Social login with ${provider} completed`)

  } catch (error) {
    console.error(`Social login error:`, error)
    errorMessage.value = error.message || `Failed to login with ${provider}`
  } finally {
    isLoading.value = false
  }
}

const navigateToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <div class="login-form">
    <!-- Hidden form to prevent browser auto-submission -->
    <form style="display: none" autocomplete="off" onsubmit="return false;"></form>
    <v-card class="login-card" elevation="8">
      <v-card-title class="text-center login-title font-weight-bold">Welcome Back</v-card-title>
      <v-card-subtitle class="text-center login-subtitle">Sign in to your account</v-card-subtitle>

      <div class="form-content">
        <!-- Email Field -->
        <v-text-field
          v-model="email"
          label="Email"
          type="email"
          required
          variant="outlined"
          :disabled="isLoading"
          class="form-field"
        ></v-text-field>

        <!-- Password Field -->
        <v-text-field
          v-model="password"
          label="Password"
          :type="showPassword ? 'text' : 'password'"
          required
          variant="outlined"
          :disabled="isLoading"
          :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
          @click:append-inner="showPassword = !showPassword"
          class="form-field"
        ></v-text-field>

        <!-- Error Alert -->
        <v-alert
          v-if="errorMessage"
          type="error"
          variant="tonal"
          class="error-alert"
          border="start"
          closable
          @click:close="errorMessage = ''"
        >
          <div v-if="errorMessage.includes('\n')">
            <strong>Please fix the following errors:</strong>
            <ul class="mt-2 mb-0">
              <li v-for="(line, index) in errorMessage.split('\n')" :key="index">
                {{ line }}
              </li>
            </ul>
          </div>
          <div v-else>
            <strong>Error:</strong> {{ errorMessage }}
          </div>
        </v-alert>

        <!-- Login Button -->
        <v-btn
          type="button"
          block
          color="primary"
          size="large"
          :loading="isLoading"
          class="login-btn"
          @mousedown.prevent="handleLogin"
          @touchstart.prevent="handleLogin"
        >
          Sign in
        </v-btn>

        <!-- Forgot Password Link -->
        <div class="text-center forgot-password-section">
          <v-btn
            variant="text"
            color="primary"
            to="/forgot-password"
            :disabled="isLoading"
            class="forgot-password-btn"
          >
            Forgot Password?
          </v-btn>
        </div>
      </div>

      <v-divider class="divider"></v-divider>

      <div class="text-center social-login-header">
        <span class="social-login-text">Or continue with</span>
      </div>

      <!-- Social Login Buttons - Responsive Layout -->
      <div class="social-buttons-container">
        <v-row class="d-none d-sm-flex">
          <v-col cols="6">
            <v-btn
              block
              variant="outlined"
              :disabled="isLoading"
              @click="loginWithSocial('google')"
              class="social-btn"
            >
              <v-icon start>mdi-google</v-icon>
              Google
            </v-btn>
          </v-col>

          <v-col cols="6">
            <v-btn
              block
              variant="outlined"
              :disabled="isLoading"
              @click="loginWithSocial('facebook')"
              class="social-btn"
            >
              <v-icon start>mdi-facebook</v-icon>
              Facebook
            </v-btn>
          </v-col>
        </v-row>

        <!-- Mobile: Stacked Social Buttons -->
        <div class="d-flex d-sm-none flex-column">
          <v-btn
            block
            variant="outlined"
            :disabled="isLoading"
            @click="loginWithSocial('google')"
            class="social-btn-mobile mb-3"
          >
            <v-icon start>mdi-google</v-icon>
            Google
          </v-btn>

          <v-btn
            block
            variant="outlined"
            :disabled="isLoading"
            @click="loginWithSocial('facebook')"
            class="social-btn-mobile"
          >
            <v-icon start>mdi-facebook</v-icon>
            Facebook
          </v-btn>
        </div>
      </div>

      <v-card-text class="signup-section">
        <div class="signup-content">
          <span class="signup-text">Don't have an account?</span>
          <v-btn
            variant="text"
            color="primary"
            @click="navigateToRegister"
            class="signup-btn"
          >
            SIGN UP
          </v-btn>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>
