<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import axios from 'axios'
import { authService } from '../services/api'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')
const errorMessage = ref('')
const isLoading = ref(false)
const showPassword = ref(false)

const handleLogin = async () => {
  // Clear any previous error
  errorMessage.value = ''

  // Validate inputs
  if (!email.value || !password.value) {
    errorMessage.value = 'Please enter both email and password'
    return
  }

  try {
    isLoading.value = true

    // Direct API call instead of using the store
    const response = await axios.post('http://localhost:5192/api/auth/login', {
      email: email.value,
      password: password.value
    })

    const userData = response.data
    const user = {
      id: userData.userId,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      token: userData.token,
      publicKey: userData.publicKey,
      privateKey: userData.privateKey,
      tokenExpiration: userData.tokenExpiration,
      subscription: userData.subscription,
      profileCompleted: true
    }

    // Update auth store manually
    authStore.user = user
    authStore.isAuthenticated = true
    localStorage.setItem('user', JSON.stringify(user))

    if (user.publicKey) {
      authStore.publicKey = user.publicKey
      localStorage.setItem('publicKey', userData.publicKey)

      console.log(authStore.passwordResetStatus)

      if (authStore.passwordResetStatus == 'reset-success') {
        await authStore.retrieveMasterPrivateKey()
        await authStore.storeEncryptedPrivateKey(null, password)

        authStore.passwordResetStatus = null
      }
    } else {
      // Generate keys if not provided
      await authStore.generateKeyPair()
    }

    if (userData.privateKey) {
      authStore.privateKey = userData.privateKey
      localStorage.setItem('privateKey', userData.privateKey)

      // Store the encrypted private key on the server using the login password
      if (password) {
        authStore.storeEncryptedPrivateKey(email.value, password).catch(error => {
          console.error('Failed to store encrypted private key on server:', error)
              // This is not critical, so we'll just log the error
          })
        }
      } else {
        // Try to retrieve the encrypted private key from the server
        if (password) {
            authStore.retrievePrivateKey(password).catch(error => {
              console.error('Failed to retrieve private key from server:', error)
              // This is not critical, so we'll just log the error
            })
          }
        }

    // Navigate based on profile completion
    if (user.profileCompleted) {
      router.push('/home')
    } else {
      router.push('/profile-setup')
    }
  } catch (error) {
    console.error('Login error:', error)

    // Handle validation errors from the API
    if (error.response?.data?.errors) {
      // Extract validation error messages
      const errorMessages = []
      const errors = error.response.data.errors

      // Loop through all error fields
      for (const field in errors) {
        if (Array.isArray(errors[field])) {
          // Add each error message for this field
          errors[field].forEach(message => {
            errorMessages.push(message)
          })
        }
      }

      // Join all error messages
      errorMessage.value = errorMessages.join('\n')
    } else {
      // Handle other types of errors
      errorMessage.value = error.response?.data || error.message || 'Invalid email or password'
    }
  } finally {
    isLoading.value = false
  }
}

const loginWithSocial = async (provider) => {
  try {
    console.log(`Social login with ${provider} called`)
    isLoading.value = true
    errorMessage.value = ''

    // Call the auth store method to handle social login
    await authStore.loginWithSocial(provider)
    console.log(`Social login with ${provider} completed`)

  } catch (error) {
    console.error(`Social login error:`, error)
    errorMessage.value = error.message || `Failed to login with ${provider}`
  } finally {
    isLoading.value = false
  }
}

const navigateToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <div class="login-form">
    <!-- Hidden form to prevent browser auto-submission -->
    <form style="display: none" autocomplete="off" onsubmit="return false;"></form>
    <v-card class="pa-6" elevation="8">
      <v-card-title class="text-center text-h4 font-weight-bold">Welcome Back</v-card-title>
      <v-card-subtitle class="text-center">Sign in to your account</v-card-subtitle>

      <div class="mt-4">
        <!-- Email Field -->
        <v-text-field
          v-model="email"
          label="Email"
          type="email"
          required
          variant="outlined"
          :disabled="isLoading"
          class="mb-4"
        ></v-text-field>

        <!-- Password Field -->
        <v-text-field
          v-model="password"
          label="Password"
          :type="showPassword ? 'text' : 'password'"
          required
          variant="outlined"
          :disabled="isLoading"
          :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
          @click:append-inner="showPassword = !showPassword"
          class="mb-6"
        ></v-text-field>

        <!-- Error Alert -->
        <v-alert
          v-if="errorMessage"
          type="error"
          variant="tonal"
          class="mb-4"
          border="start"
          closable
          @click:close="errorMessage = ''"
        >
          <div v-if="errorMessage.includes('\n')">
            <strong>Please fix the following errors:</strong>
            <ul class="mt-2 mb-0">
              <li v-for="(line, index) in errorMessage.split('\n')" :key="index">
                {{ line }}
              </li>
            </ul>
          </div>
          <div v-else>
            <strong>Error:</strong> {{ errorMessage }}
          </div>
        </v-alert>

        <!-- Login Button -->
        <v-btn
          type="button"
          block
          color="primary"
          size="large"
          :loading="isLoading"
          class="mt-2"
          @mousedown.prevent="handleLogin"
          @touchstart.prevent="handleLogin"
        >
          Sign in
        </v-btn>

        <!-- Forgot Password Link -->
        <div class="text-center mt-3">
          <v-btn
            variant="text"
            color="primary"
            to="/forgot-password"
            :disabled="isLoading"
            class="text-body-2"
          >
            Forgot Password?
          </v-btn>
        </div>
      </div>

      <v-divider class="my-4"></v-divider>

      <div class="text-center mb-4">
        <span class="text-body-2">Or continue with</span>
      </div>

      <v-row>
        <v-col cols="6">
          <v-btn
            block
            variant="outlined"
            :disabled="isLoading"
            @click="loginWithSocial('google')"
          >
            <v-icon start>mdi-google</v-icon>
            Google
          </v-btn>
        </v-col>

        <v-col cols="6">
          <v-btn
            block
            variant="outlined"
            :disabled="isLoading"
            @click="loginWithSocial('facebook')"
          >
            <v-icon start>mdi-facebook</v-icon>
            Facebook
          </v-btn>
        </v-col>
      </v-row>

      <v-card-text class="text-center d-flex align-center justify-center">
        <span class="text-body-1 mr-2">Don't have an account?</span>
        <v-btn
          variant="text"
          color="primary"
          @click="navigateToRegister"
          class="text-body-1 font-weight-medium pa-1"
          style="margin-top: -1px;"
        >
          SIGN UP
        </v-btn>
      </v-card-text>
    </v-card>
  </div>
</template>
