using BlindDateApi.Data.Context;
using BlindDateApi.Data.Repositories;
using BlindDateApi.Services;

namespace BlindDateApi.Middleware
{
    public class JwtMiddleware
    {
        private readonly RequestDelegate _next;

        public JwtMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context, IUserRepository userRepository, IJwtService jwtService)
        {
            var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

            if (token != null)
            {
                var userId = jwtService.ValidateToken(token);
                if (userId != null)
                {
                    // Attach user to context on successful JWT validation
                    var user = await userRepository.GetUserWithProfileAsync(userId.Value);
                    if (user != null)
                    {
                        // Load subscriptions
                        var userWithSubscriptions = await userRepository.GetUserWithSubscriptionsAsync(userId.Value);
                        user.Subscriptions = userWithSubscriptions.Subscriptions;
                        context.Items["User"] = user;
                    }
                }
            }

            await _next(context);
        }
    }

    // Extension method to add the middleware to the HTTP request pipeline
    public static class JwtMiddlewareExtensions
    {
        public static IApplicationBuilder UseJwtMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<JwtMiddleware>();
        }
    }
}
