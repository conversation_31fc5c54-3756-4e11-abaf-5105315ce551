using System;
using System.Text.RegularExpressions;

namespace BlindDateApi.Services
{
    public interface IPasswordValidationService
    {
        bool IsValid(string password);
        string GetValidationErrorMessage();
    }

    public class PasswordValidationService : IPasswordValidationService
    {
        private const string LowercasePattern = "[a-z]";
        private const string UppercasePattern = "[A-Z]";
        private const string DigitPattern = "[0-9]";
        private const string SpecialCharPattern = "[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]";
        private const int MinLength = 8;

        public bool IsValid(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            if (password.Length < MinLength)
                return false;

            if (!Regex.IsMatch(password, LowercasePattern))
                return false;

            if (!Regex.IsMatch(password, UppercasePattern))
                return false;

            if (!Regex.IsMatch(password, DigitPattern))
                return false;

            if (!Regex.IsMatch(password, SpecialCharPattern))
                return false;

            return true;
        }

        public string GetValidationErrorMessage()
        {
            return $"Password must be at least {MinLength} characters long and contain at least one lowercase letter, one uppercase letter, one digit, and one special character.";
        }
    }
}
