using System.ComponentModel.DataAnnotations;

namespace BlindDateApi.DTOs
{
    public class SocialLoginDto
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        [Required]
        public string Password { get; set; }

        [Required]
        public string Provider { get; set; } // "google", "facebook", etc.

        public string ProviderId { get; set; } // ID from the provider
    }
}
