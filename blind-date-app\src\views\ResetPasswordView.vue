<template>
  <v-container class="fill-height">
    <v-row justify="center" align="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="pa-6" elevation="8">
          <v-card-title class="text-center text-h5 font-weight-bold mb-4">
            Reset Password
          </v-card-title>
          
          <v-card-text>
            <div v-if="passwordResetStatus === 'reset-success'">
              <v-alert
                type="success"
                variant="tonal"
                class="mb-4"
              >
                Your password has been reset successfully.
              </v-alert>
              
              <v-btn
                color="primary"
                block
                to="/login"
                class="mt-4"
              >
                Go to Login
              </v-btn>
            </div>
            
            <v-form v-else @submit.prevent="submitResetPassword" ref="form">
              <p class="mb-4">
                Enter your new password below.
              </p>
              
              <v-text-field
                v-model="password"
                :rules="passwordRules"
                label="New Password"
                type="password"
                required
                variant="outlined"
                :disabled="isLoading"
              ></v-text-field>
              
              <v-text-field
                v-model="confirmPassword"
                :rules="confirmPasswordRules"
                label="Confirm New Password"
                type="password"
                required
                variant="outlined"
                :disabled="isLoading"
              ></v-text-field>
              
              <v-alert
                v-if="errorMessage"
                type="error"
                variant="tonal"
                class="mb-4"
              >
                {{ errorMessage }}
              </v-alert>
              
              <v-btn
                type="submit"
                color="primary"
                block
                :loading="isLoading"
                :disabled="isLoading || !token"
                class="mt-4"
              >
                Reset Password
              </v-btn>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { useRoute, useRouter } from 'vue-router'

const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()
const form = ref(null)

const token = ref('')
const password = ref('')
const confirmPassword = ref('')
const errorMessage = ref('')
const isLoading = computed(() => authStore.isLoading)
const passwordResetStatus = computed(() => authStore.passwordResetStatus)

// Password validation rules
const passwordRules = [
  v => !!v || 'Password is required',
  v => v.length >= 8 || 'Password must be at least 8 characters',
  v => /[A-Z]/.test(v) || 'Password must contain at least one uppercase letter',
  v => /[a-z]/.test(v) || 'Password must contain at least one lowercase letter',
  v => /[0-9]/.test(v) || 'Password must contain at least one number',
  v => /[^A-Za-z0-9]/.test(v) || 'Password must contain at least one special character'
]

// Confirm password validation rules
const confirmPasswordRules = [
  v => !!v || 'Please confirm your password',
  v => v === password.value || 'Passwords do not match'
]

// Submit reset password request
const submitResetPassword = async () => {
  // Validate the form
  const { valid } = await form.value.validate()
  if (!valid) return
  
  errorMessage.value = ''
  
  try {
    // Call the auth store method to reset password
    await authStore.resetPassword(token.value, password.value, confirmPassword.value)
  } catch (error) {
    errorMessage.value = error.message
  }
}

// Get token from URL on component mount
onMounted(() => {
  token.value = route.query.token
  
  if (!token.value) {
    errorMessage.value = 'Invalid or missing reset token'
  }
})
</script>

<style scoped>
.v-card {
  border-radius: 12px;
}
</style>
