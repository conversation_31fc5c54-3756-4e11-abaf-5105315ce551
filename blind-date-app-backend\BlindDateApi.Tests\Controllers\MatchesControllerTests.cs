using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BlindDateApi.Controllers;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlindDateApi.Tests.Controllers
{
    public class MatchesControllerTests
    {
        private readonly Mock<IMatchService> _mockMatchService;
        private readonly Mock<ILogger<MatchesController>> _mockLogger;
        private readonly MatchesController _controller;

        public MatchesControllerTests()
        {
            _mockMatchService = new Mock<IMatchService>();
            _mockLogger = new Mock<ILogger<MatchesController>>();
            _controller = new MatchesController(_mockMatchService.Object, _mockLogger.Object);

            // Setup controller context with user identity
            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.Name, "1"),
                new Claim(ClaimTypes.NameIdentifier, "1"),
            }, "mock"));

            var httpContext = new DefaultHttpContext { User = user };
            httpContext.Items["User"] = new BlindDateApi.Data.Models.User { Id = 1 };

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };
        }

        [Fact]
        public async Task GetMatches_ReturnsOkResult_WithMatches()
        {
            // Arrange
            var matches = new List<MatchDto>
            {
                new MatchDto
                {
                    Id = 1,
                    OtherUserId = 2,
                    FirstName = "Test",
                    LastName = "User",
                    Age = 25,
                    CreatedAt = DateTime.UtcNow,
                    IsMatched = true,
                    HasResponded = true,
                    Pictures = new List<string> { "pic1.jpg", "pic2.jpg" },
                    LastMessageAt = DateTime.UtcNow.AddMinutes(-5),
                    LastMessagePreview = "Hello",
                    UnreadCount = 1
                }
            };

            _mockMatchService.Setup(s => s.GetMatchesAsync(1))
                .ReturnsAsync(matches);

            // Act
            var result = await _controller.GetMatches();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedMatches = Assert.IsType<List<MatchDto>>(okResult.Value);
            Assert.Single(returnedMatches);
            Assert.Equal(1, returnedMatches[0].Id);
        }

        [Fact]
        public async Task GetMatch_ReturnsOkResult_WithMatch()
        {
            // Arrange
            var matchId = 1;
            var match = new MatchDto
            {
                Id = matchId,
                OtherUserId = 2,
                FirstName = "Test",
                LastName = "User",
                Age = 25,
                CreatedAt = DateTime.UtcNow,
                IsMatched = true,
                HasResponded = true,
                Pictures = new List<string> { "pic1.jpg", "pic2.jpg" },
                LastMessageAt = DateTime.UtcNow.AddMinutes(-5),
                LastMessagePreview = "Hello",
                UnreadCount = 1
            };

            _mockMatchService.Setup(s => s.GetMatchByIdAsync(1, matchId))
                .ReturnsAsync(match);

            // Act
            var result = await _controller.GetMatch(matchId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedMatch = Assert.IsType<MatchDto>(okResult.Value);
            Assert.Equal(matchId, returnedMatch.Id);
        }

        [Fact]
        public async Task SearchMatch_ReturnsOkResult_WithMatch()
        {
            // Arrange
            var match = new MatchDto
            {
                Id = 1,
                OtherUserId = 2,
                FirstName = "Test",
                LastName = "User",
                Age = 25,
                CreatedAt = DateTime.UtcNow,
                IsMatched = false,
                HasResponded = false,
                Pictures = new List<string> { "pic1.jpg", "pic2.jpg" }
            };

            _mockMatchService.Setup(s => s.HasReachedMatchLimitAsync(1))
                .ReturnsAsync(false);

            _mockMatchService.Setup(s => s.FindMatchAsync(1))
                .ReturnsAsync(match);

            // Act
            var result = await _controller.SearchMatch();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedMatch = Assert.IsType<MatchDto>(okResult.Value);
            Assert.Equal(1, returnedMatch.Id);
        }

        [Fact]
        public async Task SearchMatch_ReturnsBadRequest_WhenLimitReached()
        {
            // Arrange
            _mockMatchService.Setup(s => s.HasReachedMatchLimitAsync(1))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.SearchMatch();

            // Assert
            Assert.IsType<BadRequestObjectResult>(result.Result);
        }

        [Fact]
        public async Task LikeMatch_ReturnsOkResult_WithIsMatched()
        {
            // Arrange
            var matchId = 1;
            _mockMatchService.Setup(s => s.RespondToMatchAsync(1, It.Is<MatchResponseDto>(r =>
                r.MatchId == matchId && r.Like == true)))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.LikeMatch(matchId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnValue = okResult.Value;
            Assert.True((bool)(returnValue.GetType().GetProperty("isMatched").GetValue(returnValue, null)));
        }

        [Fact]
        public async Task DislikeMatch_ReturnsOkResult_WithSuccess()
        {
            // Arrange
            var matchId = 1;
            _mockMatchService.Setup(s => s.RespondToMatchAsync(1, It.Is<MatchResponseDto>(r =>
                r.MatchId == matchId && r.Like == false)))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.DislikeMatch(matchId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnValue = okResult.Value;
            Assert.True((bool)(returnValue.GetType().GetProperty("success").GetValue(returnValue, null)));
        }

        [Fact]
        public async Task GetRemainingMatches_ReturnsOkResult_WithCount()
        {
            // Arrange
            _mockMatchService.Setup(s => s.GetRemainingMatchesAsync(1))
                .ReturnsAsync(8);

            // Act
            var result = await _controller.GetRemainingMatches();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnValue = okResult.Value;
            Assert.Equal(8, (int)(returnValue.GetType().GetProperty("remaining").GetValue(returnValue, null)));
        }
    }
}
