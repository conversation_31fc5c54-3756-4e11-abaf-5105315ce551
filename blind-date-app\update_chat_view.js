const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src', 'views', 'ChatView.vue');
let content = fs.readFileSync(filePath, 'utf8');

// Update the message content to use decrypted content
content = content.replace(
  /content: msg\.encryptedContent, \/\/ In a real app, this would be decrypted/,
  'content: msg.decryptedContent || "[Encrypted message]", // Use the decrypted content'
);

// Update the sendMessage function to use the new method signature
content = content.replace(
  /\/\/ In a real app, we would encrypt the message here\s+const encryptedContent = message\.value \/\/ This would be encrypted\s+const iv = ['"]dummy-iv['"] \/\/ This would be generated\s+\s+\/\/ Send message using the chat store\s+await chatStore\.sendMessage\(chatStore\.currentConversationId, encryptedContent, iv\)/,
  '// Send message using the chat store - encryption is handled internally\n    await chatStore.sendMessage(chatStore.currentConversationId, message.value)'
);

// Update the local chat message content
content = content.replace(
  /content: encryptedContent,/,
  'content: message.value, // Will be encrypted by the chat store'
);

fs.writeFileSync(filePath, content);
console.log('ChatView.vue updated successfully');
