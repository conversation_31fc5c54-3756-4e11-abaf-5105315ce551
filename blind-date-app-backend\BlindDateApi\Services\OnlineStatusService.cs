using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Services
{
    public class OnlineStatusService : IOnlineStatusService
    {
        private readonly IUserRepository _userRepository;
        private readonly ICacheService _cacheService;
        private readonly ILogger<OnlineStatusService> _logger;

        // Cache keys
        private const string OnlineUsersKey = "OnlineUsers";
        private const string UserOnlineStatusKey = "User_Online_{0}"; // UserId

        // Cache expiration times
        private readonly TimeSpan _onlineUsersCacheTime = TimeSpan.FromMinutes(1);
        private readonly TimeSpan _userStatusCacheTime = TimeSpan.FromMinutes(5);

        public OnlineStatusService(
            IUserRepository userRepository,
            ICacheService cacheService,
            ILogger<OnlineStatusService> logger)
        {
            _userRepository = userRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        public async Task SetUserOnlineAsync(int userId)
        {
            try
            {
                // Update user in database
                var user = await _userRepository.GetByIdAsync(userId);
                if (user != null)
                {
                    user.IsOnline = true;
                    user.LastActiveAt = DateTime.UtcNow;
                    _userRepository.Update(user);
                    await _userRepository.SaveChangesAsync();

                    // Update cache
                    var cacheKey = string.Format(UserOnlineStatusKey, userId);
                    await _cacheService.SetAsync(cacheKey, true, _userStatusCacheTime);

                    // Update online users list in cache
                    await AddUserToOnlineListAsync(userId);

                    _logger.LogInformation("User {UserId} is now online", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting user {UserId} as online", userId);
                throw;
            }
        }

        public async Task SetUserOfflineAsync(int userId)
        {
            try
            {
                // Update user in database
                var user = await _userRepository.GetByIdAsync(userId);
                if (user != null)
                {
                    user.IsOnline = false;
                    user.LastActiveAt = DateTime.UtcNow;
                    _userRepository.Update(user);
                    await _userRepository.SaveChangesAsync();

                    // Update cache
                    var cacheKey = string.Format(UserOnlineStatusKey, userId);
                    await _cacheService.SetAsync(cacheKey, false, _userStatusCacheTime);

                    // Update online users list in cache
                    await RemoveUserFromOnlineListAsync(userId);

                    _logger.LogInformation("User {UserId} is now offline", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting user {UserId} as offline", userId);
                throw;
            }
        }

        public async Task UpdateUserActivityAsync(int userId)
        {
            try
            {
                // Update user in database
                var user = await _userRepository.GetByIdAsync(userId);
                if (user != null)
                {
                    user.LastActiveAt = DateTime.UtcNow;

                    // If user wasn't online before, set them as online
                    if (!user.IsOnline)
                    {
                        user.IsOnline = true;

                        // Update online users list in cache
                        await AddUserToOnlineListAsync(userId);
                    }

                    _userRepository.Update(user);
                    await _userRepository.SaveChangesAsync();

                    // Update cache
                    var cacheKey = string.Format(UserOnlineStatusKey, userId);
                    await _cacheService.SetAsync(cacheKey, true, _userStatusCacheTime);

                    _logger.LogDebug("Updated activity timestamp for user {UserId}", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating activity for user {UserId}", userId);
                throw;
            }
        }

        public async Task<List<int>> GetOnlineUserIdsAsync()
        {
            try
            {
                // Try to get from cache first
                var cachedOnlineUsers = await _cacheService.GetAsync<List<int>>(OnlineUsersKey);
                if (cachedOnlineUsers != null)
                {
                    return cachedOnlineUsers;
                }

                // If not in cache, get from database
                var onlineUsers = await _userRepository.GetAllAsync(
                    u => u.IsOnline && u.IsActive
                );

                var onlineUserIds = onlineUsers.Select(u => u.Id).ToList();

                // Cache the result
                await _cacheService.SetAsync(OnlineUsersKey, onlineUserIds, _onlineUsersCacheTime);

                return onlineUserIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting online users");
                throw;
            }
        }

        public async Task<bool> IsUserOnlineAsync(int userId)
        {
            try
            {
                // Try to get from cache first
                var cacheKey = string.Format(UserOnlineStatusKey, userId);
                var cachedStatus = await _cacheService.GetAsync<bool?>(cacheKey);
                if (cachedStatus.HasValue)
                {
                    return cachedStatus.Value;
                }

                // If not in cache, get from database
                var user = await _userRepository.GetByIdAsync(userId);
                var isOnline = user != null && user.IsOnline && user.IsActive;

                // Cache the result
                await _cacheService.SetAsync(cacheKey, isOnline, _userStatusCacheTime);

                return isOnline;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} is online", userId);
                throw;
            }
        }

        public async Task CleanupInactiveUsersAsync(TimeSpan inactivityThreshold)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow.Subtract(inactivityThreshold);

                // Find users who are marked as online but haven't been active recently
                var inactiveUsers = await _userRepository.GetAllAsync(
                    u => u.IsOnline && (u.LastActiveAt == null || u.LastActiveAt < cutoffTime)
                );

                foreach (var user in inactiveUsers)
                {
                    user.IsOnline = false;
                    _userRepository.Update(user);

                    // Update cache
                    var cacheKey = string.Format(UserOnlineStatusKey, user.Id);
                    await _cacheService.SetAsync(cacheKey, false, _userStatusCacheTime);

                    // Update online users list in cache
                    await RemoveUserFromOnlineListAsync(user.Id);

                    _logger.LogInformation("User {UserId} marked as offline due to inactivity", user.Id);
                }

                await _userRepository.SaveChangesAsync();

                // Refresh the online users cache
                await RefreshOnlineUsersCacheAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up inactive users");
                throw;
            }
        }

        private async Task AddUserToOnlineListAsync(int userId)
        {
            var onlineUsers = await _cacheService.GetAsync<List<int>>(OnlineUsersKey) ?? new List<int>();

            if (!onlineUsers.Contains(userId))
            {
                onlineUsers.Add(userId);
                await _cacheService.SetAsync(OnlineUsersKey, onlineUsers, _onlineUsersCacheTime);
            }
        }

        private async Task RemoveUserFromOnlineListAsync(int userId)
        {
            var onlineUsers = await _cacheService.GetAsync<List<int>>(OnlineUsersKey);

            if (onlineUsers != null && onlineUsers.Contains(userId))
            {
                onlineUsers.Remove(userId);
                await _cacheService.SetAsync(OnlineUsersKey, onlineUsers, _onlineUsersCacheTime);
            }
        }

        private async Task RefreshOnlineUsersCacheAsync()
        {
            var onlineUsers = await _userRepository.GetAllAsync(
                u => u.IsOnline && u.IsActive
            );

            var onlineUserIds = onlineUsers.Select(u => u.Id).ToList();
            await _cacheService.SetAsync(OnlineUsersKey, onlineUserIds, _onlineUsersCacheTime);
        }
    }
}
