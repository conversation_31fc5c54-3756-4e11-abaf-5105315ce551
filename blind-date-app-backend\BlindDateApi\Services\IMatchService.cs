using System.Collections.Generic;
using System.Threading.Tasks;
using BlindDateApi.DTOs;

namespace BlindDateApi.Services
{
    public interface IMatchService
    {
        /// <summary>
        /// Gets all matches for a user
        /// </summary>
        Task<List<MatchDto>> GetMatchesAsync(int userId);

        /// <summary>
        /// Finds a potential match for a user
        /// </summary>
        Task<MatchDto> FindMatchAsync(int userId);

        /// <summary>
        /// Responds to a match (like or dislike)
        /// </summary>
        Task<bool> RespondToMatchAsync(int userId, MatchResponseDto response);

        /// <summary>
        /// Gets a specific match by ID
        /// </summary>
        Task<MatchDto> GetMatchByIdAsync(int userId, int matchId);

        /// <summary>
        /// Checks if a user has reached their daily match limit
        /// </summary>
        Task<bool> HasReachedMatchLimitAsync(int userId);

        /// <summary>
        /// Gets the number of remaining matches for a user today
        /// </summary>
        Task<int> GetRemainingMatchesAsync(int userId);
    }
}
