<template>
  <div class="stripe-payment-form">
    <v-form @submit.prevent="handleSubmit">
      <v-text-field
        v-model="cardName"
        label="Name on Card"
        variant="outlined"
        :rules="[rules.required]"
        required
        :disabled="isLoading"
      ></v-text-field>

      <v-text-field
        v-model="email"
        label="Email"
        variant="outlined"
        type="email"
        :rules="[rules.required, rules.email]"
        required
        :disabled="isLoading"
      ></v-text-field>

      <div class="card-element-container">
        <label class="card-element-label">Card Details</label>
        <div id="card-element" ref="cardElement" class="card-element"></div>
        <div v-if="cardError" class="card-error text-error">{{ cardError }}</div>
      </div>

      <v-btn
        type="submit"
        color="primary"
        block
        :loading="isLoading"
        :disabled="!isCardComplete || isLoading"
        class="mt-4"
      >
        Pay {{ formatPrice(amount) }}
      </v-btn>
    </v-form>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { getStripe } from '../services/stripe'

const props = defineProps({
  amount: {
    type: Number,
    required: true
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit'])

const cardName = ref('')
const email = ref('')
const cardElement = ref(null)
const stripe = ref(null)
const elements = ref(null)
const cardError = ref('')
const isCardComplete = ref(false)

const rules = {
  required: value => !!value || 'Required',
  email: value => {
    const pattern = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    return pattern.test(value) || 'Invalid e-mail'
  }
}

const formatPrice = (price) => {
  return `€${price.toFixed(2)}`
}

const handleSubmit = async () => {
  if (!isCardComplete.value) return

  emit('submit', {
    cardElement: elements.value.getElement('card'),
    cardName: cardName.value,
    email: email.value
  })
}

onMounted(async () => {
  try {
    stripe.value = await getStripe()
    elements.value = stripe.value.elements()
    
    const card = elements.value.create('card', {
      style: {
        base: {
          color: '#32325d',
          fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
          fontSmoothing: 'antialiased',
          fontSize: '16px',
          '::placeholder': {
            color: '#aab7c4'
          }
        },
        invalid: {
          color: '#fa755a',
          iconColor: '#fa755a'
        }
      }
    })
    
    card.mount(cardElement.value)
    
    card.on('change', (event) => {
      isCardComplete.value = event.complete
      cardError.value = event.error ? event.error.message : ''
    })
  } catch (error) {
    console.error('Error initializing Stripe:', error)
    cardError.value = 'Failed to load payment form. Please try again later.'
  }
})

onUnmounted(() => {
  if (elements.value) {
    const card = elements.value.getElement('card')
    if (card) {
      card.unmount()
    }
  }
})
</script>

<style scoped>
.stripe-payment-form {
  max-width: 500px;
  margin: 0 auto;
}

.card-element-container {
  margin-top: 16px;
  margin-bottom: 16px;
}

.card-element-label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.875rem;
  color: rgba(0, 0, 0, 0.6);
}

.card-element {
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.23);
  border-radius: 4px;
  background-color: white;
}

.card-error {
  margin-top: 8px;
  font-size: 0.75rem;
  color: #f44336;
}
</style>
