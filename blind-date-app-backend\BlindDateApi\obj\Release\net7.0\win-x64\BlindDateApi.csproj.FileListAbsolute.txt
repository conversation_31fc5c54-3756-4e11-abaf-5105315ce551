C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\appsettings.Development.json
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\appsettings.json
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BlindDateApi.exe
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BlindDateApi.deps.json
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BlindDateApi.runtimeconfig.json
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BlindDateApi.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BlindDateApi.pdb
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Azure.Core.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Azure.Identity.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BCrypt.Net-Next.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Humanizer.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.EntityFrameworkCore.SqlServer.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Extensions.Caching.StackExchangeRedis.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Mono.TextTemplating.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\StackExchange.Redis.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Stripe.net.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.CodeDom.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Drawing.Common.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Memory.Data.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Security.Permissions.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\System.Windows.Extensions.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BlindDateApi.Data.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\bin\Release\net7.0\win-x64\BlindDateApi.Data.pdb
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\staticwebassets\msbuild.BlindDateApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\staticwebassets\msbuild.build.BlindDateApi.props
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\staticwebassets\msbuild.buildMultiTargeting.BlindDateApi.props
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\staticwebassets\msbuild.buildTransitive.BlindDateApi.props
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\staticwebassets.pack.json
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\staticwebassets.build.json
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\staticwebassets.development.json
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\scopedcss\bundle\BlindDateApi.styles.css
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.csproj.CopyComplete
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\refint\BlindDateApi.dll
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.pdb
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\BlindDateApi.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Datix\blind-date-app-backend\BlindDateApi\obj\Release\net7.0\win-x64\ref\BlindDateApi.dll
