using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlindDateApi.Data.Models
{
    public class Message
    {
        [Key]
        public int Id { get; set; }

        // Support both old Match-based messages and new Conversation-based messages
        public int? MatchId { get; set; }
        public int? ConversationId { get; set; }

        [Required]
        public int SenderId { get; set; }

        [Required]
        public DateTime SentAt { get; set; } = DateTime.UtcNow;

        // Plain text content (not used with E2EE)
        [MaxLength(2000)]
        public string Content { get; set; } = string.Empty;

        // For E2EE
        [Required]
        [MaxLength(2000)]
        public string EncryptedContent { get; set; } = string.Empty;

        // For E2EE
        [Required]
        [MaxLength(2000)]
        public string EncryptedKey { get; set; } = string.Empty;

        // Initialization vector used for encryption
        [MaxLength(100)]
        public string IV { get; set; } = string.Empty;

        public bool IsRead { get; set; } = false;

        public DateTime? ReadAt { get; set; }

        // Message type (text, image, etc.)
        public MessageType Type { get; set; } = MessageType.Text;

        // Navigation properties
        [ForeignKey("MatchId")]
        public virtual Match? Match { get; set; }

        [ForeignKey("ConversationId")]
        public virtual Conversation? Conversation { get; set; }

        [ForeignKey("SenderId")]
        public virtual User? Sender { get; set; }
    }

    public enum MessageType
    {
        Text = 0,
        Image = 1,
        RevealRequest = 2,
        RevealAccepted = 3,
        RevealRejected = 4,
        SystemMessage = 5
    }
}
