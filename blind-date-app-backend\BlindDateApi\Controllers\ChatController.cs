using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ChatController : ControllerBase
    {
        private readonly IChatService _chatService;
        private readonly ILogger<ChatController> _logger;

        public ChatController(IChatService chatService, ILogger<ChatController> logger)
        {
            _chatService = chatService;
            _logger = logger;
        }

        [HttpGet("conversations")]
        public async Task<ActionResult<List<ConversationDto>>> GetConversations()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var conversations = await _chatService.GetConversationsAsync(userIdInt);
                return Ok(conversations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversations");
                return StatusCode(500, "An error occurred while getting conversations");
            }
        }

        [HttpGet("conversations/{id}")]
        public async Task<ActionResult<ConversationDto>> GetConversation(int id)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var conversation = await _chatService.GetConversationAsync(userIdInt, id);
                return Ok(conversation);
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conversation {ConversationId}", id);
                return StatusCode(500, "An error occurred while getting the conversation");
            }
        }

        [HttpGet("conversations/{id}/messages")]
        public async Task<ActionResult<List<ChatMessageDto>>> GetMessages(int id, [FromQuery] int? lastMessageId = null, [FromQuery] int count = 20)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var messages = await _chatService.GetMessagesAsync(userIdInt, id, lastMessageId, count);
                return Ok(messages);
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting messages for conversation {ConversationId}", id);
                return StatusCode(500, "An error occurred while getting messages");
            }
        }

        [HttpPost("conversations")]
        public async Task<ActionResult<ConversationDto>> CreateConversation(CreateConversationDto dto)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var conversation = await _chatService.CreateConversationAsync(userIdInt, dto);
                return Ok(conversation);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating conversation");
                return StatusCode(500, "An error occurred while creating the conversation");
            }
        }

        [HttpPost("messages")]
        public async Task<ActionResult<ChatMessageDto>> SendMessage(SendChatMessageDto dto)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var message = await _chatService.SendMessageAsync(userIdInt, dto);
                return Ok(message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending message to conversation {ConversationId}", dto.ConversationId);
                return StatusCode(500, "An error occurred while sending the message");
            }
        }

        [HttpPost("conversations/{id}/read")]
        public async Task<ActionResult> MarkAsRead(int id, [FromBody] MarkAsReadDto dto)
        {
            try
            {
                // Ensure the ID in the route matches the ID in the DTO
                if (id != dto.ConversationId)
                {
                    return BadRequest("Conversation ID mismatch");
                }

                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var result = await _chatService.MarkAsReadAsync(userIdInt, dto);
                return Ok(new { success = result });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking conversation {ConversationId} as read", id);
                return StatusCode(500, "An error occurred while marking the conversation as read");
            }
        }

        [HttpPost("conversations/{id}/reveal-request")]
        public async Task<ActionResult> RequestReveal(int id, [FromBody] RevealRequestDto dto)
        {
            try
            {
                // Ensure the ID in the route matches the ID in the DTO
                if (id != dto.ConversationId)
                {
                    return BadRequest("Conversation ID mismatch");
                }

                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var result = await _chatService.RequestRevealAsync(userIdInt, dto);
                return Ok(new { success = result });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error requesting reveal in conversation {ConversationId}", id);
                return StatusCode(500, "An error occurred while requesting the reveal");
            }
        }

        [HttpPost("conversations/{id}/reveal-response")]
        public async Task<ActionResult> RespondToReveal(int id, [FromBody] RevealResponseDto dto)
        {
            try
            {
                // Ensure the ID in the route matches the ID in the DTO
                if (id != dto.ConversationId)
                {
                    return BadRequest("Conversation ID mismatch");
                }

                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var result = await _chatService.RespondToRevealAsync(userIdInt, dto);
                return Ok(new { success = result });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error responding to reveal request in conversation {ConversationId}", id);
                return StatusCode(500, "An error occurred while responding to the reveal request");
            }
        }

        [HttpDelete("conversations/{id}")]
        public async Task<ActionResult> DeleteConversation(int id)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var result = await _chatService.DeleteConversationAsync(userIdInt, id);
                return Ok(new { success = result });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting conversation {ConversationId}", id);
                return StatusCode(500, "An error occurred while deleting the conversation");
            }
        }

        [HttpGet("remaining-chats")]
        public async Task<ActionResult<int>> GetRemainingChats()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var remaining = await _chatService.GetRemainingChatsAsync(userIdInt);
                return Ok(new { remaining });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting remaining chats");
                return StatusCode(500, "An error occurred while getting remaining chats");
            }
        }

        [HttpPut("conversations/{id}/update-match")]
        public async Task<ActionResult<bool>> UpdateMessagesWithMatchId(int id, [FromBody] UpdateMatchIdDto dto)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                // Validate the match ID
                if (dto.MatchId <= 0)
                {
                    return BadRequest("Invalid match ID");
                }

                // Update all messages in the conversation with the match ID
                var result = await _chatService.UpdateMessagesWithMatchIdAsync(id, dto.MatchId, userIdInt);
                return Ok(new { success = result });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating messages with match ID for conversation {ConversationId}", id);
                return StatusCode(500, "An error occurred while updating messages with match ID");
            }
        }
    }
}
