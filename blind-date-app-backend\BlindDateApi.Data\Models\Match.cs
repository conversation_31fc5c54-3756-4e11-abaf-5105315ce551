using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BlindDateApi.Data.Models
{
    public class Match
    {
        public Match()
        {
            Messages = new HashSet<Message>();
        }

        [Key]
        public int Id { get; set; }

        [Required]
        public int User1Id { get; set; }

        [Required]
        public int User2Id { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Status: 0 = Pending, 1 = Matched, 2 = Rejected
        public int Status { get; set; } = 0;

        // User1's response: null = No response, true = Like, false = Dislike
        public bool? User1Response { get; set; }

        // User2's response: null = No response, true = Like, false = Dislike
        public bool? User2Response { get; set; }

        // Navigation properties
        public virtual User? User1 { get; set; }
        public virtual User? User2 { get; set; }
        public virtual ICollection<Message> Messages { get; set; }

        // Helper methods
        [NotMapped]
        public bool IsMatched => User1Response == true && User2Response == true;

        [NotMapped]
        public bool IsRejected => User1Response == false || User2Response == false;

        public User? GetOtherUser(int userId) =>
            userId == User1Id ? User2 : User1;

        public bool? GetUserResponse(int userId) =>
            userId == User1Id ? User1Response : User2Response;

        public void SetUserResponse(int userId, bool response)
        {
            if (userId == User1Id)
                User1Response = response;
            else if (userId == User2Id)
                User2Response = response;

            // Update status based on responses
            if (User1Response == true && User2Response == true)
                Status = 1; // Matched
            else if (User1Response == false || User2Response == false)
                Status = 2; // Rejected
        }
    }
}
