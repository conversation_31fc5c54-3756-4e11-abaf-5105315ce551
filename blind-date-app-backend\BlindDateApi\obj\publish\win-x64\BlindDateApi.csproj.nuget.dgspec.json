{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi\\BlindDateApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi.Data\\BlindDateApi.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi.Data\\BlindDateApi.Data.csproj", "projectName": "BlindDateApi.Data", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi.Data\\BlindDateApi.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://trademarknow.jfrog.io/artifactory/api/nuget/v3/trademarknow-corsearch-entities-local": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.14, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi\\BlindDateApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi\\BlindDateApi.csproj", "projectName": "BlindDateApi", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi\\BlindDateApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi\\obj\\publish\\win-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\Program Files\\dotnet\\sdk\\7.0.306\\Sdks\\Microsoft.NET.Sdk.Web\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://trademarknow.jfrog.io/artifactory/api/nuget/v3/trademarknow-corsearch-entities-local": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi.Data\\BlindDateApi.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Datix\\blind-date-app-backend\\BlindDateApi.Data\\BlindDateApi.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.416.16, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.14, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[7.0.14, )"}, "Stripe.net": {"target": "Package", "version": "[48.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.306\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}