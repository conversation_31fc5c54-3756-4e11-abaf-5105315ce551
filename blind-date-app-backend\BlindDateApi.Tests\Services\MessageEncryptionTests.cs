using System;
using System.Threading.Tasks;
using BlindDateApi.Data.Models;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Moq;
using Xunit;

namespace BlindDateApi.Tests.Services
{
    public class MessageEncryptionTests
    {
        private readonly Mock<IEncryptionService> _mockEncryptionService;
        private readonly IEncryptionService _realEncryptionService;

        public MessageEncryptionTests()
        {
            _mockEncryptionService = new Mock<IEncryptionService>();
            _realEncryptionService = new EncryptionService();
        }

        [Fact]
        public void GenerateKeyPair_ReturnsValidKeys()
        {
            // Arrange
            var expectedPublicKey = "publicKey123";
            var expectedPrivateKey = "privateKey456";

            _mockEncryptionService
                .Setup(service => service.GenerateKeyPair())
                .Returns((expectedPublicKey, expectedPrivateKey));

            // Act
            var (publicKey, privateKey) = _mockEncryptionService.Object.GenerateKeyPair();

            // Assert
            Assert.Equal(expectedPublicKey, publicKey);
            Assert.Equal(expectedPrivateKey, privateKey);
        }

        [Fact]
        public void EncryptMessage_ReturnsEncryptedContent()
        {
            // Arrange
            var message = "Hello, this is a secret message";
            var publicKey = "publicKey123";
            var expectedEncryptedMessage = "encryptedContent789";

            _mockEncryptionService
                .Setup(service => service.EncryptMessage(message, publicKey))
                .Returns(expectedEncryptedMessage);

            // Act
            var encryptedMessage = _mockEncryptionService.Object.EncryptMessage(message, publicKey);

            // Assert
            Assert.Equal(expectedEncryptedMessage, encryptedMessage);
        }

        [Fact]
        public void DecryptMessage_ReturnsDecryptedContent()
        {
            // Arrange
            var encryptedMessage = "encryptedContent789";
            var privateKey = "privateKey456";
            var expectedDecryptedMessage = "Hello, this is a secret message";

            _mockEncryptionService
                .Setup(service => service.DecryptMessage(encryptedMessage, privateKey))
                .Returns(expectedDecryptedMessage);

            // Act
            var decryptedMessage = _mockEncryptionService.Object.DecryptMessage(encryptedMessage, privateKey);

            // Assert
            Assert.Equal(expectedDecryptedMessage, decryptedMessage);
        }

        [Fact]
        public void EndToEndEncryption_WorksWithRealImplementation()
        {
            // Arrange
            var originalMessage = "Hello, this is a secret message";
            var (alicePublicKey, alicePrivateKey) = _realEncryptionService.GenerateKeyPair();
            var (bobPublicKey, bobPrivateKey) = _realEncryptionService.GenerateKeyPair();

            // Act - Alice sends a message to Bob
            // Alice encrypts the message with Bob's public key
            var encryptedByAlice = _realEncryptionService.EncryptMessage(originalMessage, bobPublicKey);

            // Bob decrypts the message with his private key
            var decryptedByBob = _realEncryptionService.DecryptMessage(encryptedByAlice, bobPrivateKey);

            // Assert
            Assert.Equal(originalMessage, decryptedByBob);

            // Bob cannot decrypt with Alice's private key
            Assert.Throws<System.Security.Cryptography.CryptographicException>(() =>
                _realEncryptionService.DecryptMessage(encryptedByAlice, alicePrivateKey));
        }

        [Fact]
        public void MessageDTO_ContainsEncryptionFields()
        {
            // This test verifies that the SendMessageDto has the necessary fields for E2EE

            // Arrange & Act
            var messageDto = new SendChatMessageDto
            {
                ConversationId = 1,
                EncryptedContent = "This would be the encrypted version",
                IV = "This would be the initialization vector"
            };

            // Assert
            Assert.NotNull(messageDto.EncryptedContent);
            Assert.NotNull(messageDto.IV);
        }

        [Fact]
        public void Message_ContainsEncryptionFields()
        {
            // This test verifies that the Message entity has the necessary fields for E2EE

            // Arrange & Act
            var message = new Message
            {
                MatchId = 1,
                SenderId = 1,
                Content = "Hello, this is a plain text message",
                EncryptedContent = "This would be the encrypted version",
                IV = "This would be the initialization vector"
            };

            // Assert
            Assert.NotNull(message.EncryptedContent);
            Assert.NotNull(message.IV);
            Assert.NotEqual(message.Content, message.EncryptedContent);
        }
    }
}
