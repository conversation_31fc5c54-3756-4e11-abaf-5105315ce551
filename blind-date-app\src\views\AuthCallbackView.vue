<script setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const status = ref('Processing authentication...')
const error = ref(null)

// Function to parse hash parameters from URL
const parseHashParams = () => {
  const hash = window.location.hash.substring(1)
  return hash.split('&').reduce((result, item) => {
    const parts = item.split('=')
    if (parts.length === 2) {
      result[parts[0]] = decodeURIComponent(parts[1])
    }
    return result
  }, {})
}

onMounted(async () => {
  try {
    // Check for hash parameters (used in implicit flow)
    const hashParams = parseHashParams()

    // Get the provider from the state parameter or query parameters
    const provider = hashParams.state || route.query.state || route.query.provider

    if (!provider) {
      error.value = 'Missing provider parameter'
      return
    }

    // Get the authentication token from hash or query parameters
    const accessToken = hashParams.access_token || route.query.access_token
    const idToken = hashParams.id_token
    const code = route.query.code
    const errorParam = hashParams.error || route.query.error

    if (errorParam) {
      throw new Error(`Authentication error: ${errorParam}`)
    }

    status.value = `Completing ${provider} authentication...`

    // Process the authentication response
    if (provider === 'google' || provider === 'facebook') {
      if (!accessToken && !code && !idToken) {
        throw new Error('Missing authentication token or code')
      }

      // Get user info using the access token
      let userData

      if (accessToken) {
        // For Google
        if (provider === 'google') {
          const response = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          })

          if (!response.ok) {
            throw new Error('Failed to get user info from Google')
          }

          userData = await response.json()

          userData = {
            id: userData.sub,
            email: userData.email,
            firstName: userData.given_name,
            lastName: userData.family_name,
            picture: userData.picture,
            token: accessToken,
            provider: 'google'
          }
        }
        // For Facebook
        else if (provider === 'facebook') {
          const response = await fetch(`https://graph.facebook.com/me?fields=id,email,first_name,last_name,picture&access_token=${accessToken}`)

          if (!response.ok) {
            throw new Error('Failed to get user info from Facebook')
          }

          const fbData = await response.json()

          userData = {
            id: fbData.id,
            email: fbData.email,
            firstName: fbData.first_name,
            lastName: fbData.last_name,
            picture: fbData.picture?.data?.url,
            token: accessToken,
            provider: 'facebook'
          }
        }
      }

      // Call the backend to complete the social login
      if (userData) {
        // Complete the social login process with the user data
        await authStore.loginWithSocial(provider, userData)
      } else {
        // If we don't have user data, just use the provider
        await authStore.loginWithSocial(provider)
      }

      // Get the original URL to redirect to
      const redirectUrl = sessionStorage.getItem(`${provider}AuthRedirectUrl`) || '/home'
      sessionStorage.removeItem(`${provider}AuthRedirectUrl`)

      // Redirect to the home page or profile setup
      if (authStore.user.profileCompleted) {
        router.push(redirectUrl)
      } else {
        router.push('/profile-setup')
      }
    } else {
      throw new Error(`Unsupported provider: ${provider}`)
    }
  } catch (err) {
    console.error('Auth callback error:', err)
    error.value = err.message || 'Authentication failed'
  }
})
</script>

<template>
  <v-container class="fill-height">
    <v-row justify="center" align="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="pa-6" elevation="8">
          <v-card-title class="text-center text-h5 font-weight-bold">
            Authentication
          </v-card-title>

          <v-card-text class="text-center">
            <div v-if="error" class="error-message">
              <v-alert type="error" variant="tonal" class="mb-4">
                {{ error }}
              </v-alert>
              <v-btn color="primary" to="/login" class="mt-4">
                Return to Login
              </v-btn>
            </div>
            <div v-else>
              <v-progress-circular
                indeterminate
                color="primary"
                size="64"
                class="mb-4"
              ></v-progress-circular>
              <div class="text-body-1">{{ status }}</div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<style scoped>
.error-message {
  color: #d32f2f;
}
</style>
