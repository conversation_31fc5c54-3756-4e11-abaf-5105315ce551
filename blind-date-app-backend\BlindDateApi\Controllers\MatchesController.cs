using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace BlindDateApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MatchesController : ControllerBase
    {
        private readonly IMatchService _matchService;
        private readonly ISearchingUsersService _searchingUsersService;
        private readonly ILogger<MatchesController> _logger;

        public MatchesController(
            IMatchService matchService,
            ISearchingUsersService searchingUsersService,
            ILogger<MatchesController> logger)
        {
            _matchService = matchService;
            _searchingUsersService = searchingUsersService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<List<MatchDto>>> GetMatches()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var matches = await _matchService.GetMatchesAsync(userIdInt);
                return Ok(matches);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting matches");
                return StatusCode(500, "An error occurred while getting matches");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<MatchDto>> GetMatch(int id)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var match = await _matchService.GetMatchByIdAsync(userIdInt, id);
                return Ok(match);
            }
            catch (InvalidOperationException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting match {MatchId}", id);
                return StatusCode(500, "An error occurred while getting the match");
            }
        }

        [HttpPost("start-searching")]
        public async Task<ActionResult> StartSearching()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                // Check if user has reached their match limit
                if (await _matchService.HasReachedMatchLimitAsync(userIdInt))
                {
                    return BadRequest("You have reached your daily match limit");
                }

                // Add user to searching list
                await _searchingUsersService.AddUserToSearchingAsync(userIdInt);

                return Ok(new { success = true, message = "Started searching for matches" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting search");
                return StatusCode(500, "An error occurred while starting search");
            }
        }

        [HttpPost("stop-searching")]
        public async Task<ActionResult> StopSearching()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                // Remove user from searching list
                await _searchingUsersService.RemoveUserFromSearchingAsync(userIdInt);

                return Ok(new { success = true, message = "Stopped searching for matches" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping search");
                return StatusCode(500, "An error occurred while stopping search");
            }
        }

        [HttpGet("searching-status")]
        public async Task<ActionResult> GetSearchingStatus()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                // Check if user is searching
                bool isSearching = await _searchingUsersService.IsUserSearchingAsync(userIdInt);

                // Get count of other searching users
                int searchingCount = await _searchingUsersService.GetSearchingUsersCountAsync();
                if (isSearching) searchingCount--; // Don't count the current user

                return Ok(new {
                    isSearching,
                    searchingCount,
                    canSearch = !await _matchService.HasReachedMatchLimitAsync(userIdInt)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting searching status");
                return StatusCode(500, "An error occurred while getting searching status");
            }
        }

        [HttpPost("search")]
        public async Task<ActionResult<MatchDto>> SearchMatch()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                // Check if user has reached their match limit
                if (await _matchService.HasReachedMatchLimitAsync(userIdInt))
                {
                    return BadRequest("You have reached your daily match limit");
                }

                // Add user to searching list
                await _searchingUsersService.AddUserToSearchingAsync(userIdInt);

                // Try to find a match
                var match = await _matchService.FindMatchAsync(userIdInt);

                if (match == null)
                {
                    // If no match found, keep the user in searching mode
                    return NotFound("No matches found at this time. Please try again later.");
                }

                return Ok(match);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching for match");
                return StatusCode(500, "An error occurred while searching for a match");
            }
        }

        [HttpPost("{id}/like")]
        public async Task<ActionResult<bool>> LikeMatch(int id)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var response = new MatchResponseDto
                {
                    MatchId = id,
                    Like = true
                };

                var isMatched = await _matchService.RespondToMatchAsync(userIdInt, response);
                return Ok(new { isMatched });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error liking match {MatchId}", id);
                return StatusCode(500, "An error occurred while liking the match");
            }
        }

        [HttpPost("{id}/dislike")]
        public async Task<ActionResult<bool>> DislikeMatch(int id)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var response = new MatchResponseDto
                {
                    MatchId = id,
                    Like = false
                };

                await _matchService.RespondToMatchAsync(userIdInt, response);
                return Ok(new { success = true });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disliking match {MatchId}", id);
                return StatusCode(500, "An error occurred while disliking the match");
            }
        }

        [HttpGet("remaining")]
        public async Task<ActionResult<int>> GetRemainingMatches()
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                if (userId == null || !int.TryParse(userId.Value, out int userIdInt))
                    return Unauthorized();

                var remaining = await _matchService.GetRemainingMatchesAsync(userIdInt);
                return Ok(new { remaining });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting remaining matches");
                return StatusCode(500, "An error occurred while getting remaining matches");
            }
        }
    }
}
