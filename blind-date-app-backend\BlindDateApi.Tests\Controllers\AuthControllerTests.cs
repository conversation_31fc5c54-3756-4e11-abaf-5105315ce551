using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using BlindDateApi.Controllers;
using BlindDateApi.Data.Context;
using BlindDateApi.Data.Models;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace BlindDateApi.Tests.Controllers
{
    public class AuthControllerTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IRepository<Subscription>> _mockSubscriptionRepository;
        private readonly Mock<IRepository<UserSubscription>> _mockUserSubscriptionRepository;
        private readonly Mock<IJwtService> _mockJwtService;
        private readonly Mock<IPasswordValidationService> _mockPasswordValidationService;
        private readonly Mock<IOnlineStatusService> _mockOnlineStatusService;
        private readonly Mock<IEncryptionService> _mockEncryptionService;
        private readonly Mock<IEmailService> _mockEmailService;
        private readonly Mock<ILogger<AuthController>> _mockLogger;
        private readonly AuthController _controller;

        public AuthControllerTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockSubscriptionRepository = new Mock<IRepository<Subscription>>();
            _mockUserSubscriptionRepository = new Mock<IRepository<UserSubscription>>();
            _mockJwtService = new Mock<IJwtService>();
            _mockPasswordValidationService = new Mock<IPasswordValidationService>();
            _mockOnlineStatusService = new Mock<IOnlineStatusService>();
            _mockEncryptionService = new Mock<IEncryptionService>();
            _mockEmailService = new Mock<IEmailService>();
            _mockLogger = new Mock<ILogger<AuthController>>();

            _controller = new AuthController(
                _mockUserRepository.Object,
                _mockSubscriptionRepository.Object,
                _mockUserSubscriptionRepository.Object,
                _mockJwtService.Object,
                _mockPasswordValidationService.Object,
                _mockOnlineStatusService.Object,
                _mockEncryptionService.Object,
                _mockEmailService.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task Register_ValidData_ReturnsOk()
        {
            // Arrange
            var registerDto = new RegisterDto
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                BirthDate = new DateTime(1990, 1, 1),
                Gender = 1
            };

            _mockUserRepository
                .Setup(repo => repo.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<User, bool>>>(),
                    It.IsAny<Func<IQueryable<User>, Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<User, object>>>()))
                .ReturnsAsync((User)null);

            var subscription = new Subscription
            {
                Id = 1,
                Name = "Free",
                Price = 0,
                ChatsPerDay = 10,
                MaxInterests = 2,
                MaxPictures = 2,
                PictureRevealCondition = "Match"
            };

            _mockSubscriptionRepository
                .Setup(repo => repo.SingleOrDefaultAsync(It.IsAny<Expression<Func<Subscription, bool>>>(), null))
                .ReturnsAsync(subscription);

            _mockPasswordValidationService
                .Setup(service => service.IsValid(It.IsAny<string>()))
                .Returns(true);

            _mockJwtService
                .Setup(service => service.GenerateToken(It.IsAny<User>()))
                .Returns("test-jwt-token");

            // Act
            var result = await _controller.Register(registerDto);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthResponseDto>>(result);
            Assert.Null(actionResult.Value);
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var response = Assert.IsType<AuthResponseDto>(okResult.Value);
            Assert.Equal("<EMAIL>", response.Email);
            Assert.Equal("John", response.FirstName);
            Assert.Equal("Doe", response.LastName);
            Assert.Equal("test-jwt-token", response.Token);
        }

        [Fact]
        public async Task Register_InvalidPassword_ReturnsBadRequest()
        {
            // Arrange
            var registerDto = new RegisterDto
            {
                Email = "<EMAIL>",
                Password = "weak",
                FirstName = "John",
                LastName = "Doe",
                BirthDate = new DateTime(1990, 1, 1),
                Gender = 1
            };

            _mockUserRepository
                .Setup(repo => repo.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<User, bool>>>(),
                    It.IsAny<Func<IQueryable<User>, Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<User, object>>>()))
                .ReturnsAsync((User)null);

            _mockPasswordValidationService
                .Setup(service => service.IsValid("weak"))
                .Returns(false);

            _mockPasswordValidationService
                .Setup(service => service.GetValidationErrorMessage())
                .Returns("Password must be at least 8 characters long and contain at least one lowercase letter, one uppercase letter, one digit, and one special character.");

            // Act
            var result = await _controller.Register(registerDto);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthResponseDto>>(result);
            Assert.Null(actionResult.Value);
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(actionResult.Result);
            Assert.Equal("Password must be at least 8 characters long and contain at least one lowercase letter, one uppercase letter, one digit, and one special character.", badRequestResult.Value);
        }

        [Fact]
        public async Task Register_ExistingEmail_ReturnsBadRequest()
        {
            // Arrange
            var registerDto = new RegisterDto
            {
                Email = "<EMAIL>",
                Password = "Password123!",
                FirstName = "John",
                LastName = "Doe",
                BirthDate = new DateTime(1990, 1, 1),
                Gender = 1
            };

            _mockUserRepository
                .Setup(repo => repo.SingleOrDefaultAsync(
                    It.IsAny<Expression<Func<User, bool>>>(),
                    It.IsAny<Func<IQueryable<User>, Microsoft.EntityFrameworkCore.Query.IIncludableQueryable<User, object>>>()))
                .ReturnsAsync(new User { Email = "<EMAIL>" });

            // Act
            var result = await _controller.Register(registerDto);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthResponseDto>>(result);
            Assert.Null(actionResult.Value);
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(actionResult.Result);
            Assert.Equal("Email already in use", badRequestResult.Value);
        }

        [Fact]
        public async Task Login_ValidCredentials_ReturnsOk()
        {
            // Arrange
            var loginDto = new LoginDto
            {
                Email = "<EMAIL>",
                Password = "Password123!"
            };

            var hashedPassword = BCrypt.Net.BCrypt.HashPassword("Password123!");
            var subscription = new Subscription
            {
                Id = 1,
                Name = "Free",
                Price = 0,
                ChatsPerDay = 10,
                MaxInterests = 2,
                MaxPictures = 2,
                PictureRevealCondition = "Match"
            };

            var userSubscription = new UserSubscription
            {
                Id = 1,
                UserId = 1,
                SubscriptionId = 1,
                Subscription = subscription,
                StartDate = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddDays(30)
            };

            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                PasswordHash = hashedPassword,
                Profile = new Profile
                {
                    FirstName = "John",
                    LastName = "Doe"
                },
                Subscriptions = new[] { userSubscription }
            };

            _mockUserRepository
                .Setup(repo => repo.GetUserByEmailAsync("<EMAIL>"))
                .ReturnsAsync(user);

            _mockUserRepository
                .Setup(repo => repo.GetUserWithProfileAsync(1))
                .ReturnsAsync(user);

            _mockUserRepository
                .Setup(repo => repo.GetUserWithSubscriptionsAsync(1))
                .ReturnsAsync(user);

            _mockJwtService
                .Setup(service => service.GenerateToken(It.IsAny<User>()))
                .Returns("test-jwt-token");

            // Act
            var result = await _controller.Login(loginDto);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthResponseDto>>(result);
            Assert.Null(actionResult.Value);
            var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var response = Assert.IsType<AuthResponseDto>(okResult.Value);
            Assert.Equal("<EMAIL>", response.Email);
            Assert.Equal("John", response.FirstName);
            Assert.Equal("Doe", response.LastName);
            Assert.Equal("test-jwt-token", response.Token);
        }

        [Fact]
        public async Task Login_InvalidEmail_ReturnsUnauthorized()
        {
            // Arrange
            var loginDto = new LoginDto
            {
                Email = "<EMAIL>",
                Password = "Password123!"
            };

            _mockUserRepository
                .Setup(repo => repo.GetUserByEmailAsync("<EMAIL>"))
                .ReturnsAsync((User)null);

            // Act
            var result = await _controller.Login(loginDto);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthResponseDto>>(result);
            Assert.Null(actionResult.Value);
            var unauthorizedResult = Assert.IsType<UnauthorizedObjectResult>(actionResult.Result);
            Assert.Equal(401, unauthorizedResult.StatusCode);
        }

        [Fact]
        public async Task Login_InvalidPassword_ReturnsUnauthorized()
        {
            // Arrange
            var loginDto = new LoginDto
            {
                Email = "<EMAIL>",
                Password = "WrongPassword"
            };

            var hashedPassword = BCrypt.Net.BCrypt.HashPassword("Password123!");
            var user = new User
            {
                Id = 1,
                Email = "<EMAIL>",
                PasswordHash = hashedPassword,
                Profile = new Profile
                {
                    FirstName = "John",
                    LastName = "Doe"
                }
            };

            _mockUserRepository
                .Setup(repo => repo.GetUserByEmailAsync("<EMAIL>"))
                .ReturnsAsync(user);

            _mockUserRepository
                .Setup(repo => repo.GetUserWithProfileAsync(1))
                .ReturnsAsync(user);

            // Act
            var result = await _controller.Login(loginDto);

            // Assert
            var actionResult = Assert.IsType<ActionResult<AuthResponseDto>>(result);
            Assert.Null(actionResult.Value);
            var statusCodeResult = Assert.IsType<ObjectResult>(actionResult.Result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }
    }
}
