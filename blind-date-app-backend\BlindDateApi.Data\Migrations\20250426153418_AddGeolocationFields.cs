﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BlindDateApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddGeolocationFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<double>(
                name: "Latitude",
                table: "Profiles",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<float>(
                name: "LocationAccuracy",
                table: "Profiles",
                type: "real",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LocationUpdatedAt",
                table: "Profiles",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Longitude",
                table: "Profiles",
                type: "float",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaxDistance",
                table: "Profiles",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "Profiles");

            migrationBuilder.DropColumn(
                name: "LocationAccuracy",
                table: "Profiles");

            migrationBuilder.DropColumn(
                name: "LocationUpdatedAt",
                table: "Profiles");

            migrationBuilder.DropColumn(
                name: "Longitude",
                table: "Profiles");

            migrationBuilder.DropColumn(
                name: "MaxDistance",
                table: "Profiles");
        }
    }
}
