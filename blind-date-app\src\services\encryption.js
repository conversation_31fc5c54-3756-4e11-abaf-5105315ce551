
// Helper function to convert base64 to ArrayBuffer
function base64ToArrayBuffer(base64) {
  try {

    const binaryString = window.atob(base64)
    const bytes = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }
    return bytes.buffer
  } catch (error) {
    console.error('Error converting base64 to ArrayBuffer:', error)
    throw new Error('Invalid base64 string')
  }
}

// Helper function to convert ArrayBuffer to base64
function arrayBufferToBase64(buffer) {
  const binary = String.fromCharCode.apply(null, new Uint8Array(buffer))
  return window.btoa(binary)
}

// Helper function to convert string to ArrayBuffer
function stringToArrayBuffer(str) {
  const encoder = new TextEncoder()
  return encoder.encode(str).buffer
}

// Helper function to convert ArrayBuffer to string
function arrayBufferToString(buffer) {
  const decoder = new TextDecoder()
  return decoder.decode(buffer)
}

// Generate a random IV (Initialization Vector) for encryption
function generateIV() {
  const iv = new Uint8Array(16)
  crypto.getRandomValues(iv)
  return iv
}

// Password-based encryption for private key
export async function encryptPrivateKey(privateKey, password) {
  try {
    // Generate a random salt
    const salt = crypto.getRandomValues(new Uint8Array(16))

    // Derive the key from the password
    let passValue = password.value ? password.value : password

    // Derive a key from the password
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      stringToArrayBuffer(passValue),
      { name: 'PBKDF2' },
      false,
      ['deriveBits', 'deriveKey']
    )

    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    )

    // Generate a random IV
    const iv = generateIV()

    // Encrypt the private key
    const encryptedData = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      stringToArrayBuffer(privateKey)
    )

    // Return the encrypted key with salt and IV
    return {
      encryptedPrivateKey: arrayBufferToBase64(encryptedData),
      salt: arrayBufferToBase64(salt),
      iv: arrayBufferToBase64(iv.buffer)
    }
  } catch (error) {
    console.error('Error encrypting private key:', error)
    throw new Error('Failed to encrypt private key')
  }
}

// Password-based decryption for private key
export async function decryptPrivateKey(encryptedData, password) {
  try {
    const { encryptedPrivateKey, salt, iv } = encryptedData

    // Derive the key from the password
    let passValue = password.value ? password.value : password

    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      stringToArrayBuffer(passValue),
      { name: 'PBKDF2' },
      false,
      ['deriveBits', 'deriveKey']
    )

    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: base64ToArrayBuffer(salt),
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    )

    // Decrypt the private key
    const decryptedData = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv: base64ToArrayBuffer(iv) },
      key,
      base64ToArrayBuffer(encryptedPrivateKey)
    )

    // Return the decrypted private key
    return arrayBufferToString(decryptedData)
  } catch (error) {
    console.error('Error decrypting private key:', error)
    throw new Error('Failed to decrypt private key')
  }
}

// Encrypt with recipient's public key using proper RSA-OAEP
async function asymmetricEncrypt(text, publicKeyBase64) {
  try {
    // Convert the base64 public key to a CryptoKey object
    let publicKeyCrypto;
    try {
      const publicKeyBuffer = base64ToArrayBuffer(publicKeyBase64);

      // Try to import the key with SHA-256
      try {
        publicKeyCrypto = await window.crypto.subtle.importKey(
          'spki',
          publicKeyBuffer,
          {
            name: 'RSA-OAEP',
            hash: { name: 'SHA-256' }
          },
          false,
          ['encrypt']
        );
      } catch (sha256Error) {
        console.warn('Failed to import key with SHA-256, trying SHA-1:', sha256Error);

        // Try with SHA-1 as fallback
        publicKeyCrypto = await window.crypto.subtle.importKey(
          'spki',
          publicKeyBuffer,
          {
            name: 'RSA-OAEP',
            hash: { name: 'SHA-1' }
          },
          false,
          ['encrypt']
        );
      }
    } catch (importError) {
      throw new Error('Invalid public key format');
    }

    // Generate a random symmetric key for AES-GCM
    const symmetricKey = await window.crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true,
      ['encrypt', 'decrypt']
    );

    // Generate a random IV for AES-GCM
    const iv = generateIV();
    const ivBase64 = arrayBufferToBase64(iv.buffer);

    // Encrypt the message with the symmetric key
    const textEncoder = new TextEncoder();
    const messageBuffer = textEncoder.encode(text);
    const encryptedMessage = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv
      },
      symmetricKey,
      messageBuffer
    );

    // Export the symmetric key
    const exportedSymmetricKey = await window.crypto.subtle.exportKey('raw', symmetricKey);

    // Encrypt the symmetric key with the recipient's public key
    const encryptedSymmetricKey = await window.crypto.subtle.encrypt(
      {
        name: 'RSA-OAEP'
      },
      publicKeyCrypto,
      exportedSymmetricKey
    );

    // Base64 encode the encrypted message and key
    const encryptedMessageBase64 = arrayBufferToBase64(encryptedMessage);
    const encryptedKeyBase64 = arrayBufferToBase64(encryptedSymmetricKey);

    // Return the encrypted message, encrypted key, and IV
    return {
      encryptedText: encryptedMessageBase64,
      encryptedKey: encryptedKeyBase64,
      iv: ivBase64
    };
  } catch (error) {
    console.error('Asymmetric encryption error:', error);
    // Fallback to simple base64 encoding with a random IV
    const fallbackIV = arrayBufferToBase64(generateIV().buffer);
    console.log('Using fallback encryption. IV:', fallbackIV.substring(0, 10) + '...');

    return {
      encryptedText: btoa(text),
      encryptedKey: '',
      iv: fallbackIV
    };
  }
}

// Decrypt with your private key using proper RSA-OAEP
async function asymmetricDecrypt(encryptedData, privateKeyBase64) {
  try {
    console.log('Decrypting message with private key:', privateKeyBase64.substring(0, 10) + '...');

    // Extract the encrypted text, encrypted key, and IV
    if (!encryptedData || typeof encryptedData !== 'object') {
      console.error('Invalid encrypted data format:', encryptedData);
      throw new Error('Invalid encrypted data format');
    }

    const { encryptedText, encryptedKey, iv: ivBase64 } = encryptedData;

    if (!encryptedText) {
      console.error('Missing encrypted text');
      throw new Error('Missing encrypted text');
    }

    if (!ivBase64) {
      console.error('Missing IV');
      throw new Error('Missing IV');
    }

    // If there's no encrypted key, this might be a fallback encryption
    if (!encryptedKey) {
      console.warn('No encrypted key found, attempting fallback decryption');
      return atob(encryptedText);
    }

    // Convert the base64 private key to a CryptoKey object
    let privateKeyCrypto;
    try {
      const privateKeyBuffer = base64ToArrayBuffer(privateKeyBase64);

      // Try with SHA-256 first
      try {
        privateKeyCrypto = await window.crypto.subtle.importKey(
          'pkcs8',
          privateKeyBuffer,
          {
            name: 'RSA-OAEP',
            hash: { name: 'SHA-256' }
          },
          false,
          ['decrypt']
        );
      } catch (sha256Error) {
        console.warn('Failed to import private key with SHA-256, trying SHA-1:', sha256Error);

        // Try with SHA-1 as fallback
        privateKeyCrypto = await window.crypto.subtle.importKey(
          'pkcs8',
          privateKeyBuffer,
          {
            name: 'RSA-OAEP',
            hash: { name: 'SHA-1' }
          },
          false,
          ['decrypt']
        );
      }
    } catch (importError) {
      console.error('Failed to import private key:', importError);
      throw new Error('Invalid private key format');
    }

    // Decode the encrypted key and message from base64
    const encryptedKeyBuffer = base64ToArrayBuffer(encryptedKey);
    const encryptedMessageBuffer = base64ToArrayBuffer(encryptedText);
    const iv = new Uint8Array(base64ToArrayBuffer(ivBase64));

    // Decrypt the symmetric key with the private key
    const symmetricKeyBuffer = await window.crypto.subtle.decrypt(
      {
        name: 'RSA-OAEP'
      },
      privateKeyCrypto,
      encryptedKeyBuffer
    );

    // Import the symmetric key
    const symmetricKey = await window.crypto.subtle.importKey(
      'raw',
      symmetricKeyBuffer,
      {
        name: 'AES-GCM',
        length: 256
      },
      false,
      ['decrypt']
    );

    // Decrypt the message with the symmetric key
    const decryptedMessageBuffer = await window.crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv
      },
      symmetricKey,
      encryptedMessageBuffer
    );

    // Convert the decrypted message to a string
    const textDecoder = new TextDecoder();
    const decryptedMessage = textDecoder.decode(decryptedMessageBuffer);

    console.log('Decryption successful');
    return decryptedMessage;
  } catch (error) {
    console.error('Asymmetric decryption error:', error);
    // Try simple base64 decoding as fallback
    try {
      if (typeof encryptedData === 'string') {
        return atob(encryptedData);
      } else if (encryptedData && encryptedData.encryptedText) {
        return atob(encryptedData.encryptedText);
      }
      console.log('Returning as string');
      return String(encryptedData); // Return as string if all else fails
    } catch (b64Error) {
      console.error('Fallback decryption failed:', b64Error);
      return '[Decryption failed]'; // Return an error message if all decryption fails
    }
  }
}

// Generate a proper RSA key pair
export async function generateKeyPair() {
  try {
    console.log('Generating new RSA key pair...');

    // Generate a new RSA key pair
    const keyPair = await window.crypto.subtle.generateKey(
      {
        name: 'RSA-OAEP',
        modulusLength: 2048,
        publicExponent: new Uint8Array([1, 0, 1]), // 65537
        hash: { name: 'SHA-256' }
      },
      true, // extractable
      ['encrypt', 'decrypt']
    );

    // Export the keys to the expected format
    const publicKeyBuffer = await window.crypto.subtle.exportKey('spki', keyPair.publicKey);
    const privateKeyBuffer = await window.crypto.subtle.exportKey('pkcs8', keyPair.privateKey);

    // Convert to base64
    const publicKey = arrayBufferToBase64(publicKeyBuffer);
    const privateKey = arrayBufferToBase64(privateKeyBuffer);

    console.log('Generated new RSA key pair successfully');
    return { privateKey, publicKey };
  } catch (error) {
    console.error('RSA key generation error:', error);

    // Try with SHA-1 as fallback
    try {
      console.warn('Retrying key generation with SHA-1...');

      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'RSA-OAEP',
          modulusLength: 2048,
          publicExponent: new Uint8Array([1, 0, 1]), // 65537
          hash: { name: 'SHA-1' }
        },
        true, // extractable
        ['encrypt', 'decrypt']
      );

      // Export the keys
      const publicKeyBuffer = await window.crypto.subtle.exportKey('spki', keyPair.publicKey);
      const privateKeyBuffer = await window.crypto.subtle.exportKey('pkcs8', keyPair.privateKey);

      // Convert to base64
      const publicKey = arrayBufferToBase64(publicKeyBuffer);
      const privateKey = arrayBufferToBase64(privateKeyBuffer);

      console.log('Generated new RSA key pair with SHA-1 successfully');
      return { privateKey, publicKey };
    } catch (fallbackError) {
      console.error('Fallback RSA key generation failed:', fallbackError);

      // Last resort fallback to simple key generation
      console.warn('Falling back to simple key generation');
      const privateKey = Math.random().toString(36).substring(2, 15) +
                       Math.random().toString(36).substring(2, 15) +
                       Date.now().toString(36);

      // Create a different but related public key
      const publicKey = privateKey.split('').map(c => {
        const code = c.charCodeAt(0);
        return String.fromCharCode((code + 7) % 128);
      }).join('');

      return { privateKey, publicKey };
    }
  }
}

// Encrypt a message with recipient's public key
export async function encryptMessage(message, publicKey) {
  try {
    if (!message) {
      console.error('No message provided for encryption')
      throw new Error('No message provided for encryption')
    }

    if (!publicKey) {
      console.warn('No public key provided for encryption, using default')
      publicKey = 'default-key'
    }

    // Use asymmetric encryption
    const result = await asymmetricEncrypt(message, publicKey)

    return result
  } catch (error) {
    console.error('Encryption error:', error)
    // Fallback to a very simple encoding if encryption fails
    const iv = arrayBufferToBase64(generateIV().buffer)
    return {
      encryptedText: btoa(message),
      encryptedKey: '',
      iv: iv
    }
  }
}

// Decrypt a message with your private key
export async function decryptMessage(encryptedData, privateKey) {
  try {
    if (!encryptedData) {
      console.error('No encrypted data provided for decryption')
      throw new Error('No encrypted data provided for decryption')
    }

    if (!privateKey) {
      console.warn('No private key provided for decryption, using default')
      privateKey = 'default-key'
    }

    // Handle both new format (object with encryptedText and iv) and old format (string)
    if (typeof encryptedData === 'string') {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(encryptedData)
        if (parsed.encryptedText && parsed.iv) {
          encryptedData = parsed
        } else {
          // If not a valid format, create a default object
          const iv = arrayBufferToBase64(generateIV().buffer)
          encryptedData = {
            encryptedText: encryptedData,
            iv: iv
          }
        }
      } catch (e) {
        // Not JSON, use as-is with a default IV
        const iv = arrayBufferToBase64(generateIV().buffer)
        encryptedData = {
          encryptedText: encryptedData,
          iv: iv
        }
      }
    }

    // Check if we have an encrypted key (asymmetric encryption)
    if (encryptedData.encryptedKey) {
      return await asymmetricDecrypt(encryptedData, privateKey)
    } else {
      try {
        return atob(encryptedData.encryptedText)
      } catch (error) {
        return '[Decryption failed]'
      }
    }
  } catch (error) {
    console.error('Decryption error:', error)

    // Try to decode as base64 as a fallback
    try {
      if (typeof encryptedData === 'string') {
        return atob(encryptedData)
      } else if (encryptedData && encryptedData.encryptedText) {
        return atob(encryptedData.encryptedText)
      }
      return String(encryptedData)
    } catch (b64Error) {
      console.error('Emergency fallback decryption failed:', b64Error)
      // If all else fails, return the encrypted message
      return '[Decryption failed]'
    }
  }
}

// Check if a public key is valid by attempting to import it
export async function isValidPublicKey(publicKey) {
  try {
    if (!publicKey) return false;

    // Try to import the key to validate it
    try {
      const publicKeyBuffer = base64ToArrayBuffer(publicKey);

      // Try with SHA-256 first
      try {
        await window.crypto.subtle.importKey(
          'spki',
          publicKeyBuffer,
          {
            name: 'RSA-OAEP',
            hash: { name: 'SHA-256' }
          },
          false,
          ['encrypt']
        );
        return true;
      } catch (sha256Error) {
        // Try with SHA-1 as fallback
        try {
          await window.crypto.subtle.importKey(
            'spki',
            publicKeyBuffer,
            {
              name: 'RSA-OAEP',
              hash: { name: 'SHA-1' }
            },
            false,
            ['encrypt']
          );
          return true;
        } catch (sha1Error) {
          console.error('Invalid public key (tried both SHA-256 and SHA-1):', sha1Error);
          return false;
        }
      }
    } catch (error) {
      console.error('Invalid public key format:', error);
      return false;
    }
  } catch (error) {
    console.error('Error validating public key:', error);
    return false;
  }
}

// Check if a private key is valid by attempting to import it
export async function isValidPrivateKey(privateKey) {
  try {
    if (!privateKey) return false;

    // Try to import the key to validate it
    try {
      const privateKeyBuffer = base64ToArrayBuffer(privateKey);

      // Try with SHA-256 first
      try {
        await window.crypto.subtle.importKey(
          'pkcs8',
          privateKeyBuffer,
          {
            name: 'RSA-OAEP',
            hash: { name: 'SHA-256' }
          },
          false,
          ['decrypt']
        );
        return true;
      } catch (sha256Error) {
        // Try with SHA-1 as fallback
        try {
          await window.crypto.subtle.importKey(
            'pkcs8',
            privateKeyBuffer,
            {
              name: 'RSA-OAEP',
              hash: { name: 'SHA-1' }
            },
            false,
            ['decrypt']
          );
          return true;
        } catch (sha1Error) {
          console.error('Invalid private key (tried both SHA-256 and SHA-1):', sha1Error);
          return false;
        }
      }
    } catch (error) {
      console.error('Invalid private key format:', error);
      return false;
    }
  } catch (error) {
    console.error('Error validating private key:', error);
    return false;
  }
}

export default {
  encryptMessage,
  decryptMessage,
  generateKeyPair,
  encryptPrivateKey,
  decryptPrivateKey,
  isValidPublicKey,
  isValidPrivateKey
}
