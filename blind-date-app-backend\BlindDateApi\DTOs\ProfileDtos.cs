using System;
using System.Collections.Generic;

using Microsoft.AspNetCore.Http;

namespace BlindDateApi.DTOs
{
    public class ProfilePictureUploadDto
    {
        public IFormFile File { get; set; }
    }

    public class ProfilePictureDto
    {
        public string Url { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class ProfileDto
    {
        public int Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public DateTime BirthDate { get; set; }
        public int Age { get; set; }
        public string Bio { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public float? LocationAccuracy { get; set; }
        public DateTime? LocationUpdatedAt { get; set; }
        public int MaxDistance { get; set; }
        public int Gender { get; set; }
        public int Preference { get; set; }
        public int MinAgePreference { get; set; }
        public int MaxAgePreference { get; set; }
        public List<string> Interests { get; set; } = new List<string>();
        public List<string> Pictures { get; set; } = new List<string>();
    }

    public class UpdateProfileDto
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Bio { get; set; }
        public string? Location { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public float? LocationAccuracy { get; set; }
        public int? MaxDistance { get; set; }
        public int? Gender { get; set; }
        public int? Preference { get; set; }
        public int? MinAgePreference { get; set; }
        public int? MaxAgePreference { get; set; }
        public List<string>? Interests { get; set; }
        public List<string>? Pictures { get; set; }
    }
}
