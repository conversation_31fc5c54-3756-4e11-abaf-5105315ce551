using BlindDateApi.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace BlindDateApi.Data.Context
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Profile> Profiles { get; set; }
        public DbSet<Match> Matches { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<Subscription> Subscriptions { get; set; }
        public DbSet<UserSubscription> UserSubscriptions { get; set; }
        public DbSet<Interest> Interests { get; set; }
        public DbSet<ProfileInterest> ProfileInterests { get; set; }
        public DbSet<Conversation> Conversations { get; set; }
        public DbSet<ConversationParticipant> ConversationParticipants { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships and constraints
            modelBuilder.Entity<User>()
                .HasOne(u => u.Profile)
                .WithOne(p => p.User)
                .HasForeignKey<Profile>(p => p.UserId);

            modelBuilder.Entity<Match>()
                .HasOne(m => m.User1)
                .WithMany(u => u.InitiatedMatches)
                .HasForeignKey(m => m.User1Id)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Match>()
                .HasOne(m => m.User2)
                .WithMany(u => u.ReceivedMatches)
                .HasForeignKey(m => m.User2Id)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Message>()
                .HasOne(m => m.Match)
                .WithMany(m => m.Messages)
                .HasForeignKey(m => m.MatchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Message>()
                .HasOne(m => m.Conversation)
                .WithMany(c => c.Messages)
                .HasForeignKey(m => m.ConversationId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Message>()
                .HasOne(m => m.Sender)
                .WithMany(u => u.SentMessages)
                .HasForeignKey(m => m.SenderId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<UserSubscription>()
                .HasOne(us => us.User)
                .WithMany(u => u.Subscriptions)
                .HasForeignKey(us => us.UserId);

            modelBuilder.Entity<UserSubscription>()
                .HasOne(us => us.Subscription)
                .WithMany(s => s.Users)
                .HasForeignKey(us => us.SubscriptionId);

            // Configure ConversationParticipant relationships
            modelBuilder.Entity<ConversationParticipant>()
                .HasOne(cp => cp.Conversation)
                .WithMany(c => c.Participants)
                .HasForeignKey(cp => cp.ConversationId);

            modelBuilder.Entity<ConversationParticipant>()
                .HasOne(cp => cp.User)
                .WithMany()
                .HasForeignKey(cp => cp.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure ProfileInterest relationships
            modelBuilder.Entity<ProfileInterest>()
                .HasOne(pi => pi.Profile)
                .WithMany(p => p.ProfileInterests)
                .HasForeignKey(pi => pi.ProfileId);

            modelBuilder.Entity<ProfileInterest>()
                .HasOne(pi => pi.Interest)
                .WithMany(i => i.ProfileInterests)
                .HasForeignKey(pi => pi.InterestId);

            // Seed data for subscription plans
            modelBuilder.Entity<Subscription>().HasData(
                new Subscription
                {
                    Id = 1,
                    Name = "Free",
                    Price = 0,
                    ChatsPerDay = 10,
                    MaxInterests = 2,
                    MaxPictures = 2,
                    PictureRevealCondition = "Match"
                },
                new Subscription
                {
                    Id = 2,
                    Name = "Premium",
                    Price = 4.99m,
                    ChatsPerDay = 30,
                    MaxInterests = 4,
                    MaxPictures = 4,
                    PictureRevealCondition = "10 Messages"
                },
                new Subscription
                {
                    Id = 3,
                    Name = "VIP",
                    Price = 9.99m,
                    ChatsPerDay = -1, // Unlimited
                    MaxInterests = 6,
                    MaxPictures = 6,
                    PictureRevealCondition = "Immediate"
                }
            );

            // Seed data for predefined interests
            modelBuilder.Entity<Interest>().HasData(
                new Interest { Id = 1, Name = "Music", Description = "Music and concerts" },
                new Interest { Id = 2, Name = "Movies", Description = "Films and cinema" },
                new Interest { Id = 3, Name = "Books", Description = "Reading and literature" },
                new Interest { Id = 4, Name = "Sports", Description = "Sports and athletics" },
                new Interest { Id = 5, Name = "Travel", Description = "Traveling and exploring" },
                new Interest { Id = 6, Name = "Food", Description = "Cooking and dining" },
                new Interest { Id = 7, Name = "Art", Description = "Visual arts and museums" },
                new Interest { Id = 8, Name = "Photography", Description = "Taking and viewing photos" },
                new Interest { Id = 9, Name = "Technology", Description = "Tech and gadgets" },
                new Interest { Id = 10, Name = "Gaming", Description = "Video and board games" },
                new Interest { Id = 11, Name = "Fitness", Description = "Exercise and wellness" },
                new Interest { Id = 12, Name = "Fashion", Description = "Clothing and style" },
                new Interest { Id = 13, Name = "Nature", Description = "Outdoors and wildlife" },
                new Interest { Id = 14, Name = "Pets", Description = "Animals and pet care" },
                new Interest { Id = 15, Name = "Cooking", Description = "Food preparation" },
                new Interest { Id = 16, Name = "Dancing", Description = "Dance and movement" }
            );
        }
    }
}
