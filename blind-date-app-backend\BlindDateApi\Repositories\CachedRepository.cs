using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using BlindDateApi.Data.Repositories;
using BlindDateApi.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;

namespace BlindDateApi.Repositories
{
    public class CachedRepository<T> : IRepository<T> where T : class
    {
        private readonly IRepository<T> _repository;
        private readonly ICacheService _cacheService;
        private readonly ILogger<CachedRepository<T>> _logger;

        // Cache keys
        private readonly string _cacheKeyPrefix;
        private const string CacheKeyById = "{0}_Id_{1}";
        private const string CacheKeyAll = "{0}_All";

        // Cache expiration times
        private readonly TimeSpan _cacheTime = TimeSpan.FromMinutes(5);

        public CachedRepository(
            IRepository<T> repository,
            ICacheService cacheService,
            ILogger<CachedRepository<T>> logger)
        {
            _repository = repository;
            _cacheService = cacheService;
            _logger = logger;

            // Use the type name as the cache key prefix
            _cacheKeyPrefix = typeof(T).Name;
        }

        public async Task<IEnumerable<T>> GetAllAsync(Expression<Func<T, bool>>? predicate = null, Func<IQueryable<T>, IIncludableQueryable<T, object>>? include = null)
        {
            // If no predicate and no include, we can use the cache
            if (predicate == null && include == null)
            {
                var cacheKey = string.Format(CacheKeyAll, _cacheKeyPrefix);
                var cachedItems = await _cacheService.GetAsync<IEnumerable<T>>(cacheKey);

                if (cachedItems != null)
                {
                    _logger.LogDebug("All {EntityType} retrieved from cache", _cacheKeyPrefix);
                    return cachedItems;
                }

                var items = await _repository.GetAllAsync();
                await _cacheService.SetAsync(cacheKey, items, _cacheTime);
                _logger.LogDebug("All {EntityType} added to cache", _cacheKeyPrefix);

                return items;
            }

            // For filtered or included queries, we don't cache as they can vary widely
            return await _repository.GetAllAsync(predicate, include);
        }

        public async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, Func<IQueryable<T>, IIncludableQueryable<T, object>>? include = null)
        {
            // For predicate-based queries, we don't cache as the predicates can vary widely
            return await _repository.FindAsync(predicate, include);
        }

        public async Task<T> GetByIdAsync(int id)
        {
            var cacheKey = string.Format(CacheKeyById, _cacheKeyPrefix, id);
            var cachedItem = await _cacheService.GetAsync<T>(cacheKey);

            if (cachedItem != null)
            {
                _logger.LogDebug("{EntityType} with ID {Id} retrieved from cache", _cacheKeyPrefix, id);
                return cachedItem;
            }

            var item = await _repository.GetByIdAsync(id);
            if (item != null)
            {
                await _cacheService.SetAsync(cacheKey, item, _cacheTime);
                _logger.LogDebug("{EntityType} with ID {Id} added to cache", _cacheKeyPrefix, id);
            }

            return item;
        }

        public async Task<T?> SingleOrDefaultAsync(Expression<Func<T, bool>> predicate, Func<IQueryable<T>, IIncludableQueryable<T, object>>? include = null)
        {
            // For predicate-based queries, we don't cache as the predicates can vary widely
            return await _repository.SingleOrDefaultAsync(predicate, include);
        }

        public async Task AddAsync(T entity)
        {
            await _repository.AddAsync(entity);

            // Invalidate the "all" cache
            var allCacheKey = string.Format(CacheKeyAll, _cacheKeyPrefix);
            await _cacheService.RemoveAsync(allCacheKey);
        }

        public async Task AddRangeAsync(IEnumerable<T> entities)
        {
            await _repository.AddRangeAsync(entities);

            // Invalidate the "all" cache
            var allCacheKey = string.Format(CacheKeyAll, _cacheKeyPrefix);
            await _cacheService.RemoveAsync(allCacheKey);
        }

        public void Update(T entity)
        {
            _repository.Update(entity);

            // Get the ID property using reflection
            var idProperty = typeof(T).GetProperty("Id");
            if (idProperty != null)
            {
                var id = idProperty.GetValue(entity);
                if (id != null)
                {
                    // Invalidate the entity cache
                    var cacheKey = string.Format(CacheKeyById, _cacheKeyPrefix, id);
                    _cacheService.Remove(cacheKey);
                }
            }

            // Invalidate the "all" cache
            var allCacheKey = string.Format(CacheKeyAll, _cacheKeyPrefix);
            _cacheService.Remove(allCacheKey);
        }

        public void Remove(T entity)
        {
            _repository.Remove(entity);

            // Get the ID property using reflection
            var idProperty = typeof(T).GetProperty("Id");
            if (idProperty != null)
            {
                var id = idProperty.GetValue(entity);
                if (id != null)
                {
                    // Invalidate the entity cache
                    var cacheKey = string.Format(CacheKeyById, _cacheKeyPrefix, id);
                    _cacheService.Remove(cacheKey);
                }
            }

            // Invalidate the "all" cache
            var allCacheKey = string.Format(CacheKeyAll, _cacheKeyPrefix);
            _cacheService.Remove(allCacheKey);
        }

        public void RemoveRange(IEnumerable<T> entities)
        {
            _repository.RemoveRange(entities);

            // Invalidate entity caches
            foreach (var entity in entities)
            {
                var idProperty = typeof(T).GetProperty("Id");
                if (idProperty != null)
                {
                    var id = idProperty.GetValue(entity);
                    if (id != null)
                    {
                        // Invalidate the entity cache
                        var cacheKey = string.Format(CacheKeyById, _cacheKeyPrefix, id);
                        _cacheService.Remove(cacheKey);
                    }
                }
            }

            // Invalidate the "all" cache
            var allCacheKey = string.Format(CacheKeyAll, _cacheKeyPrefix);
            _cacheService.Remove(allCacheKey);
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var result = await _repository.DeleteAsync(id);

            if (result)
            {
                // Invalidate the entity cache
                var cacheKey = string.Format(CacheKeyById, _cacheKeyPrefix, id);
                await _cacheService.RemoveAsync(cacheKey);

                // Invalidate the "all" cache
                var allCacheKey = string.Format(CacheKeyAll, _cacheKeyPrefix);
                await _cacheService.RemoveAsync(allCacheKey);
            }

            return result;
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _repository.SaveChangesAsync();
        }

        public async Task<IDbContextTransaction> BeginTransactionAsync()
        {
            return await _repository.BeginTransactionAsync();
        }
    }
}
