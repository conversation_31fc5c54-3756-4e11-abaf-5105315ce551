using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using BlindDateApi.Data.Repositories;
using BlindDateApi.DTOs;
using BlindDateApi.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Match = BlindDateApi.Data.Models.Match;
using User = BlindDateApi.Data.Models.User;
using Profile = BlindDateApi.Data.Models.Profile;

namespace BlindDateApi.Tests.Services
{
    public class MatchServiceTests
    {
        private readonly Mock<IRepository<Match>> _mockMatchRepository;
        private readonly Mock<IRepository<Profile>> _mockProfileRepository;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<ICacheService> _mockCacheService;
        private readonly Mock<IOnlineStatusService> _mockOnlineStatusService;
        private readonly Mock<IGeoLocationService> _mockGeoLocationService;
        private readonly Mock<ISearchingUsersService> _mockSearchingUserService;
        private readonly Mock<ILogger<MatchService>> _mockLogger;
        private readonly MatchService _matchService;

        public MatchServiceTests()
        {
            _mockMatchRepository = new Mock<IRepository<Match>>();
            _mockProfileRepository = new Mock<IRepository<Profile>>();
            _mockUserRepository = new Mock<IUserRepository>();
            _mockCacheService = new Mock<ICacheService>();
            _mockOnlineStatusService = new Mock<IOnlineStatusService>();
            _mockGeoLocationService = new Mock<IGeoLocationService>();
            _mockSearchingUserService = new Mock<ISearchingUsersService>();
            _mockLogger = new Mock<ILogger<MatchService>>();

            // Setup basic mocks
            _mockMatchRepository.Setup(r => r.SaveChangesAsync())
                .ReturnsAsync(1);

            // Setup GeoLocationService mock
            _mockGeoLocationService.Setup(g => g.CalculateDistance(
                It.IsAny<double>(), It.IsAny<double>(),
                It.IsAny<double>(), It.IsAny<double>()))
                .Returns(10.0); // Default distance of 10km

            _mockGeoLocationService.Setup(g => g.IsWithinDistance(
                It.IsAny<double>(), It.IsAny<double>(),
                It.IsAny<double>(), It.IsAny<double>(),
                It.IsAny<int>()))
                .Returns(true); // Default to within distance

            _matchService = new MatchService(
                _mockMatchRepository.Object,
                _mockProfileRepository.Object,
                _mockUserRepository.Object,
                _mockCacheService.Object,
                _mockOnlineStatusService.Object,
                _mockGeoLocationService.Object,
                _mockSearchingUserService.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task GetMatchesAsync_ReturnsEmptyList_WhenNoMatches()
        {
            // Arrange
            var userId = 1;
            _mockMatchRepository.Setup(r => r.GetAllAsync(
                It.IsAny<Expression<Func<Match, bool>>>(),
                It.IsAny<Func<IQueryable<Match>, IIncludableQueryable<Match, object>>>()
            )).ReturnsAsync(new List<Match>());

            // Act
            var result = await _matchService.GetMatchesAsync(userId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task FindMatchAsync_ReturnsNull_WhenNoEligibleProfiles()
        {
            // Arrange
            var userId = 1;
            var user = new User
            {
                Id = userId,
                Profile = new Profile
                {
                    FirstName = "Test",
                    LastName = "User",
                    BirthDate = DateTime.UtcNow.AddYears(-25),
                    MinAgePreference = 20,
                    MaxAgePreference = 30,
                    Preference = 2 // Female
                }
            };

            _mockUserRepository.Setup(r => r.GetUserWithProfileAsync(userId))
                .ReturnsAsync(user);

            // Setup for HasReachedMatchLimitAsync to return false
            _mockCacheService.Setup(c => c.GetAsync<int?>(It.IsAny<string>()))
                .ReturnsAsync(10); // Return 10 remaining matches

            // Setup for GetUserWithSubscriptionsAsync
            var subscription = new BlindDateApi.Data.Models.Subscription
            {
                Id = 1,
                ChatsPerDay = 10
            };

            var userSubscription = new BlindDateApi.Data.Models.UserSubscription
            {
                Id = 1,
                UserId = userId,
                SubscriptionId = 1,
                Subscription = subscription,
                StartDate = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddYears(1)
            };

            var userWithSubscription = new User
            {
                Id = userId,
                Subscriptions = new List<BlindDateApi.Data.Models.UserSubscription> { userSubscription }
            };

            _mockUserRepository.Setup(r => r.GetUserWithSubscriptionsAsync(userId))
                .ReturnsAsync(userWithSubscription);

            _mockProfileRepository.Setup(r => r.GetAllAsync(
                It.IsAny<Expression<Func<Profile, bool>>>(),
                It.IsAny<Func<IQueryable<Profile>, IIncludableQueryable<Profile, object>>>()
            )).ReturnsAsync(new List<Profile>());

            // Act
            var result = await _matchService.FindMatchAsync(userId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task RespondToMatchAsync_ReturnsTrue_WhenMatchExists()
        {
            // Arrange
            var userId = 1;
            var matchId = 1;
            var match = new Match
            {
                Id = matchId,
                User1Id = userId,
                User2Id = 2,
                Status = 0, // Pending
                User2Response = true // Other user already liked
            };

            _mockMatchRepository.Setup(r => r.GetByIdAsync(matchId))
                .ReturnsAsync(match);

            var response = new MatchResponseDto
            {
                MatchId = matchId,
                Like = true
            };

            // Act
            var result = await _matchService.RespondToMatchAsync(userId, response);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetRemainingMatchesAsync_ReturnsZero_WhenNoSubscription()
        {
            // Arrange
            var userId = 1;
            _mockUserRepository.Setup(r => r.GetUserWithSubscriptionsAsync(userId))
                .ReturnsAsync((User)null);

            // Act
            var result = await _matchService.GetRemainingMatchesAsync(userId);

            // Assert
            Assert.Equal(0, result);
        }
    }
}
