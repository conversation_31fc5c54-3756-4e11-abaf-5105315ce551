import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Welcome',
    component: () => import('../views/WelcomeView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/RegisterView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/auth/callback',
    name: 'AuthCallback',
    component: () => import('../views/AuthCallbackView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('../views/ForgotPasswordView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: () => import('../views/ResetPasswordView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/profile-setup',
    name: 'ProfileSetup',
    component: () => import('../views/ProfileSetupView.vue'),
    meta: { requiresAuth: true, allowNewRegistration: true }
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('../views/HomeView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/chat/:id',
    name: 'Chat',
    component: () => import('../views/ChatView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/ProfileView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/payments',
    name: 'Payments',
    component: () => import('../views/PaymentsView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/matches',
    name: 'Matches',
    component: () => import('../views/MatchesView.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// Navigation guard for authentication
router.beforeEach((to, from, next) => {

  // Always allow access to login, register, auth callback, and password reset pages
  if (to.path === '/login' ||
      to.path === '/register' ||
      to.path.startsWith('/auth/callback') ||
      to.path === '/forgot-password' ||
      to.path === '/reset-password') {
    return next()
  }

  // Check authentication status
  const userJson = localStorage.getItem('user')
  const isAuthenticated = userJson !== null

  // If not authenticated and route requires auth, redirect to login
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  if (requiresAuth && !isAuthenticated) {
    return next('/login')
  }

  // If authenticated, get user data
  if (isAuthenticated) {
    try {
      const user = JSON.parse(userJson)

      // If user is trying to access the welcome page, redirect to home
      if (to.path === '/') {
        return next('/home')
      }

      // If profile is not completed and not going to profile setup, redirect to profile setup
      const isProfileCompleted = user.profileCompleted === true
      if (!isProfileCompleted && to.path !== '/profile-setup') {
        return next('/profile-setup')
      }
    } catch (error) {
      // If there's an error parsing user data, clear it and redirect to login
      localStorage.removeItem('user')
      return next('/login')
    }
  }

  // Allow navigation for all other cases
  return next()
})

export default router
